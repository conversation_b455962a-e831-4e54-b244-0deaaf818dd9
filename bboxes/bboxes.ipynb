{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>taskname</th>\n", "      <th>taskid</th>\n", "      <th>imagename</th>\n", "      <th>imageid</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>w</th>\n", "      <th>h</th>\n", "      <th>label</th>\n", "      <th>blank</th>\n", "      <th>annotator</th>\n", "      <th>datetime</th>\n", "      <th>thingy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6351.043017</td>\n", "      <td>1166.623453</td>\n", "      <td>60.223925</td>\n", "      <td>46.458456</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1.697720e+12</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6482.702368</td>\n", "      <td>1211.366282</td>\n", "      <td>41.640716</td>\n", "      <td>35.962437</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1.697720e+12</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6102.257645</td>\n", "      <td>1084.551374</td>\n", "      <td>43.533476</td>\n", "      <td>28.391397</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1.697720e+12</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6230.965313</td>\n", "      <td>1126.192090</td>\n", "      <td>39.747956</td>\n", "      <td>26.498637</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1.697720e+12</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6405.099216</td>\n", "      <td>1175.403845</td>\n", "      <td>-39.747956</td>\n", "      <td>30.284157</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1.697720e+12</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        taskname                    taskid  \\\n", "0    DAN05-L_174  6530de5f05667e0045cadc4e   \n", "1  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "2  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "3  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "4  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "\n", "                                   imagename                   imageid  \\\n", "0  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "1  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "2  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "3  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "4  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "\n", "             x            y          w          h          label  blank  \\\n", "0  6351.043017  1166.623453  60.223925  46.458456  vervet monkey    NaN   \n", "1  6482.702368  1211.366282  41.640716  35.962437  vervet monkey    NaN   \n", "2  6102.257645  1084.551374  43.533476  28.391397  vervet monkey    NaN   \n", "3  6230.965313  1126.192090  39.747956  26.498637  vervet monkey    NaN   \n", "4  6405.099216  1175.403845 -39.747956  30.284157  vervet monkey    NaN   \n", "\n", "    annotator      datetime  thingy  \n", "0  annotator1  1.697720e+12   False  \n", "1     wmagesa  1.697720e+12   False  \n", "2     wmagesa  1.697720e+12   False  \n", "3     wmagesa  1.697720e+12   False  \n", "4     wmagesa  1.697720e+12   False  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "# Load the CSV file\n", "annotations = pd.read_csv('~/workspace/_temp/DANscout-export-annotations.csv')\n", "\n", "# Display the first few rows of the dataset\n", "annotations.head()\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["elephant            311\n", "white_bones          25\n", "unknown mammal       10\n", "giraffe               7\n", "bird                  6\n", "unknown antelope      4\n", "impala                4\n", "elephant bull         3\n", "zebra                 3\n", "vervet monkey         2\n", "roof_mabati           2\n", "buffalo               1\n", "kudu                  1\n", "Name: label, dtype: int64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Remove duplicate image annotations based on image name and annotator combination\n", "unique_image_annotator = annotations.drop_duplicates(subset=['imagename', 'annotator'], keep='first')\n", "\n", "# Count the number of bounding boxes per label for the unique annotations\n", "label_counts_unique_annotator = unique_image_annotator['label'].value_counts()\n", "\n", "label_counts_unique_annotator"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["annotations.to_parquet(\"~/workspace/_temp/DANscout-export-annotations.parquet\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "bboxes", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}