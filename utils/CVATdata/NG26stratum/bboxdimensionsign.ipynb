{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c197dd55", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "39417f03", "metadata": {}, "outputs": [], "source": ["coco_file_path = \"smallerDAN.json\""]}, {"cell_type": "code", "execution_count": 3, "id": "e8d7ce5b", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "annotdf = pd.DataFrame(coco_data['annotations'])"]}, {"cell_type": "code", "execution_count": 4, "id": "95d673c9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100</td>\n", "      <td>16</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>9509.492278</td>\n", "      <td>[344.6801418, 98.48004052, -84.73863952, 112.2...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1203.733346</td>\n", "      <td>[6405.099216, 1175.403845, -39.74795613, 30.28...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>32</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>[]</td>\n", "      <td>312.110186</td>\n", "      <td>[6689.40808, 130.1389467, -16.65626838, 18.738...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>2797.910556</td>\n", "      <td>[6351.043017, 1166.623453, 60.22392457, 46.458...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1497.501603</td>\n", "      <td>[6482.702368, 1211.366282, 41.64071594, 35.962...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  image_id  category_id segmentation         area  \\\n", "0  100        16            5           []  9509.492278   \n", "1    5         1           16           []  1203.733346   \n", "2   32         3            2           []   312.110186   \n", "3    1         1           16           []  2797.910556   \n", "4    2         1           16           []  1497.501603   \n", "\n", "                                                bbox  iscrowd  \n", "0  [344.6801418, 98.48004052, -84.73863952, 112.2...        0  \n", "1  [6405.099216, 1175.403845, -39.74795613, 30.28...        0  \n", "2  [6689.40808, 130.1389467, -16.65626838, 18.738...        0  \n", "3  [6351.043017, 1166.623453, 60.22392457, 46.458...        0  \n", "4  [6482.702368, 1211.366282, 41.64071594, 35.962...        0  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["annotdf.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "0d262906", "metadata": {}, "outputs": [{"data": {"text/plain": ["(19, 7)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["annotdf.shape"]}, {"cell_type": "code", "execution_count": 6, "id": "e29e478e", "metadata": {}, "outputs": [{"data": {"text/plain": ["(9, 7)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["greater_than_zero_mask = [all(item > 0 for item in row) for row in annotdf['bbox']]\n", "annotdf[greater_than_zero_mask].shape"]}, {"cell_type": "code", "execution_count": 7, "id": "97dd32bf", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>2797.910556</td>\n", "      <td>[6351.043017, 1166.623453, 60.22392457, 46.458...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1497.501603</td>\n", "      <td>[6482.702368, 1211.366282, 41.64071594, 35.962...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1235.976203</td>\n", "      <td>[6102.257645, 1084.551374, 43.53347576, 28.391...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1053.266678</td>\n", "      <td>[6230.965313, 1126.19209, 39.74795613, 26.4986...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>[]</td>\n", "      <td>697.672786</td>\n", "      <td>[3742.007804, 702.1799572, 32.70726125, 21.330...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>36</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>6315.915175</td>\n", "      <td>[3561.822336, 474.1432647, 81.38801262, 77.602...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>21</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>[]</td>\n", "      <td>580.368299</td>\n", "      <td>[1457.851306, 33.13397998, 34.06958466, 17.034...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>47</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>47059.280257</td>\n", "      <td>[1972.277251, 4477.992617, 245.6799586, 191.54...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>50</td>\n", "      <td>9</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>7400.161128</td>\n", "      <td>[3750.047018, 41.58196774, 55.42350581, 133.52...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  image_id  category_id segmentation          area  \\\n", "3    1         1           16           []   2797.910556   \n", "4    2         1           16           []   1497.501603   \n", "5    3         1           16           []   1235.976203   \n", "6    4         1           16           []   1053.266678   \n", "7    6         1            1           []    697.672786   \n", "10  36         5            5           []   6315.915175   \n", "15  21         2            1           []    580.368299   \n", "17  47         7            5           []  47059.280257   \n", "18  50         9            5           []   7400.161128   \n", "\n", "                                                 bbox  iscrowd  \n", "3   [6351.043017, 1166.623453, 60.22392457, 46.458...        0  \n", "4   [6482.702368, 1211.366282, 41.64071594, 35.962...        0  \n", "5   [6102.257645, 1084.551374, 43.53347576, 28.391...        0  \n", "6   [6230.965313, 1126.19209, 39.74795613, 26.4986...        0  \n", "7   [3742.007804, 702.1799572, 32.70726125, 21.330...        0  \n", "10  [3561.822336, 474.1432647, 81.38801262, 77.602...        0  \n", "15  [1457.851306, 33.13397998, 34.06958466, 17.034...        0  \n", "17  [1972.277251, 4477.992617, 245.6799586, 191.54...        0  \n", "18  [3750.047018, 41.58196774, 55.42350581, 133.52...        0  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["annotdf[greater_than_zero_mask]"]}, {"cell_type": "code", "execution_count": 8, "id": "7a7baeba", "metadata": {}, "outputs": [{"data": {"text/plain": ["(10, 7)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["filterpositive = annotdf[greater_than_zero_mask]['bbox'].tolist()\n", "less_than_zero_mask = annotdf[~annotdf['bbox'].isin(filterpositive)]\n", "less_than_zero_mask.shape\n"]}, {"cell_type": "code", "execution_count": 9, "id": "4ea7bf5e", "metadata": {}, "outputs": [{"data": {"text/plain": ["[[344.6801418, 98.48004052, -84.73863952, 112.2214415],\n", " [6405.099216, 1175.403845, -39.74795613, 30.28415705],\n", " [6689.40808, 130.1389467, -16.65626838, 18.73830193],\n", " [495.2323071, 144.8057253, -30.28391167, 18.92744479],\n", " [3147.13928, 691.8088799, 124.9211356, -92.7444795],\n", " [3726.491106, 697.4871133, 54.88958991, -88.95899054],\n", " [5190.787042, 356.793107, -130.5993691, -123.0283912],\n", " [6739.120052, 4090.668803, 190.0887593, -174.0571772],\n", " [6739.120052, 4118.151515, -180.9278553, -153.4451431],\n", " [309.4895723, 4505.059053, -278.9924954, 162.3986167]]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["#before\n", "less_than_zero_mask['bbox'].tolist()"]}, {"cell_type": "code", "execution_count": 11, "id": "1bd9d034", "metadata": {}, "outputs": [{"data": {"text/plain": ["[[428.6801418, 98.48004052, 84.73863952, 112.2214415],\n", " [6444.099216, 1175.403845, 39.74795613, 30.28415705],\n", " [6705.40808, 130.1389467, 16.65626838, 18.73830193],\n", " [525.2323071000001, 144.8057253, 30.28391167, 18.92744479],\n", " [3147.13928, 783.8088799, 124.9211356, 92.7444795],\n", " [3726.491106, 785.4871133, 54.88958991, 88.95899054],\n", " [5320.787042, 479.793107, 130.5993691, 123.0283912],\n", " [6739.120052, 4264.6688030000005, 190.0887593, 174.0571772],\n", " [6919.120052, 4271.151515, 180.9278553, 153.4451431],\n", " [587.4895723, 4505.059053, 278.9924954, 162.3986167]]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["#after \n", "less_than_zero_mask['bbox'].tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "52e72342", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 10, "id": "d65ee9e5", "metadata": {}, "outputs": [], "source": ["for item in less_than_zero_mask['bbox']:\n", "    \n", "    if((item[-2]<0) and (item[-1]<0)):\n", "        item[-2] = abs(item[-2])\n", "        item[0]  = item[0] + int(abs(item[-2]))\n", "        item[-1] = abs(item[-1])\n", "        item[1] = item[1] + int(abs(item[-1]))\n", "    \n", "    elif((item[0]<0) and (item[1]<0)):\n", "        item[0] = abs(item[0])\n", "        item[-2] = item[-2] + int(abs(item[0]))\n", "        item[1] = abs(item[1])\n", "        item[-1] = item[-1] + int(abs(item[1]))\n", "        \n", "    elif(item[-2]<0):\n", "        item[-2] = abs(item[-2])\n", "        item[0]  = item[0] + int(abs(item[-2]))\n", "    \n", "    elif(item[0]<0):\n", "        item[0] = abs(item[0])\n", "        item[-2] = item[-2] + int(abs(item[0]))\n", "           \n", "    elif(item[1]<0):\n", "        item[1] = abs(item[1])\n", "        item[-1] = item[-1] + int(abs(item[1]))\n", "            \n", "    elif(item[-1]<0):\n", "        item[-1] = abs(item[-1])\n", "        item[1] = item[1] + int(abs(item[-1]))\n", "    \n", "    \n", "    \n", "    "]}, {"cell_type": "code", "execution_count": 13, "id": "79882544", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100</td>\n", "      <td>16</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>9509.492278</td>\n", "      <td>[428.6801418, 98.48004052, 84.73863952, 112.22...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1203.733346</td>\n", "      <td>[6444.099216, 1175.403845, 39.74795613, 30.284...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>32</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>[]</td>\n", "      <td>312.110186</td>\n", "      <td>[6705.40808, 130.1389467, 16.65626838, 18.7383...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>34</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>[]</td>\n", "      <td>573.197066</td>\n", "      <td>[525.2323071000001, 144.8057253, 30.28391167, ...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>35</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>11585.745700</td>\n", "      <td>[3147.13928, 783.8088799, 124.9211356, 92.7444...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>37</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>4882.922510</td>\n", "      <td>[3726.491106, 785.4871133, 54.88958991, 88.958...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>39</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>16067.430272</td>\n", "      <td>[5320.787042, 479.793107, 130.5993691, 123.028...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>43</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>33086.312861</td>\n", "      <td>[6739.120052, 4264.6688030000005, 190.0887593,...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>44</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>27762.500647</td>\n", "      <td>[6919.120052, 4271.151515, 180.9278553, 153.44...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>46</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>45307.995323</td>\n", "      <td>[587.4895723, 4505.059053, 278.9924954, 162.39...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     id  image_id  category_id segmentation          area  \\\n", "0   100        16            5           []   9509.492278   \n", "1     5         1           16           []   1203.733346   \n", "2    32         3            2           []    312.110186   \n", "8    34         4            2           []    573.197066   \n", "9    35         5            5           []  11585.745700   \n", "11   37         5            5           []   4882.922510   \n", "12   39         5            5           []  16067.430272   \n", "13   43         6            5           []  33086.312861   \n", "14   44         6            5           []  27762.500647   \n", "16   46         7            5           []  45307.995323   \n", "\n", "                                                 bbox  iscrowd  \n", "0   [428.6801418, 98.48004052, 84.73863952, 112.22...        0  \n", "1   [6444.099216, 1175.403845, 39.74795613, 30.284...        0  \n", "2   [6705.40808, 130.1389467, 16.65626838, 18.7383...        0  \n", "8   [525.2323071000001, 144.8057253, 30.28391167, ...        0  \n", "9   [3147.13928, 783.8088799, 124.9211356, 92.7444...        0  \n", "11  [3726.491106, 785.4871133, 54.88958991, 88.958...        0  \n", "12  [5320.787042, 479.793107, 130.5993691, 123.028...        0  \n", "13  [6739.120052, 4264.6688030000005, 190.0887593,...        0  \n", "14  [6919.120052, 4271.151515, 180.9278553, 153.44...        0  \n", "16  [587.4895723, 4505.059053, 278.9924954, 162.39...        0  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["#after\n", "less_than_zero_mask"]}, {"cell_type": "code", "execution_count": 14, "id": "7fd21b41", "metadata": {}, "outputs": [], "source": ["annotdf.update(less_than_zero_mask)"]}, {"cell_type": "code", "execution_count": 15, "id": "270202d0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100.0</td>\n", "      <td>16.0</td>\n", "      <td>5.0</td>\n", "      <td>[]</td>\n", "      <td>9509.492278</td>\n", "      <td>[428.6801418, 98.48004052, 84.73863952, 112.22...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5.0</td>\n", "      <td>1.0</td>\n", "      <td>16.0</td>\n", "      <td>[]</td>\n", "      <td>1203.733346</td>\n", "      <td>[6444.099216, 1175.403845, 39.74795613, 30.284...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>32.0</td>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>[]</td>\n", "      <td>312.110186</td>\n", "      <td>[6705.40808, 130.1389467, 16.65626838, 18.7383...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>16.0</td>\n", "      <td>[]</td>\n", "      <td>2797.910556</td>\n", "      <td>[6351.043017, 1166.623453, 60.22392457, 46.458...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>16.0</td>\n", "      <td>[]</td>\n", "      <td>1497.501603</td>\n", "      <td>[6482.702368, 1211.366282, 41.64071594, 35.962...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>16.0</td>\n", "      <td>[]</td>\n", "      <td>1235.976203</td>\n", "      <td>[6102.257645, 1084.551374, 43.53347576, 28.391...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>16.0</td>\n", "      <td>[]</td>\n", "      <td>1053.266678</td>\n", "      <td>[6230.965313, 1126.19209, 39.74795613, 26.4986...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>6.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>[]</td>\n", "      <td>697.672786</td>\n", "      <td>[3742.007804, 702.1799572, 32.70726125, 21.330...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>34.0</td>\n", "      <td>4.0</td>\n", "      <td>2.0</td>\n", "      <td>[]</td>\n", "      <td>573.197066</td>\n", "      <td>[525.2323071000001, 144.8057253, 30.28391167, ...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>35.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>[]</td>\n", "      <td>11585.745700</td>\n", "      <td>[3147.13928, 783.8088799, 124.9211356, 92.7444...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>36.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>[]</td>\n", "      <td>6315.915175</td>\n", "      <td>[3561.822336, 474.1432647, 81.38801262, 77.602...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>37.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>[]</td>\n", "      <td>4882.922510</td>\n", "      <td>[3726.491106, 785.4871133, 54.88958991, 88.958...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>39.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>[]</td>\n", "      <td>16067.430272</td>\n", "      <td>[5320.787042, 479.793107, 130.5993691, 123.028...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>43.0</td>\n", "      <td>6.0</td>\n", "      <td>5.0</td>\n", "      <td>[]</td>\n", "      <td>33086.312861</td>\n", "      <td>[6739.120052, 4264.6688030000005, 190.0887593,...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>44.0</td>\n", "      <td>6.0</td>\n", "      <td>5.0</td>\n", "      <td>[]</td>\n", "      <td>27762.500647</td>\n", "      <td>[6919.120052, 4271.151515, 180.9278553, 153.44...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>21.0</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>[]</td>\n", "      <td>580.368299</td>\n", "      <td>[1457.851306, 33.13397998, 34.06958466, 17.034...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>46.0</td>\n", "      <td>7.0</td>\n", "      <td>5.0</td>\n", "      <td>[]</td>\n", "      <td>45307.995323</td>\n", "      <td>[587.4895723, 4505.059053, 278.9924954, 162.39...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>47.0</td>\n", "      <td>7.0</td>\n", "      <td>5.0</td>\n", "      <td>[]</td>\n", "      <td>47059.280257</td>\n", "      <td>[1972.277251, 4477.992617, 245.6799586, 191.54...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>50.0</td>\n", "      <td>9.0</td>\n", "      <td>5.0</td>\n", "      <td>[]</td>\n", "      <td>7400.161128</td>\n", "      <td>[3750.047018, 41.58196774, 55.42350581, 133.52...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       id  image_id  category_id segmentation          area  \\\n", "0   100.0      16.0          5.0           []   9509.492278   \n", "1     5.0       1.0         16.0           []   1203.733346   \n", "2    32.0       3.0          2.0           []    312.110186   \n", "3     1.0       1.0         16.0           []   2797.910556   \n", "4     2.0       1.0         16.0           []   1497.501603   \n", "5     3.0       1.0         16.0           []   1235.976203   \n", "6     4.0       1.0         16.0           []   1053.266678   \n", "7     6.0       1.0          1.0           []    697.672786   \n", "8    34.0       4.0          2.0           []    573.197066   \n", "9    35.0       5.0          5.0           []  11585.745700   \n", "10   36.0       5.0          5.0           []   6315.915175   \n", "11   37.0       5.0          5.0           []   4882.922510   \n", "12   39.0       5.0          5.0           []  16067.430272   \n", "13   43.0       6.0          5.0           []  33086.312861   \n", "14   44.0       6.0          5.0           []  27762.500647   \n", "15   21.0       2.0          1.0           []    580.368299   \n", "16   46.0       7.0          5.0           []  45307.995323   \n", "17   47.0       7.0          5.0           []  47059.280257   \n", "18   50.0       9.0          5.0           []   7400.161128   \n", "\n", "                                                 bbox  iscrowd  \n", "0   [428.6801418, 98.48004052, 84.73863952, 112.22...      0.0  \n", "1   [6444.099216, 1175.403845, 39.74795613, 30.284...      0.0  \n", "2   [6705.40808, 130.1389467, 16.65626838, 18.7383...      0.0  \n", "3   [6351.043017, 1166.623453, 60.22392457, 46.458...      0.0  \n", "4   [6482.702368, 1211.366282, 41.64071594, 35.962...      0.0  \n", "5   [6102.257645, 1084.551374, 43.53347576, 28.391...      0.0  \n", "6   [6230.965313, 1126.19209, 39.74795613, 26.4986...      0.0  \n", "7   [3742.007804, 702.1799572, 32.70726125, 21.330...      0.0  \n", "8   [525.2323071000001, 144.8057253, 30.28391167, ...      0.0  \n", "9   [3147.13928, 783.8088799, 124.9211356, 92.7444...      0.0  \n", "10  [3561.822336, 474.1432647, 81.38801262, 77.602...      0.0  \n", "11  [3726.491106, 785.4871133, 54.88958991, 88.958...      0.0  \n", "12  [5320.787042, 479.793107, 130.5993691, 123.028...      0.0  \n", "13  [6739.120052, 4264.6688030000005, 190.0887593,...      0.0  \n", "14  [6919.120052, 4271.151515, 180.9278553, 153.44...      0.0  \n", "15  [1457.851306, 33.13397998, 34.06958466, 17.034...      0.0  \n", "16  [587.4895723, 4505.059053, 278.9924954, 162.39...      0.0  \n", "17  [1972.277251, 4477.992617, 245.6799586, 191.54...      0.0  \n", "18  [3750.047018, 41.58196774, 55.42350581, 133.52...      0.0  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["annotdf"]}, {"cell_type": "code", "execution_count": 16, "id": "bd1244fc", "metadata": {}, "outputs": [{"data": {"text/plain": ["(19, 7)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["greater_than_zero_mask = [all(item > 0 for item in row) for row in annotdf['bbox']]\n", "annotdf[greater_than_zero_mask].shape"]}, {"cell_type": "code", "execution_count": null, "id": "cadf8d0a", "metadata": {}, "outputs": [], "source": ["#then to COCO format"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}