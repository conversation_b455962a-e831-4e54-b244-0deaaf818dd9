{"cells": [{"cell_type": "markdown", "id": "c7ca5c06", "metadata": {}, "source": ["### Using Fiftyone to find dataset that can be annotated with CVAT\n", "\n", "\n", "Reference: https://docs.voxel51.com/tutorials/cvat_annotation.html"]}, {"cell_type": "code", "execution_count": 1, "id": "4c756e04", "metadata": {}, "outputs": [], "source": ["import fiftyone as fo"]}, {"cell_type": "markdown", "id": "1974b194", "metadata": {}, "source": ["Loading a subset of unlabelled images into Fiftyone"]}, {"cell_type": "code", "execution_count": 3, "id": "4977dd50", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 100% |███████████████████| 50/50 [43.3ms elapsed, 0s remaining, 1.2K samples/s]      \n"]}], "source": ["dataset_dir = \"/home/<USER>/Dropbox/Conservation/MWS_LabArusha/annotations/tsavo51/\"\n", "name = \"tsavotest\"\n", "\n", "dataset = fo.Dataset.from_dir(\n", "    dataset_dir=dataset_dir,\n", "    dataset_type=fo.types.ImageDirectory,\n", "    name=name,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "8da9d811", "metadata": {}, "outputs": [], "source": ["dataset.persistent = True"]}, {"cell_type": "markdown", "id": "d21c39e3", "metadata": {}, "source": ["Visualizing the dataset on Fiftyone"]}, {"cell_type": "code", "execution_count": 5, "id": "811ef258", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"800\"\n", "            src=\"http://localhost:5151/?notebook=True&subscription=59b67800-4468-43f8-8a52-b909653bc33c\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x7f2362ac1400>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["session = fo.launch_app(dataset)"]}, {"cell_type": "markdown", "id": "34711912", "metadata": {}, "source": ["### Finding unique samples\n", "- run the `compute_similarity()` method on the dataset in order to index all samples in the dataset by their visual similarity.\n", "\n", "- Once this is done, we can then use the index to find the most unique samples\n", "\n", "- Having reduced the number of samples that need to be annotated, the time and cost of annotating this dataset have also been reduced"]}, {"cell_type": "code", "execution_count": 6, "id": "72419f03", "metadata": {}, "outputs": [], "source": ["import fiftyone.brain as fob"]}, {"cell_type": "code", "execution_count": 7, "id": "16a27547", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Computing embeddings...\n", " 100% |███████████████████| 50/50 [2.3m elapsed, 0s remaining, 0.4 samples/s]    \n", "Computing unique samples...\n", "Generating index for 50 embeddings...\n", "Index complete\n", "threshold: 1.000000, kept: 4, target: 30\n", "threshold: 0.500000, kept: 14, target: 30\n", "threshold: 0.250000, kept: 37, target: 30\n", "threshold: 0.375000, kept: 21, target: 30\n", "threshold: 0.312500, kept: 29, target: 30\n", "threshold: 0.281250, kept: 33, target: 30\n", "threshold: 0.296875, kept: 31, target: 30\n", "threshold: 0.304688, kept: 29, target: 30\n", "threshold: 0.300781, kept: 31, target: 30\n", "threshold: 0.302734, kept: 30, target: 30\n", "Uniqueness computation complete\n"]}], "source": ["results = fob.compute_similarity(dataset, brain_key=\"img_sim\")\n", "results.find_unique(30)"]}, {"cell_type": "markdown", "id": "b4b7b70c", "metadata": {}, "source": ["We can also visualize the exact samples that were selected.\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "id": "241bf24e", "metadata": {}, "outputs": [{"ename": "PackageError", "evalue": "You must install the `umap-learn>=0.5` package in order to use UMAP-based visualization. This is recommended, as UMAP is awesome! If you do not wish to install UMAP, try `method='tsne'` instead\n\nIf you think this error is inaccurate, you can set `fiftyone.config.requirement_error_level` to 1 (warning) or 2 (ignore).\nSee https://docs.voxel51.com/user_guide/config.html for details.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mPackageNotFoundError\u001b[0m                      <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/eta/core/utils.py:728\u001b[0m, in \u001b[0;36m_get_package_version\u001b[0;34m(requirement_str)\u001b[0m\n\u001b[1;32m    727\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 728\u001b[0m     version \u001b[38;5;241m=\u001b[39m \u001b[43mmetadata\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mversion\u001b[49m\u001b[43m(\u001b[49m\u001b[43mreq\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    729\u001b[0m     error \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/importlib/metadata.py:569\u001b[0m, in \u001b[0;36mversion\u001b[0;34m(distribution_name)\u001b[0m\n\u001b[1;32m    563\u001b[0m \u001b[38;5;124;03m\"\"\"Get the version string for the named package.\u001b[39;00m\n\u001b[1;32m    564\u001b[0m \n\u001b[1;32m    565\u001b[0m \u001b[38;5;124;03m:param distribution_name: The name of the distribution package to query.\u001b[39;00m\n\u001b[1;32m    566\u001b[0m \u001b[38;5;124;03m:return: The version string for the package as defined in the package's\u001b[39;00m\n\u001b[1;32m    567\u001b[0m \u001b[38;5;124;03m    \"Version\" metadata key.\u001b[39;00m\n\u001b[1;32m    568\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m--> 569\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mdistribution\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdistribution_name\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mversion\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/importlib/metadata.py:542\u001b[0m, in \u001b[0;36mdistribution\u001b[0;34m(distribution_name)\u001b[0m\n\u001b[1;32m    537\u001b[0m \u001b[38;5;124;03m\"\"\"Get the ``Distribution`` instance for the named package.\u001b[39;00m\n\u001b[1;32m    538\u001b[0m \n\u001b[1;32m    539\u001b[0m \u001b[38;5;124;03m:param distribution_name: The name of the distribution package as a string.\u001b[39;00m\n\u001b[1;32m    540\u001b[0m \u001b[38;5;124;03m:return: A ``Distribution`` instance (or subclass thereof).\u001b[39;00m\n\u001b[1;32m    541\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m--> 542\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mDistribution\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_name\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdistribution_name\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/importlib/metadata.py:196\u001b[0m, in \u001b[0;36mDistribution.from_name\u001b[0;34m(cls, name)\u001b[0m\n\u001b[1;32m    195\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 196\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m PackageNotFoundError(name)\n", "\u001b[0;31mPackageNotFoundError\u001b[0m: umap-learn", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mPackageError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[8], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m vis_results \u001b[38;5;241m=\u001b[39m \u001b[43mfob\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcompute_visualization\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdataset\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbrain_key\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mimg_vis\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/fiftyone/brain/__init__.py:376\u001b[0m, in \u001b[0;36mcompute_visualization\u001b[0;34m(samples, patches_field, embeddings, points, brain_key, num_dims, method, model, force_square, alpha, batch_size, num_workers, skip_failures, **kwargs)\u001b[0m\n\u001b[1;32m    267\u001b[0m \u001b[38;5;124;03m\"\"\"Computes a low-dimensional representation of the samples' media or their\u001b[39;00m\n\u001b[1;32m    268\u001b[0m \u001b[38;5;124;03mpatches that can be interactively visualized.\u001b[39;00m\n\u001b[1;32m    269\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    372\u001b[0m \u001b[38;5;124;03m    a :class:`fiftyone.brain.visualization.VisualizationResults`\u001b[39;00m\n\u001b[1;32m    373\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    374\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mfiftyone\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbrain\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01minternal\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mvisualization\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mfbv\u001b[39;00m\n\u001b[0;32m--> 376\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfbv\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcompute_visualization\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    377\u001b[0m \u001b[43m    \u001b[49m\u001b[43msamples\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    378\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpatches_field\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    379\u001b[0m \u001b[43m    \u001b[49m\u001b[43membeddings\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    380\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpoints\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    381\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbrain_key\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    382\u001b[0m \u001b[43m    \u001b[49m\u001b[43mnum_dims\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    383\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    384\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    385\u001b[0m \u001b[43m    \u001b[49m\u001b[43mforce_square\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    386\u001b[0m \u001b[43m    \u001b[49m\u001b[43malpha\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    387\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbatch_size\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    388\u001b[0m \u001b[43m    \u001b[49m\u001b[43mnum_workers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    389\u001b[0m \u001b[43m    \u001b[49m\u001b[43mskip_failures\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    390\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    391\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/fiftyone/brain/internal/core/visualization.py:93\u001b[0m, in \u001b[0;36mcompute_visualization\u001b[0;34m(samples, patches_field, embeddings, points, brain_key, num_dims, method, model, force_square, alpha, batch_size, num_workers, skip_failures, **kwargs)\u001b[0m\n\u001b[1;32m     88\u001b[0m config \u001b[38;5;241m=\u001b[39m _parse_config(\n\u001b[1;32m     89\u001b[0m     embeddings_field, model, patches_field, method, num_dims, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs\n\u001b[1;32m     90\u001b[0m )\n\u001b[1;32m     92\u001b[0m brain_method \u001b[38;5;241m=\u001b[39m config\u001b[38;5;241m.\u001b[39mbuild()\n\u001b[0;32m---> 93\u001b[0m \u001b[43mbrain_method\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mensure_requirements\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     95\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m brain_key \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m     96\u001b[0m     brain_method\u001b[38;5;241m.\u001b[39mregister_run(samples, brain_key)\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/fiftyone/brain/internal/core/visualization.py:218\u001b[0m, in \u001b[0;36mUMAPVisualization.ensure_requirements\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    217\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mensure_requirements\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m--> 218\u001b[0m     \u001b[43mfou\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mensure_package\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    219\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mumap-learn>=0.5\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    220\u001b[0m \u001b[43m        \u001b[49m\u001b[43merror_msg\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m    221\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m<PERSON><PERSON> must install the `umap-learn>=0.5` package in order to \u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\n\u001b[1;32m    222\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43muse UMAP-based visualization. This is recommended, as UMAP \u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\n\u001b[1;32m    223\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mis awesome! If you do not wish to install UMAP, try \u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\n\u001b[1;32m    224\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m`method=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mtsne\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m` instead\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\n\u001b[1;32m    225\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    226\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/fiftyone/core/utils.py:420\u001b[0m, in \u001b[0;36mensure_package\u001b[0;34m(requirement_str, error_level, error_msg, log_success)\u001b[0m\n\u001b[1;32m    417\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m error_level \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    418\u001b[0m     error_level \u001b[38;5;241m=\u001b[39m fo\u001b[38;5;241m.\u001b[39mconfig\u001b[38;5;241m.\u001b[39mrequirement_error_level\n\u001b[0;32m--> 420\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43metau\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mensure_package\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    421\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrequirement_str\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    422\u001b[0m \u001b[43m    \u001b[49m\u001b[43merror_level\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merror_level\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    423\u001b[0m \u001b[43m    \u001b[49m\u001b[43merror_msg\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merror_msg\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    424\u001b[0m \u001b[43m    \u001b[49m\u001b[43merror_suffix\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m_REQUIREMENT_ERROR_SUFFIX\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    425\u001b[0m \u001b[43m    \u001b[49m\u001b[43mlog_success\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlog_success\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    426\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/eta/core/utils.py:709\u001b[0m, in \u001b[0;36mensure_package\u001b[0;34m(requirement_str, error_level, error_msg, error_suffix, log_success)\u001b[0m\n\u001b[1;32m    706\u001b[0m parse_requirement \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mlambda\u001b[39;00m req_str: _get_package_version(req_str)\n\u001b[1;32m    707\u001b[0m error_cls \u001b[38;5;241m=\u001b[39m PackageError\n\u001b[0;32m--> 709\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_ensure_requirements\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    710\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrequirement_str\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    711\u001b[0m \u001b[43m    \u001b[49m\u001b[43mparse_requirement\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    712\u001b[0m \u001b[43m    \u001b[49m\u001b[43merror_level\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    713\u001b[0m \u001b[43m    \u001b[49m\u001b[43merror_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    714\u001b[0m \u001b[43m    \u001b[49m\u001b[43merror_msg\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    715\u001b[0m \u001b[43m    \u001b[49m\u001b[43merror_suffix\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    716\u001b[0m \u001b[43m    \u001b[49m\u001b[43mlog_success\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    717\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/eta/core/utils.py:844\u001b[0m, in \u001b[0;36m_ensure_requirements\u001b[0;34m(requirement_str, parse_requirement, error_level, error_cls, error_msg, error_suffix, log_success)\u001b[0m\n\u001b[1;32m    842\u001b[0m \u001b[38;5;66;03m# Require all\u001b[39;00m\n\u001b[1;32m    843\u001b[0m results \u001b[38;5;241m=\u001b[39m [parse_requirement(req_str) \u001b[38;5;28;01mfor\u001b[39;00m req_str \u001b[38;5;129;01min\u001b[39;00m requirement_str]\n\u001b[0;32m--> 844\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43m_ensure_all_requirements\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    845\u001b[0m \u001b[43m    \u001b[49m\u001b[43mresults\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    846\u001b[0m \u001b[43m    \u001b[49m\u001b[43merror_level\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    847\u001b[0m \u001b[43m    \u001b[49m\u001b[43mlog_success\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    848\u001b[0m \u001b[43m    \u001b[49m\u001b[43merror_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merror_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    849\u001b[0m \u001b[43m    \u001b[49m\u001b[43merror_msg\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merror_msg\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    850\u001b[0m \u001b[43m    \u001b[49m\u001b[43merror_suffix\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merror_suffix\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    851\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/eta/core/utils.py:922\u001b[0m, in \u001b[0;36m_ensure_all_requirements\u001b[0;34m(results, error_level, log_success, error_cls, error_msg, error_suffix)\u001b[0m\n\u001b[1;32m    919\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m error_suffix \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m error_level \u001b[38;5;241m<\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m    920\u001b[0m     error_msg \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m error_suffix\n\u001b[0;32m--> 922\u001b[0m \u001b[43mhandle_error\u001b[49m\u001b[43m(\u001b[49m\u001b[43merror_cls\u001b[49m\u001b[43m(\u001b[49m\u001b[43merror_msg\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror_level\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbase_error\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlast_error\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    924\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/eta/core/utils.py:1002\u001b[0m, in \u001b[0;36mhandle_error\u001b[0;34m(error, error_level, base_error)\u001b[0m\n\u001b[1;32m   1000\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m error_level \u001b[38;5;241m<\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m   1001\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m base_error \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m-> 1002\u001b[0m         \u001b[43msix\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mraise_from\u001b[49m\u001b[43m(\u001b[49m\u001b[43merror\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbase_error\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1003\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1004\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m error\n", "File \u001b[0;32m<string>:3\u001b[0m, in \u001b[0;36mraise_from\u001b[0;34m(value, from_value)\u001b[0m\n", "\u001b[0;31mPackageError\u001b[0m: You must install the `umap-learn>=0.5` package in order to use UMAP-based visualization. This is recommended, as UMAP is awesome! If you do not wish to install UMAP, try `method='tsne'` instead\n\nIf you think this error is inaccurate, you can set `fiftyone.config.requirement_error_level` to 1 (warning) or 2 (ignore).\nSee https://docs.voxel51.com/user_guide/config.html for details."]}], "source": ["vis_results = fob.compute_visualization(dataset, brain_key=\"img_vis\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "8fb6fdec", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "128eb4d4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9a783d7a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "883080c1", "metadata": {}, "outputs": [], "source": ["session.freeze()"]}, {"cell_type": "code", "execution_count": null, "id": "8423b204", "metadata": {}, "outputs": [], "source": ["session.close()"]}, {"cell_type": "code", "execution_count": null, "id": "7254b4a4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}