# Model the time taken per annotator

# todo:
#  - direct connect Google sheet

# Libraries / setup
library(tidyverse); source("~/workspace/R/_functions/ClipboardFunctions.R")

# Load data
tst <- ClipPaste() # ToDo: connect Drive
tst <- tst %>% mutate(
  rate = Duration / No.Images,
  #Annotator = as.factor(Annotator),
  Date = as.Date(Date, "%d/%m/%Y")
) %>%
  filter(!is.na(bboxes), !is.na(No.Images), !is.na(Annotator),
         Duration > 0)

tst <- tst[order(tst$rate), ]

# Annotator performance

glm(data = tst, formula = rate ~ bboxes + Annotator)

boxplot(tst$rate[tst$Date >= as.Date("16-10-2023", "%d-%m-%Y")] ~ tst$Annotator[tst$Date >= as.Date("16-10-2023", "%d-%m-%Y")], ylim = c(0, 5), ylab = ("Minutes per image"),
        xlab = 'Annotator')
abline(h = median(tst$rate[tst$Date >= as.Date("16-10-2023", "%d-%m-%Y")], na.rm = T), col = 'green', lwd = 2)

rate.imgbox <- tst %>% filter(bboxes > 0) %>%
  glm(data = ., formula = Duration ~ No.Images + bboxes)
rate.imgbox0 <- tst %>% filter(bboxes > 0) %>%
  glm(data = ., formula = Duration ~ No.Images + bboxes - 1)

tst$pred <- tst$No.Images * coef(rate.imgbox0)[1] + tst$bboxes * coef(rate.imgbox0)[2]
g <- tst %>% filter(!is.na(pred), Date >= as.Date("16-10-2023", "%d-%m-%Y")) %>% 
  ggplot(mapping = aes(x = Duration, y = pred, color = Annotator)) 
g + geom_point(cex = 3) +xlim(0, 200) + ylim(0, 200) + geom_jitter() + geom_abline(intercept = rate.imgbox$coefficients[1], slope = sum(rate.imgbox$coefficients[2:3]), col = 'red') + 
  geom_abline(intercept = 0, slope = 1/sum(rate.imgbox0$coefficients[1:2]))

anntr <- "Helena"
t <-
  tst %>%  ggplot(aes(x = Date, y = rate)) + geom_point() + ggtitle("All"); t
mean(tst$rate[tst$Date == as.Date("16-10-2023", "%d-%m-%Y")], na.rm = T)
