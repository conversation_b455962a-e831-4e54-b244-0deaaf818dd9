{"cells": [{"cell_type": "code", "execution_count": 2, "id": "a408e247", "metadata": {}, "outputs": [], "source": ["# search for missing files\n", "import pandas as pd"]}, {"cell_type": "markdown", "id": "6dbe4aa5-dd3f-4b0e-bd52-4d80a20a6203", "metadata": {}, "source": ["## DAN block"]}, {"cell_type": "code", "execution_count": 3, "id": "c52b1e34-5ed6-42a9-b1c8-05d027757ced", "metadata": {}, "outputs": [], "source": ["homedir = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023\""]}, {"cell_type": "code", "execution_count": 27, "id": "4f1139e4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>ImageFilename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062708_M0309366.jpg</td>\n", "      <td>6530d3400e8be9004ca314e2</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062718_M0309371.jpg</td>\n", "      <td>6530d3cb0e8be9004ca31510</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062712_M0309368.jpg</td>\n", "      <td>6530d3f80e8be9004ca3151d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "      <td>6530d42a0e8be9004ca3152d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062706_M0309365.jpg</td>\n", "      <td>6530d4af0e8be9004ca3155e</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Task Name                   Task ID  \\\n", "0  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "1  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "2  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "3  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "4  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "\n", "                               ImageFilename                  Image ID  \\\n", "0  KES22_IIM-L_20220906A-062708_M0309366.jpg  6530d3400e8be9004ca314e2   \n", "1  KES22_IIM-L_20220906A-062718_M0309371.jpg  6530d3cb0e8be9004ca31510   \n", "2  KES22_IIM-L_20220906A-062712_M0309368.jpg  6530d3f80e8be9004ca3151d   \n", "3  KES22_IIM-L_20220906A-062716_M0309370.jpg  6530d42a0e8be9004ca3152d   \n", "4  KES22_IIM-L_20220906A-062706_M0309365.jpg  6530d4af0e8be9004ca3155e   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                 True   \n", "1          4672         7008             NaN                 True   \n", "2          4672         7008             NaN                 True   \n", "3          4672         7008             NaN                 True   \n", "4          4672         7008             NaN                 True   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["danimg = pd.read_csv(homedir+\"/scoutexports/DANgtscout-export-images.csv\")\n", "danimg.head()"]}, {"cell_type": "code", "execution_count": 28, "id": "9295b56a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(13154, 11)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["danimg.shape"]}, {"cell_type": "code", "execution_count": 33, "id": "59a7068b-df39-4c6a-9cd5-8f41547a7a4a", "metadata": {}, "outputs": [{"data": {"text/plain": ["8534"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["danimg[\"ImageFilename\"].nunique()"]}, {"cell_type": "code", "execution_count": 34, "id": "bd680e2f-df89-4f83-9ef8-cac6c76f93f4", "metadata": {}, "outputs": [], "source": ["danimgdf = danimg[\"ImageFilename\"].unique()  #unique images on tasks"]}, {"cell_type": "code", "execution_count": 35, "id": "37485243-526d-4045-8cc7-d53b0dfac8df", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220906A-062708_M0309366.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220906A-062718_M0309371.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220906A-062712_M0309368.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220906A-062706_M0309365.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename\n", "0  KES22_IIM-L_20220906A-062708_M0309366.jpg\n", "1  KES22_IIM-L_20220906A-062718_M0309371.jpg\n", "2  KES22_IIM-L_20220906A-062712_M0309368.jpg\n", "3  KES22_IIM-L_20220906A-062716_M0309370.jpg\n", "4  KES22_IIM-L_20220906A-062706_M0309365.jpg"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["danimgdf= pd.DataFrame(danimgdf,columns=[\"ImageFilename\"])\n", "danimgdf.head()"]}, {"cell_type": "code", "execution_count": 39, "id": "2cd7a4a8-04fb-4785-980d-42d2fe7b92db", "metadata": {}, "outputs": [{"data": {"text/plain": ["(9076, 1)"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["##ingested files\n", "daningestdf = pd.read_csv(homedir+\"/labriefs/_media/DANingested.csv\")\n", "daningestdf.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2fc744c7-1280-4b2c-981f-507a6c365062", "metadata": {}, "outputs": [], "source": ["#find the difference 9076 - 8534 = 542 images\n", "daningestdf['missimg'] = 'imageid1'\n", "dang26img['missimg'] ='imageid2'\n", "\n", "\n", "merged_df = ng26ingestdf.merge(ng26img, on='ImageFilename',how='left', suffixes=('_img1','_img2'))\n", "print(merged_df.count())\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6f3065f6-640a-4edb-b5dd-8fc1f9f4f4a5", "metadata": {}, "outputs": [], "source": ["rows_not_in_tasks = merged_df[merged_df['missimg_img2'].isna()]\n", "\n", "\n", "rows_not_in_tasks.count()"]}, {"cell_type": "code", "execution_count": null, "id": "e13a60ac-ccfa-40ea-aafe-2157c2f3aa77", "metadata": {}, "outputs": [], "source": ["rows_not_in_tasks.to_csv(\"MissingfilesDAN.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "2cd16a28-5da9-4a41-8b16-18b9de1d7b8b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7831a06c-38fa-4686-8d84-a1ef65d38560", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 22, "id": "8199aec7", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>ImageFilename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062708_M0309366.jpg</td>\n", "      <td>6530d3400e8be9004ca314e2</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062718_M0309371.jpg</td>\n", "      <td>6530d3cb0e8be9004ca31510</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062712_M0309368.jpg</td>\n", "      <td>6530d3f80e8be9004ca3151d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "      <td>6530d42a0e8be9004ca3152d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062706_M0309365.jpg</td>\n", "      <td>6530d4af0e8be9004ca3155e</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Task Name                   Task ID  \\\n", "0  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "1  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "2  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "3  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "4  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "\n", "                               ImageFilename                  Image ID  \\\n", "0  KES22_IIM-L_20220906A-062708_M0309366.jpg  6530d3400e8be9004ca314e2   \n", "1  KES22_IIM-L_20220906A-062718_M0309371.jpg  6530d3cb0e8be9004ca31510   \n", "2  KES22_IIM-L_20220906A-062712_M0309368.jpg  6530d3f80e8be9004ca3151d   \n", "3  KES22_IIM-L_20220906A-062716_M0309370.jpg  6530d42a0e8be9004ca3152d   \n", "4  KES22_IIM-L_20220906A-062706_M0309365.jpg  6530d4af0e8be9004ca3155e   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                 True   \n", "1          4672         7008             NaN                 True   \n", "2          4672         7008             NaN                 True   \n", "3          4672         7008             NaN                 True   \n", "4          4672         7008             NaN                 True   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["danimgdf = pd.read_csv(\"DANimages.csv\")\n", "danimgdf.head()"]}, {"cell_type": "code", "execution_count": 23, "id": "f9936294", "metadata": {}, "outputs": [{"data": {"text/plain": ["(13154, 11)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["danimgdf.shape"]}, {"cell_type": "code", "execution_count": 25, "id": "9a70ba83", "metadata": {}, "outputs": [{"data": {"text/plain": ["8534"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["danimgdf[\"ImageFilename\"].nunique()"]}, {"cell_type": "code", "execution_count": 36, "id": "1a407eb6", "metadata": {}, "outputs": [], "source": ["danimg = danimgdf[\"ImageFilename\"].unique()"]}, {"cell_type": "code", "execution_count": 37, "id": "7a917c16", "metadata": {}, "outputs": [{"data": {"text/plain": ["(8534,)"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["danimg.shape"]}, {"cell_type": "code", "execution_count": 38, "id": "f7fa4a7b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220906A-062708_M0309366.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220906A-062718_M0309371.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220906A-062712_M0309368.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220906A-062706_M0309365.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename\n", "0  KES22_IIM-L_20220906A-062708_M0309366.jpg\n", "1  KES22_IIM-L_20220906A-062718_M0309371.jpg\n", "2  KES22_IIM-L_20220906A-062712_M0309368.jpg\n", "3  KES22_IIM-L_20220906A-062716_M0309370.jpg\n", "4  KES22_IIM-L_20220906A-062706_M0309365.jpg"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["danimg= pd.DataFrame(danimg,columns=[\"ImageFilename\"])\n", "danimg.head()"]}, {"cell_type": "code", "execution_count": 39, "id": "dad5186a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(8534, 1)"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["danimg.shape"]}, {"cell_type": "code", "execution_count": null, "id": "34cf1c8e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "98ead86f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "d5cc2ffb-e24c-4d3e-b4b1-b6512f85b4c8", "metadata": {}, "source": ["## NG26 Block"]}, {"cell_type": "code", "execution_count": 51, "id": "f9973b23", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>ImageFilename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050240_M0301361.jpg</td>\n", "      <td>6540dfe8e1ace1004c5535db</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050302_M0301372.jpg</td>\n", "      <td>6540dfebe1ace1004c5535dc</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050248_M0301365.jpg</td>\n", "      <td>6540dfede1ace1004c5535dd</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050338_M0301390.jpg</td>\n", "      <td>6540dff0e1ace1004c5535de</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050318_M0301380.jpg</td>\n", "      <td>6540dff3e1ace1004c5535df</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Task Name                   Task ID  \\\n", "0  NG26-01-L_34  6540f9228a297200456fceee   \n", "1  NG26-01-L_34  6540f9228a297200456fceee   \n", "2  NG26-01-L_34  6540f9228a297200456fceee   \n", "3  NG26-01-L_34  6540f9228a297200456fceee   \n", "4  NG26-01-L_34  6540f9228a297200456fceee   \n", "\n", "                               ImageFilename                  Image ID  \\\n", "0  KES22_IIM-L_20220924A-050240_M0301361.jpg  6540dfe8e1ace1004c5535db   \n", "1  KES22_IIM-L_20220924A-050302_M0301372.jpg  6540dfebe1ace1004c5535dc   \n", "2  KES22_IIM-L_20220924A-050248_M0301365.jpg  6540dfede1ace1004c5535dd   \n", "3  KES22_IIM-L_20220924A-050338_M0301390.jpg  6540dff0e1ace1004c5535de   \n", "4  KES22_IIM-L_20220924A-050318_M0301380.jpg  6540dff3e1ace1004c5535df   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                 True   \n", "1          4672         7008             NaN                 True   \n", "2          4672         7008             NaN                 True   \n", "3          4672         7008             NaN                 True   \n", "4          4672         7008             NaN                 True   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["##NG26\n", "homedir =\"/home/<USER>/Dropbox/Conservation/MWSLab-2023/scoutexports\"\n", "ngimgdf = pd.read_csv(homedir+\"/NG26scout-export-images.csv\")\n", "ngimgdf.head()"]}, {"cell_type": "code", "execution_count": 52, "id": "d4d4dfa0", "metadata": {}, "outputs": [{"data": {"text/plain": ["(10493, 11)"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["ngimgdf.shape"]}, {"cell_type": "code", "execution_count": 53, "id": "238db280", "metadata": {}, "outputs": [], "source": ["ng26img = ngimgdf[\"ImageFilename\"].unique()  #unique images on tasks"]}, {"cell_type": "code", "execution_count": 56, "id": "c122627d", "metadata": {}, "outputs": [], "source": ["ng26img = pd.DataFrame(ng26img,columns=[\"ImageFilename\"])"]}, {"cell_type": "code", "execution_count": 61, "id": "9edfa82a", "metadata": {}, "outputs": [{"data": {"text/plain": ["9938"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["ngimgdf[\"ImageFilename\"].nunique()"]}, {"cell_type": "code", "execution_count": 48, "id": "5f1ea42d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(10096, 1)"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["##ingested files\n", "homedir =\"/home/<USER>/Dropbox/Conservation/MWSLab-2023\"\n", "ng26ingestdf = pd.read_csv(homedir+\"/labriefs/_media/ng26ingested.csv\")\n", "ng26ingestdf.shape"]}, {"cell_type": "code", "execution_count": 62, "id": "b4b91943", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ImageFilename    10096\n", "missimg_img1     10096\n", "missimg_img2      9936\n", "dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "      <th>missimg_img1</th>\n", "      <th>missimg_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220924A-050238_M0301360.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220924A-050240_M0301361.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220924A-050242_M0301362.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220924A-050244_M0301363.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220924A-050246_M0301364.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename missimg_img1 missimg_img2\n", "0  KES22_IIM-L_20220924A-050238_M0301360.jpg     imageid1     imageid2\n", "1  KES22_IIM-L_20220924A-050240_M0301361.jpg     imageid1     imageid2\n", "2  KES22_IIM-L_20220924A-050242_M0301362.jpg     imageid1     imageid2\n", "3  KES22_IIM-L_20220924A-050244_M0301363.jpg     imageid1     imageid2\n", "4  KES22_IIM-L_20220924A-050246_M0301364.jpg     imageid1     imageid2"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["#find the difference 10096 - 9938 =158 images\n", "ng26ingestdf['missimg'] = 'imageid1'\n", "ng26img['missimg'] ='imageid2'\n", "\n", "\n", "merged_df = ng26ingestdf.merge(ng26img, on='ImageFilename',how='left', suffixes=('_img1','_img2'))\n", "print(merged_df.count())\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": 60, "id": "169c3b63", "metadata": {}, "outputs": [{"data": {"text/plain": ["ImageFilename    160\n", "missimg_img1     160\n", "missimg_img2       0\n", "dtype: int64"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["rows_not_in_tasks = merged_df[merged_df['missimg_img2'].isna()]\n", "\n", "\n", "rows_not_in_tasks.count()"]}, {"cell_type": "code", "execution_count": 63, "id": "c57243ac", "metadata": {}, "outputs": [], "source": ["rows_not_in_tasks.to_csv(\"MissingfilesNG26.csv\")"]}, {"cell_type": "markdown", "id": "8187c2f5", "metadata": {}, "source": ["## MT Block"]}, {"cell_type": "code", "execution_count": 2, "id": "f11515be", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070352_M0801356.jpg</td>\n", "      <td>654b3d440ff5c4004c9b6b56</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070504_M0801392.jpg</td>\n", "      <td>654b3d8e0ff5c4004c9b6b72</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070500_M0801390.jpg</td>\n", "      <td>654b3dab0ff5c4004c9b6b7d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070342_M0801351.jpg</td>\n", "      <td>654b3e2c0ff5c4004c9b6bae</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070354_M0801357.jpg</td>\n", "      <td>654b3e990ff5c4004c9b6bd7</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Task Name                   Task ID  \\\n", "0  MT02-L_46  654b524e47d0660045090326   \n", "1  MT02-L_46  654b524e47d0660045090326   \n", "2  MT02-L_46  654b524e47d0660045090326   \n", "3  MT02-L_46  654b524e47d0660045090326   \n", "4  MT02-L_46  654b524e47d0660045090326   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_WOT-L_20220909A-070352_M0801356.jpg  654b3d440ff5c4004c9b6b56   \n", "1  KES22_WOT-L_20220909A-070504_M0801392.jpg  654b3d8e0ff5c4004c9b6b72   \n", "2  KES22_WOT-L_20220909A-070500_M0801390.jpg  654b3dab0ff5c4004c9b6b7d   \n", "3  KES22_WOT-L_20220909A-070342_M0801351.jpg  654b3e2c0ff5c4004c9b6bae   \n", "4  KES22_WOT-L_20220909A-070354_M0801357.jpg  654b3e990ff5c4004c9b6bd7   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                False   \n", "1          4672         7008             NaN                False   \n", "2          4672         7008             NaN                False   \n", "3          4672         7008             NaN                False   \n", "4          4672         7008             NaN                False   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["homedir =\"/home/<USER>/Dropbox/Conservation/MWSLab-2023\"\n", "mtimg = pd.read_csv(homedir+\"/scoutexports/MTscout-export-images.csv\")\n", "mtimg.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "1299722a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(9278, 11)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimg.shape"]}, {"cell_type": "code", "execution_count": 4, "id": "91b1e95e", "metadata": {}, "outputs": [{"data": {"text/plain": ["8677"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimg[\"Image Filename\"].nunique()"]}, {"cell_type": "code", "execution_count": 7, "id": "c2238d8f", "metadata": {}, "outputs": [], "source": ["mtimgdf = mtimg[\"Image Filename\"].unique()  #unique images on tasks"]}, {"cell_type": "code", "execution_count": 8, "id": "d8408a21", "metadata": {}, "outputs": [], "source": ["mtimgdf = pd.DataFrame(mtimgdf,columns=[\"ImageFilename\"])"]}, {"cell_type": "code", "execution_count": 5, "id": "1f731f79", "metadata": {}, "outputs": [{"data": {"text/plain": ["(8975, 1)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["#ingested 8975, difference 8975-8677 = 298\n", "\n", "homedir =\"/home/<USER>/Dropbox/Conservation/MWSLab-2023\"\n", "mtingestdf = pd.read_csv(homedir+\"/labriefs/_media/MTingested.csv\")\n", "mtingestdf.shape"]}, {"cell_type": "code", "execution_count": 9, "id": "bff01b59", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ImageFilename    8975\n", "missimg_img1     8975\n", "missimg_img2     8663\n", "dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "      <th>missimg_img1</th>\n", "      <th>missimg_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_WOT-L_20220909A-053315_M0808646.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_WOT-L_20220909A-053317_M0808647.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_WOT-L_20220909A-053319_M0808648.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_WOT-L_20220909A-053321_M0808649.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_WOT-L_20220909A-053323_M0808650.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename missimg_img1 missimg_img2\n", "0  KES22_WOT-L_20220909A-053315_M0808646.jpg     imageid1          NaN\n", "1  KES22_WOT-L_20220909A-053317_M0808647.jpg     imageid1          NaN\n", "2  KES22_WOT-L_20220909A-053319_M0808648.jpg     imageid1          NaN\n", "3  KES22_WOT-L_20220909A-053321_M0808649.jpg     imageid1          NaN\n", "4  KES22_WOT-L_20220909A-053323_M0808650.jpg     imageid1          NaN"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["mtingestdf['missimg'] = 'imageid1'\n", "mtimgdf['missimg'] ='imageid2'\n", "\n", "\n", "merged_df = mtingestdf.merge(mtimgdf, on='ImageFilename',how='left', suffixes=('_img1','_img2'))\n", "print(merged_df.count())\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "00c10ad9", "metadata": {}, "outputs": [{"data": {"text/plain": ["ImageFilename    312\n", "missimg_img1     312\n", "missimg_img2       0\n", "dtype: int64"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["rows_not_in_tasks = merged_df[merged_df['missimg_img2'].isna()]\n", "\n", "\n", "rows_not_in_tasks.count()"]}, {"cell_type": "code", "execution_count": 11, "id": "9f50d239", "metadata": {}, "outputs": [], "source": ["rows_not_in_tasks.to_csv(\"MissingfilesMT.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "b657eb48", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "f711827e", "metadata": {}, "source": ["## SH Block"]}, {"cell_type": "code", "execution_count": 12, "id": "353683c6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060454_M0309398.jpg</td>\n", "      <td>653a4d3f4a2456004cd8a583</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060452_M0309397.jpg</td>\n", "      <td>653a4d414a2456004cd8a584</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060559_M0309430.jpg</td>\n", "      <td>653a4d444a2456004cd8a585</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060611_M0309436.jpg</td>\n", "      <td>653a4d474a2456004cd8a586</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060615_M0309438.jpg</td>\n", "      <td>653a4d494a2456004cd8a587</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Task Name                   Task ID  \\\n", "0  SH01-L_85  653a60f36f30500045ef6b69   \n", "1  SH01-L_85  653a60f36f30500045ef6b69   \n", "2  SH01-L_85  653a60f36f30500045ef6b69   \n", "3  SH01-L_85  653a60f36f30500045ef6b69   \n", "4  SH01-L_85  653a60f36f30500045ef6b69   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_IIM-L_20220904A-060454_M0309398.jpg  653a4d3f4a2456004cd8a583   \n", "1  KES22_IIM-L_20220904A-060452_M0309397.jpg  653a4d414a2456004cd8a584   \n", "2  KES22_IIM-L_20220904A-060559_M0309430.jpg  653a4d444a2456004cd8a585   \n", "3  KES22_IIM-L_20220904A-060611_M0309436.jpg  653a4d474a2456004cd8a586   \n", "4  KES22_IIM-L_20220904A-060615_M0309438.jpg  653a4d494a2456004cd8a587   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                False   \n", "1          4672         7008             NaN                False   \n", "2          4672         7008             NaN                False   \n", "3          4672         7008             NaN                False   \n", "4          4672         7008             NaN                False   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg = pd.read_csv(homedir+\"/scoutexports/SHscout-export-images.csv\")\n", "shimg.head()"]}, {"cell_type": "code", "execution_count": 13, "id": "12b4f764", "metadata": {}, "outputs": [{"data": {"text/plain": ["(5581, 11)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg.shape"]}, {"cell_type": "code", "execution_count": 14, "id": "74b9366b", "metadata": {}, "outputs": [{"data": {"text/plain": ["5232"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg[\"Image Filename\"].nunique()  "]}, {"cell_type": "code", "execution_count": 15, "id": "6950d573", "metadata": {}, "outputs": [], "source": ["shimgdf = shimg[\"Image Filename\"].unique()  #unique images on tasks"]}, {"cell_type": "code", "execution_count": 16, "id": "82b6444a", "metadata": {}, "outputs": [], "source": ["shimgdf = pd.DataFrame(shimgdf,columns=[\"ImageFilename\"])"]}, {"cell_type": "code", "execution_count": 18, "id": "bad3e269", "metadata": {}, "outputs": [{"data": {"text/plain": ["(5287, 1)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["#ingested 5287, difference 5287-5232 = 55\n", "\n", "homedir =\"/home/<USER>/Dropbox/Conservation/MWSLab-2023\"\n", "shingestdf = pd.read_csv(homedir+\"/labriefs/_media/SHingested.csv\")\n", "shingestdf.shape"]}, {"cell_type": "code", "execution_count": 19, "id": "2b651815", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ImageFilename    5287\n", "missimg_img1     5287\n", "missimg_img2     5232\n", "dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "      <th>missimg_img1</th>\n", "      <th>missimg_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220904A-060442_M0309392.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220904A-060444_M0309393.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220904A-060446_M0309394.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220904A-060448_M0309395.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220904A-060450_M0309396.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename missimg_img1 missimg_img2\n", "0  KES22_IIM-L_20220904A-060442_M0309392.jpg     imageid1     imageid2\n", "1  KES22_IIM-L_20220904A-060444_M0309393.jpg     imageid1     imageid2\n", "2  KES22_IIM-L_20220904A-060446_M0309394.jpg     imageid1     imageid2\n", "3  KES22_IIM-L_20220904A-060448_M0309395.jpg     imageid1     imageid2\n", "4  KES22_IIM-L_20220904A-060450_M0309396.jpg     imageid1     imageid2"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["shingestdf['missimg'] = 'imageid1'\n", "shimgdf['missimg'] ='imageid2'\n", "\n", "\n", "merged_df = shingestdf.merge(shimgdf, on='ImageFilename',how='left', suffixes=('_img1','_img2'))\n", "print(merged_df.count())\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": 20, "id": "4ddce956", "metadata": {}, "outputs": [{"data": {"text/plain": ["ImageFilename    55\n", "missimg_img1     55\n", "missimg_img2      0\n", "dtype: int64"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["rows_not_in_tasks = merged_df[merged_df['missimg_img2'].isna()]\n", "\n", "\n", "rows_not_in_tasks.count()"]}, {"cell_type": "code", "execution_count": 21, "id": "124b18f6", "metadata": {}, "outputs": [], "source": ["rows_not_in_tasks.to_csv(\"MissingfilesSH.csv\")"]}, {"cell_type": "markdown", "id": "75d582e1-c336-4376-9b4b-d70db9b6c5d7", "metadata": {}, "source": ["## NG25 block"]}, {"cell_type": "code", "execution_count": 7, "id": "2222749e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145114_M0804837.jpg</td>\n", "      <td>6525041633c806004cc2d5d5</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145144_M0804852.jpg</td>\n", "      <td>6525041933c806004cc2d5d6</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145158_M0804859.jpg</td>\n", "      <td>6525041c33c806004cc2d5d7</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145246_M0804883.jpg</td>\n", "      <td>6525041e33c806004cc2d5d8</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145236_M0804878.jpg</td>\n", "      <td>6525042133c806004cc2d5d9</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Task Name                   Task ID  \\\n", "0  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "1  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "2  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "3  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "4  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_WOT-L_20220923B-145114_M0804837.jpg  6525041633c806004cc2d5d5   \n", "1  KES22_WOT-L_20220923B-145144_M0804852.jpg  6525041933c806004cc2d5d6   \n", "2  KES22_WOT-L_20220923B-145158_M0804859.jpg  6525041c33c806004cc2d5d7   \n", "3  KES22_WOT-L_20220923B-145246_M0804883.jpg  6525041e33c806004cc2d5d8   \n", "4  KES22_WOT-L_20220923B-145236_M0804878.jpg  6525042133c806004cc2d5d9   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                False   \n", "1          4672         7008             NaN                False   \n", "2          4672         7008             NaN                False   \n", "3          4672         7008             NaN                False   \n", "4          4672         7008             NaN                False   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25img = pd.read_csv(homedir+\"/scoutexports/NG25scout-export-images.csv\")\n", "ng25img.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "0750f6c8", "metadata": {}, "outputs": [{"data": {"text/plain": ["(6338, 11)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25img.shape"]}, {"cell_type": "code", "execution_count": 18, "id": "60fee3d6", "metadata": {}, "outputs": [{"data": {"text/plain": ["3050"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25img[\"Image Filename\"].nunique()  "]}, {"cell_type": "code", "execution_count": 19, "id": "a7b9b439", "metadata": {}, "outputs": [], "source": ["ng25imgdf = ng25img[\"Image Filename\"].unique()  #unique images on tasks"]}, {"cell_type": "code", "execution_count": 20, "id": "cca47861-815d-40c1-a0de-fb4985b6ff6d", "metadata": {}, "outputs": [], "source": ["ng25imgdf = pd.DataFrame(ng25imgdf,columns=[\"ImageFilename\"])"]}, {"cell_type": "code", "execution_count": 21, "id": "0d5ab07e-efc7-48db-87c0-678923cc4f62", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_WOT-L_20220923B-145114_M0804837.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_WOT-L_20220923B-145144_M0804852.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_WOT-L_20220923B-145158_M0804859.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_WOT-L_20220923B-145246_M0804883.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_WOT-L_20220923B-145236_M0804878.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename\n", "0  KES22_WOT-L_20220923B-145114_M0804837.jpg\n", "1  KES22_WOT-L_20220923B-145144_M0804852.jpg\n", "2  KES22_WOT-L_20220923B-145158_M0804859.jpg\n", "3  KES22_WOT-L_20220923B-145246_M0804883.jpg\n", "4  KES22_WOT-L_20220923B-145236_M0804878.jpg"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25imgdf.head()"]}, {"cell_type": "code", "execution_count": 24, "id": "c194bb6a-49c4-4270-8e07-096322a01358", "metadata": {}, "outputs": [], "source": ["ng25imgdf.to_csv(\"NG25images.csv\")"]}, {"cell_type": "code", "execution_count": 7, "id": "b1b4b500-7464-4026-8505-69afba63f304", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_WOT-L_20220923B-143807_M0804445.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_WOT-L_20220923B-143809_M0804446.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_WOT-L_20220923B-143811_M0804447.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_WOT-L_20220923B-143813_M0804448.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_WOT-L_20220923B-143815_M0804449.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename\n", "0  KES22_WOT-L_20220923B-143807_M0804445.jpg\n", "1  KES22_WOT-L_20220923B-143809_M0804446.jpg\n", "2  KES22_WOT-L_20220923B-143811_M0804447.jpg\n", "3  KES22_WOT-L_20220923B-143813_M0804448.jpg\n", "4  KES22_WOT-L_20220923B-143815_M0804449.jpg"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25update = pd.read_csv(homedir+\"/labriefs/_media/NG25imagesupdate.csv\")\n", "ng25update.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "40f455ca-ed4a-4b3b-bc6d-99262e33709a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3050, 1)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25update.shape"]}, {"cell_type": "code", "execution_count": 9, "id": "61d638b8-b485-450b-86e5-fa134496b595", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3050, 1)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["#ingested 3050, difference 0\n", "\n", "\n", "ng25ingestdf = pd.read_csv(homedir+\"/labriefs/_media/NG25ingested.csv\")\n", "ng25ingestdf.shape"]}, {"cell_type": "code", "execution_count": 23, "id": "ee786df2-d8df-484f-b720-ad3ccbff427b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_WOT-L_20220923B-140119_M0803345.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_WOT-L_20220923B-140122_M0803346.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_WOT-L_20220923B-140124_M0803347.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_WOT-L_20220923B-140126_M0803348.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_WOT-L_20220923B-140128_M0803349.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename\n", "0  KES22_WOT-L_20220923B-140119_M0803345.jpg\n", "1  KES22_WOT-L_20220923B-140122_M0803346.jpg\n", "2  KES22_WOT-L_20220923B-140124_M0803347.jpg\n", "3  KES22_WOT-L_20220923B-140126_M0803348.jpg\n", "4  KES22_WOT-L_20220923B-140128_M0803349.jpg"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25ingestdf.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "609d0ad1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ImageFilename    3050\n", "missimg_img1     3050\n", "missimg_img2     3050\n", "dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "      <th>missimg_img1</th>\n", "      <th>missimg_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_WOT-L_20220923B-140119_M0803345.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_WOT-L_20220923B-140122_M0803346.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_WOT-L_20220923B-140124_M0803347.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_WOT-L_20220923B-140126_M0803348.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_WOT-L_20220923B-140128_M0803349.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename missimg_img1 missimg_img2\n", "0  KES22_WOT-L_20220923B-140119_M0803345.jpg     imageid1     imageid2\n", "1  KES22_WOT-L_20220923B-140122_M0803346.jpg     imageid1     imageid2\n", "2  KES22_WOT-L_20220923B-140124_M0803347.jpg     imageid1     imageid2\n", "3  KES22_WOT-L_20220923B-140126_M0803348.jpg     imageid1     imageid2\n", "4  KES22_WOT-L_20220923B-140128_M0803349.jpg     imageid1     imageid2"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25ingestdf['missimg'] = 'imageid1'\n", "ng25update['missimg'] ='imageid2'\n", "\n", "\n", "merged_df = ng25ingestdf.merge(ng25update, on='ImageFilename',how='left', suffixes=('_img1','_img2'))\n", "print(merged_df.count())\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "fe9b8fbe-abae-4dbb-ab4d-e0daf55b94d9", "metadata": {}, "outputs": [{"data": {"text/plain": ["ImageFilename    0\n", "missimg_img1     0\n", "missimg_img2     0\n", "dtype: int64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["rows_not_in_tasks = merged_df[merged_df['missimg_img2'].isna()]\n", "\n", "\n", "rows_not_in_tasks.count()"]}, {"cell_type": "code", "execution_count": 12, "id": "95c05f85-9a51-4bba-9965-7c92773f2ba4", "metadata": {}, "outputs": [], "source": ["rows_not_in_tasks.to_csv(\"MissingfilesNG25.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "8cb7dc46-1fa9-47a2-9138-b5f2fbbb7291", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6b9a9c36-ea62-409a-9f4c-f42732eea774", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b6b2704f-5135-4a06-9e45-48e3d03ec6f4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "abb50178-b486-4be3-97b6-b323d9f7ac1b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ef3ed608", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}