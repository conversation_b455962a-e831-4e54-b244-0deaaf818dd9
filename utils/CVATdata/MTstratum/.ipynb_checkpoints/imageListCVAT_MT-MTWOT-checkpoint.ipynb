{"cells": [{"cell_type": "markdown", "id": "ff9ed367", "metadata": {}, "source": ["Divide stratum to left and right"]}, {"cell_type": "code", "execution_count": 1, "id": "8daf5dd7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "b489dc6c-8845-4df1-9680-2fd060786fc4", "metadata": {}, "outputs": [], "source": ["coco_file_path = \"/home/<USER>/Dropbox/Conservation/MWS_LabArusha/annotations/exports/MTleft.json\""]}, {"cell_type": "code", "execution_count": 3, "id": "23b685d4", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "images_df = pd.DataFrame(coco_data['images'])\n"]}, {"cell_type": "code", "execution_count": 6, "id": "4a1f8fd3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(296, 6)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.shape"]}, {"cell_type": "code", "execution_count": 7, "id": "8e9230e2", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-070510_M0801395.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053746_M0808781.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054013_M0808854.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054205_M0808910.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   1   7008    4672  KES22_WOT-L_20220909A-070510_M0801395.jpg        1   \n", "1   2   7008    4672  KES22_WOT-L_20220909A-053748_M0808782.jpg        1   \n", "2   3   7008    4672  KES22_WOT-L_20220909A-053746_M0808781.jpg        1   \n", "3   4   7008    4672  KES22_WOT-L_20220909A-054013_M0808854.jpg        1   \n", "4   5   7008    4672  KES22_WOT-L_20220909A-054205_M0808910.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-09  \n", "1    2022-09-09  \n", "2    2022-09-09  \n", "3    2022-09-09  \n", "4    2022-09-09  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "60719a18", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 8, "id": "28706da0", "metadata": {}, "outputs": [], "source": ["pattern1 = \"KES22_WOT-L_\""]}, {"cell_type": "code", "execution_count": 10, "id": "4e4444a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(59, 6)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "MTleftposve = images_df[images_df['file_name'].str.contains(pattern1, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "MTleftposve.shape"]}, {"cell_type": "code", "execution_count": 11, "id": "6179df13-2124-4328-8245-d000ee2c1bf6", "metadata": {}, "outputs": [], "source": ["MTleftposve.to_csv(\"MTonlyleftimages.csv\")"]}, {"cell_type": "code", "execution_count": 12, "id": "060a581a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-070510_M0801395.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053746_M0808781.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054013_M0808854.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054205_M0808910.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   1   7008    4672  KES22_WOT-L_20220909A-070510_M0801395.jpg        1   \n", "1   2   7008    4672  KES22_WOT-L_20220909A-053748_M0808782.jpg        1   \n", "2   3   7008    4672  KES22_WOT-L_20220909A-053746_M0808781.jpg        1   \n", "3   4   7008    4672  KES22_WOT-L_20220909A-054013_M0808854.jpg        1   \n", "4   5   7008    4672  KES22_WOT-L_20220909A-054205_M0808910.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-09  \n", "1    2022-09-09  \n", "2    2022-09-09  \n", "3    2022-09-09  \n", "4    2022-09-09  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftposve.head()"]}, {"cell_type": "code", "execution_count": 13, "id": "594ad7b9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>55</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-071755_M0801776.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>56</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-072605_M0802020.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>57</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-073050_M0802162.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>58</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-073052_M0802163.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>59</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-073142_M0802188.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "54  55   7008    4672  KES22_WOT-L_20220909A-071755_M0801776.jpg        1   \n", "55  56   7008    4672  KES22_WOT-L_20220909A-072605_M0802020.jpg        1   \n", "56  57   7008    4672  KES22_WOT-L_20220909A-073050_M0802162.jpg        1   \n", "57  58   7008    4672  KES22_WOT-L_20220909A-073052_M0802163.jpg        1   \n", "58  59   7008    4672  KES22_WOT-L_20220909A-073142_M0802188.jpg        1   \n", "\n", "   date_captured  \n", "54    2022-09-09  \n", "55    2022-09-09  \n", "56    2022-09-09  \n", "57    2022-09-09  \n", "58    2022-09-09  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftposve.tail()"]}, {"cell_type": "code", "execution_count": 14, "id": "164fc814", "metadata": {}, "outputs": [], "source": ["MTleftsorted = MTleftposve.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 15, "id": "932d181b-abf7-45cb-b26d-c73c7d9163f0", "metadata": {}, "outputs": [], "source": ["MTleftsorted.to_csv(\"MTonlyleftimages.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 16, "id": "7bc344e3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053746_M0808781.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054011_M0808853.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054013_M0808854.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054203_M0808909.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054205_M0808910.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>8</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054800_M0809087.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>9</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054819_M0809096.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>10</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-055025_M0809159.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "2   3   7008    4672  KES22_WOT-L_20220909A-053746_M0808781.jpg        1   \n", "1   2   7008    4672  KES22_WOT-L_20220909A-053748_M0808782.jpg        1   \n", "5   6   7008    4672  KES22_WOT-L_20220909A-054011_M0808853.jpg        1   \n", "3   4   7008    4672  KES22_WOT-L_20220909A-054013_M0808854.jpg        1   \n", "6   7   7008    4672  KES22_WOT-L_20220909A-054203_M0808909.jpg        1   \n", "4   5   7008    4672  KES22_WOT-L_20220909A-054205_M0808910.jpg        1   \n", "7   8   7008    4672  KES22_WOT-L_20220909A-054800_M0809087.jpg        1   \n", "8   9   7008    4672  KES22_WOT-L_20220909A-054819_M0809096.jpg        1   \n", "9  10   7008    4672  KES22_WOT-L_20220909A-055025_M0809159.jpg        1   \n", "\n", "  date_captured  \n", "2    2022-09-09  \n", "1    2022-09-09  \n", "5    2022-09-09  \n", "3    2022-09-09  \n", "6    2022-09-09  \n", "4    2022-09-09  \n", "7    2022-09-09  \n", "8    2022-09-09  \n", "9    2022-09-09  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftsorted.head(9)"]}, {"cell_type": "code", "execution_count": 36, "id": "97e19225-b15b-427e-a15f-70d7d814cf48", "metadata": {}, "outputs": [], "source": ["MTleftsorted[\"file_name\"].to_csv(\"MTleftpositive.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 17, "id": "07d3b903", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>ImageFilename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070352_M0801356.jpg</td>\n", "      <td>654b3d440ff5c4004c9b6b56</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070504_M0801392.jpg</td>\n", "      <td>654b3d8e0ff5c4004c9b6b72</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070500_M0801390.jpg</td>\n", "      <td>654b3dab0ff5c4004c9b6b7d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070342_M0801351.jpg</td>\n", "      <td>654b3e2c0ff5c4004c9b6bae</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070354_M0801357.jpg</td>\n", "      <td>654b3e990ff5c4004c9b6bd7</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Task Name                   Task ID  \\\n", "0  MT02-L_46  654b524e47d0660045090326   \n", "1  MT02-L_46  654b524e47d0660045090326   \n", "2  MT02-L_46  654b524e47d0660045090326   \n", "3  MT02-L_46  654b524e47d0660045090326   \n", "4  MT02-L_46  654b524e47d0660045090326   \n", "\n", "                               ImageFilename                  Image ID  \\\n", "0  KES22_WOT-L_20220909A-070352_M0801356.jpg  654b3d440ff5c4004c9b6b56   \n", "1  KES22_WOT-L_20220909A-070504_M0801392.jpg  654b3d8e0ff5c4004c9b6b72   \n", "2  KES22_WOT-L_20220909A-070500_M0801390.jpg  654b3dab0ff5c4004c9b6b7d   \n", "3  KES22_WOT-L_20220909A-070342_M0801351.jpg  654b3e2c0ff5c4004c9b6bae   \n", "4  KES22_WOT-L_20220909A-070354_M0801357.jpg  654b3e990ff5c4004c9b6bd7   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                False   \n", "1          4672         7008             NaN                False   \n", "2          4672         7008             NaN                False   \n", "3          4672         7008             NaN                False   \n", "4          4672         7008             NaN                False   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["#imgpath = \"/home/<USER>/Dropbox/Conservation/MWS_LabArusha/annotations/exports/MTscout-export-images.csv\"\n", "imgpath = \"/home/<USER>/Dropbox/Conservation/MWS_LabArusha/annotations/exports/MTscout-export-images.csv\"\n", "mtimgs = pd.read_csv(imgpath)\n", "mtimgs.head()"]}, {"cell_type": "code", "execution_count": 18, "id": "87ff32c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(9278, 11)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimgs.shape"]}, {"cell_type": "code", "execution_count": 19, "id": "fbff7df6", "metadata": {}, "outputs": [{"data": {"text/plain": ["8677"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimgs[\"ImageFilename\"].nunique()"]}, {"cell_type": "code", "execution_count": 20, "id": "6b578304", "metadata": {}, "outputs": [], "source": ["mtimgsunique = mtimgs[\"ImageFilename\"].unique()"]}, {"cell_type": "code", "execution_count": 21, "id": "7675cd62", "metadata": {}, "outputs": [{"data": {"text/plain": ["8677"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["len(mtimgsunique)"]}, {"cell_type": "code", "execution_count": 22, "id": "c1e7f0f8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_WOT-L_20220909A-070352_M0801356.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_WOT-L_20220909A-070504_M0801392.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_WOT-L_20220909A-070500_M0801390.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_WOT-L_20220909A-070342_M0801351.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_WOT-L_20220909A-070354_M0801357.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   file_name\n", "0  KES22_WOT-L_20220909A-070352_M0801356.jpg\n", "1  KES22_WOT-L_20220909A-070504_M0801392.jpg\n", "2  KES22_WOT-L_20220909A-070500_M0801390.jpg\n", "3  KES22_WOT-L_20220909A-070342_M0801351.jpg\n", "4  KES22_WOT-L_20220909A-070354_M0801357.jpg"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimgsunique = pd.DataFrame(mtimgsunique,columns=[\"file_name\"])\n", "mtimgsunique.head()"]}, {"cell_type": "code", "execution_count": 24, "id": "67950dd6", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1912, 1)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimgsuniqueleft = mtimgsunique[mtimgsunique['file_name'].str.contains(pattern1, case=False, na=False, regex=True)]\n", "# Print the resulting filtered DataFrame\n", "mtimgsuniqueleft.shape"]}, {"cell_type": "code", "execution_count": 25, "id": "0f086fec", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1907</th>\n", "      <td>KES22_WOT-L_20220909A-073206_M0802200.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1908</th>\n", "      <td>KES22_WOT-L_20220909A-073030_M0802152.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1909</th>\n", "      <td>KES22_WOT-L_20220909A-073106_M0802170.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1910</th>\n", "      <td>KES22_WOT-L_20220909A-073042_M0802158.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1911</th>\n", "      <td>KES22_WOT-L_20220909A-073110_M0802172.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      file_name\n", "1907  KES22_WOT-L_20220909A-073206_M0802200.jpg\n", "1908  KES22_WOT-L_20220909A-073030_M0802152.jpg\n", "1909  KES22_WOT-L_20220909A-073106_M0802170.jpg\n", "1910  KES22_WOT-L_20220909A-073042_M0802158.jpg\n", "1911  KES22_WOT-L_20220909A-073110_M0802172.jpg"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimgsuniqueleft.tail()"]}, {"cell_type": "code", "execution_count": 26, "id": "247cec3a-dece-43d8-ab76-c7ff9b1a5244", "metadata": {}, "outputs": [], "source": ["MTleftotalsorted = mtimgsuniqueleft.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 27, "id": "6cc04368-35fb-4d16-a052-218249487290", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>KES22_WOT-L_20220909A-053650_M0808753.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>KES22_WOT-L_20220909A-053652_M0808754.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>KES22_WOT-L_20220909A-053654_M0808755.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>135</th>\n", "      <td>KES22_WOT-L_20220909A-053656_M0808756.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>KES22_WOT-L_20220909A-053658_M0808757.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                     file_name\n", "94   KES22_WOT-L_20220909A-053650_M0808753.jpg\n", "60   KES22_WOT-L_20220909A-053652_M0808754.jpg\n", "78   KES22_WOT-L_20220909A-053654_M0808755.jpg\n", "135  KES22_WOT-L_20220909A-053656_M0808756.jpg\n", "108  KES22_WOT-L_20220909A-053658_M0808757.jpg"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftotalsorted.head()"]}, {"cell_type": "code", "execution_count": 28, "id": "2b99b790-5318-4b36-a53a-16d3756b4037", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in MTleftsorted[\"file_name\"]:\n", "    if item in MTleftotalsorted[\"file_name\"].values:\n", "        index_in_pos = MTleftotalsorted[\"file_name\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(MTleftotalsorted[\"file_name\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(MTleftotalsorted[\"file_name\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 29, "id": "2be9136b-e16e-42d9-b00a-93ec347ddb84", "metadata": {}, "outputs": [{"data": {"text/plain": ["177"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 30, "id": "d7696c45-bfd9-436c-afda-4239c5007d50", "metadata": {}, "outputs": [{"data": {"text/plain": ["133"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 31, "id": "008a9c33-b2e3-4b6f-875e-1a9b32e5f81b", "metadata": {}, "outputs": [], "source": ["cvatimg = pd.DataFrame(fin,columns=[\"cvatfilename\"])"]}, {"cell_type": "code", "execution_count": 32, "id": "8a77236e-0e42-482c-892a-0504f417334f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>101</th>\n", "      <td>KES22_WOT-L_20220909A-053744_M0808780.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>KES22_WOT-L_20220909A-053746_M0808781.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110</th>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>KES22_WOT-L_20220909A-053750_M0808783.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>113</th>\n", "      <td>KES22_WOT-L_20220909A-054009_M0808852.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  c<PERSON><PERSON><PERSON><PERSON>\n", "101  KES22_WOT-L_20220909A-053744_M0808780.jpg\n", "99   KES22_WOT-L_20220909A-053746_M0808781.jpg\n", "110  KES22_WOT-L_20220909A-053748_M0808782.jpg\n", "80   KES22_WOT-L_20220909A-053750_M0808783.jpg\n", "113  KES22_WOT-L_20220909A-054009_M0808852.jpg"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin = cvatimg.sort_values(by=[\"cvatfilename\"],ascending=True)\n", "cvatfin.head()"]}, {"cell_type": "code", "execution_count": 33, "id": "ffdef2e9-a764-40b6-9132-94f29afa7e82", "metadata": {}, "outputs": [{"data": {"text/plain": ["(133, 1)"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin.shape"]}, {"cell_type": "code", "execution_count": 34, "id": "afc94256-61ff-436c-a8a0-976acc8ab884", "metadata": {}, "outputs": [], "source": ["cvatfin.to_csv(\"MTonlyleftcvat.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "c2d70828-12c5-45b1-a911-9dc64154f5dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c2687fbc-6339-428e-9d54-751e512b042f", "metadata": {}, "outputs": [], "source": ["#MT Right"]}, {"cell_type": "code", "execution_count": null, "id": "8ddae76b-c500-4d2d-958d-e20286f9ee1e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 36, "id": "1ca074ad", "metadata": {}, "outputs": [], "source": ["pattern2 = \"KES22_WOT-R_\""]}, {"cell_type": "code", "execution_count": 38, "id": "1e9aefc9", "metadata": {}, "outputs": [{"data": {"text/plain": ["(91, 6)"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "MTrightposve = images_df[images_df['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "MTrightposve.shape"]}, {"cell_type": "code", "execution_count": 39, "id": "3ad7fa26", "metadata": {}, "outputs": [], "source": ["MTrightsorted = MTrightposve.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 40, "id": "786e514b-1138-4c25-a57f-d0dd97b46ca2", "metadata": {}, "outputs": [], "source": ["MTrightsorted.to_csv(\"MTonlyrightimagespos.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 41, "id": "5fd9fbb4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>60</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220909A-053330_M0707703.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>61</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220909A-053927_M0707881.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>62</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220909A-053936_M0707885.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>65</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220909A-054030_M0707912.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>66</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220909A-054032_M0707913.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "59  60   7008    4672  KES22_WOT-R_20220909A-053330_M0707703.jpg        1   \n", "60  61   7008    4672  KES22_WOT-R_20220909A-053927_M0707881.jpg        1   \n", "61  62   7008    4672  KES22_WOT-R_20220909A-053936_M0707885.jpg        1   \n", "64  65   7008    4672  KES22_WOT-R_20220909A-054030_M0707912.jpg        1   \n", "65  66   7008    4672  KES22_WOT-R_20220909A-054032_M0707913.jpg        1   \n", "\n", "   date_captured  \n", "59    2022-09-09  \n", "60    2022-09-09  \n", "61    2022-09-09  \n", "64    2022-09-09  \n", "65    2022-09-09  "]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["MTrightsorted.head()"]}, {"cell_type": "code", "execution_count": 42, "id": "64d5e89c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2025, 1)"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimgsuniqueright = mtimgsunique[mtimgsunique['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "mtimgsuniqueright.shape"]}, {"cell_type": "code", "execution_count": 43, "id": "bccea1bf-8ba4-4731-b395-f9884d37d808", "metadata": {}, "outputs": [], "source": ["MTrightotalsorted = mtimgsuniqueright.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 44, "id": "557fa857-7da4-489d-9f5c-8db5342e9fac", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1955</th>\n", "      <td>KES22_WOT-R_20220909A-053316_M0707696.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1915</th>\n", "      <td>KES22_WOT-R_20220909A-053318_M0707697.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1912</th>\n", "      <td>KES22_WOT-R_20220909A-053320_M0707698.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1930</th>\n", "      <td>KES22_WOT-R_20220909A-053322_M0707699.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1917</th>\n", "      <td>KES22_WOT-R_20220909A-053324_M0707700.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      file_name\n", "1955  KES22_WOT-R_20220909A-053316_M0707696.jpg\n", "1915  KES22_WOT-R_20220909A-053318_M0707697.jpg\n", "1912  KES22_WOT-R_20220909A-053320_M0707698.jpg\n", "1930  KES22_WOT-R_20220909A-053322_M0707699.jpg\n", "1917  KES22_WOT-R_20220909A-053324_M0707700.jpg"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["MTrightotalsorted.head()"]}, {"cell_type": "code", "execution_count": 45, "id": "b879b12b-d248-4bc7-baa7-f9119aee8f71", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in MTrightsorted[\"file_name\"]:\n", "    if item in MTrightotalsorted[\"file_name\"].values:\n", "        index_in_pos = MTrightotalsorted[\"file_name\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(MTrightotalsorted[\"file_name\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(MTrightotalsorted[\"file_name\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 46, "id": "db6f4e5d-fbb6-46a9-9bee-8476dbe5cbbb", "metadata": {}, "outputs": [{"data": {"text/plain": ["273"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 47, "id": "7badf276-6239-486d-b2ba-9cb2a2c3bcef", "metadata": {}, "outputs": [{"data": {"text/plain": ["206"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 48, "id": "82318eab-c30a-427e-96de-befaea605c12", "metadata": {}, "outputs": [], "source": ["cvatimg = pd.DataFrame(fin,columns=[\"cvatfilename\"])"]}, {"cell_type": "code", "execution_count": 49, "id": "efc73194-3fdb-436e-bcb9-09a25d0b2c1d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>185</th>\n", "      <td>KES22_WOT-R_20220909A-053328_M0707702.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_WOT-R_20220909A-053330_M0707703.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>KES22_WOT-R_20220909A-053332_M0707704.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>KES22_WOT-R_20220909A-053925_M0707880.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>173</th>\n", "      <td>KES22_WOT-R_20220909A-053927_M0707881.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  c<PERSON><PERSON><PERSON><PERSON>\n", "185  KES22_WOT-R_20220909A-053328_M0707702.jpg\n", "0    KES22_WOT-R_20220909A-053330_M0707703.jpg\n", "64   KES22_WOT-R_20220909A-053332_M0707704.jpg\n", "100  KES22_WOT-R_20220909A-053925_M0707880.jpg\n", "173  KES22_WOT-R_20220909A-053927_M0707881.jpg"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin = cvatimg.sort_values(by=[\"cvatfilename\"],ascending=True)\n", "cvatfin.head()"]}, {"cell_type": "code", "execution_count": 50, "id": "67455f80-c824-4529-b515-437b119701c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(206, 1)"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin.shape"]}, {"cell_type": "code", "execution_count": 51, "id": "dc41c311-5c01-4556-8eca-b38590211628", "metadata": {}, "outputs": [], "source": ["cvatfin.to_csv(\"MTonlyrightcvat.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "a9973fe2-38f9-4102-8131-4cc4d2db4c4e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b16ac600-595c-47fe-ba9a-b7c7d29f7111", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5259e8b4-d426-487d-91fd-92baeb3e1556", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "cc806d4c-c697-48e4-89e3-129958131725", "metadata": {}, "source": ["Annotations"]}, {"cell_type": "code", "execution_count": 4, "id": "98589797", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "annotationsdf = pd.DataFrame(coco_data['annotations'])\n"]}, {"cell_type": "code", "execution_count": 5, "id": "9ab9d2b2-f241-4195-a1b0-99e7e1ff6688", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>3531.029913</td>\n", "      <td>[3136.4668019044, 1525.14141578463, -60.966167...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>5315.129237</td>\n", "      <td>[3136.4668019044, 1576.96265824868, 67.0627843...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>12456.356031</td>\n", "      <td>[1456.64331726931, 1710.85830811493, 115.45772...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>20377.265781</td>\n", "      <td>[1346.86384096244, 1915.27526399669, 149.52721...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>4413.641252</td>\n", "      <td>[1519.10405378873, 1877.42027216673, 52.996988...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1           14           []   3531.029913   \n", "1   2         1           14           []   5315.129237   \n", "2   3         2            9           []  12456.356031   \n", "3   4         2            9           []  20377.265781   \n", "4   5         2            9           []   4413.641252   \n", "\n", "                                                bbox  iscrowd  \n", "0  [3136.4668019044, 1525.14141578463, -60.966167...        0  \n", "1  [3136.4668019044, 1576.96265824868, 67.0627843...        0  \n", "2  [1456.64331726931, 1710.85830811493, 115.45772...        0  \n", "3  [1346.86384096244, 1915.27526399669, 149.52721...        0  \n", "4  [1519.10405378873, 1877.42027216673, 52.996988...        0  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "e4193fb0-f31a-4583-aee1-5466a6d9ff32", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053746_M0808781.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054011_M0808853.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054013_M0808854.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>7</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054203_M0808909.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   3   7008    4672  KES22_WOT-L_20220909A-053746_M0808781.jpg        1   \n", "1   2   7008    4672  KES22_WOT-L_20220909A-053748_M0808782.jpg        1   \n", "2   6   7008    4672  KES22_WOT-L_20220909A-054011_M0808853.jpg        1   \n", "3   4   7008    4672  KES22_WOT-L_20220909A-054013_M0808854.jpg        1   \n", "4   7   7008    4672  KES22_WOT-L_20220909A-054203_M0808909.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-09  \n", "1    2022-09-09  \n", "2    2022-09-09  \n", "3    2022-09-09  \n", "4    2022-09-09  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftsorted = pd.read_csv(homedir+\"/MTstratum/MTonlyleftimages.csv\")\n", "MTleftsorted.head()"]}, {"cell_type": "code", "execution_count": 9, "id": "a8ad9f17-01f2-48aa-b76f-0f35d3009a2a", "metadata": {}, "outputs": [], "source": ["MTleftsorted=MTleftsorted.sort_values(by=[\"id\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 10, "id": "fd7c2b81-3bad-49b8-98c7-9bd405abc281", "metadata": {}, "outputs": [], "source": ["# Compare image 'ID' columns and return annotations \n", "result_values = annotationsdf[annotationsdf['image_id'].isin(MTleftsorted['id'])]"]}, {"cell_type": "code", "execution_count": 11, "id": "dc2d2883-f90f-4b14-8e97-23bed48462ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["(483, 7)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values.shape"]}, {"cell_type": "code", "execution_count": 12, "id": "a013ab74-3c01-47b8-8c87-83c9ddd2ac7c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2414, 7)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.shape"]}, {"cell_type": "code", "execution_count": 13, "id": "c57c8809-7d45-470d-8e25-4f583132f5b7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>3531.029913</td>\n", "      <td>[3136.4668019044, 1525.14141578463, -60.966167...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>5315.129237</td>\n", "      <td>[3136.4668019044, 1576.96265824868, 67.0627843...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>12456.356031</td>\n", "      <td>[1456.64331726931, 1710.85830811493, 115.45772...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>20377.265781</td>\n", "      <td>[1346.86384096244, 1915.27526399669, 149.52721...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>4413.641252</td>\n", "      <td>[1519.10405378873, 1877.42027216673, 52.996988...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1           14           []   3531.029913   \n", "1   2         1           14           []   5315.129237   \n", "2   3         2            9           []  12456.356031   \n", "3   4         2            9           []  20377.265781   \n", "4   5         2            9           []   4413.641252   \n", "\n", "                                                bbox  iscrowd  \n", "0  [3136.4668019044, 1525.14141578463, -60.966167...        0  \n", "1  [3136.4668019044, 1576.96265824868, 67.0627843...        0  \n", "2  [1456.64331726931, 1710.85830811493, 115.45772...        0  \n", "3  [1346.86384096244, 1915.27526399669, 149.52721...        0  \n", "4  [1519.10405378873, 1877.42027216673, 52.996988...        0  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values.head()"]}, {"cell_type": "code", "execution_count": 60, "id": "c8e87cea-2d9f-4ee4-b71f-1eaaced692da", "metadata": {}, "outputs": [], "source": ["result_values.to_csv(\"MTonlyleftannotations.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 7, "id": "bd6d3d9c", "metadata": {}, "outputs": [], "source": ["homedir = \"/home/<USER>/Dropbox/Conservation/MWSLab-2023/utils/CVATdata\""]}, {"cell_type": "code", "execution_count": 23, "id": "fd2ab339-e6e6-456a-a121-214cb260ac04", "metadata": {}, "outputs": [{"data": {"text/plain": ["0    [3136.4668019044, 1525.14141578463, -60.966167...\n", "1    [3136.4668019044, 1576.96265824868, 67.0627843...\n", "2    [1456.64331726931, 1710.85830811493, 115.45772...\n", "3    [1346.86384096244, 1915.27526399669, 149.52721...\n", "4    [1519.10405378873, 1877.42027216673, 52.996988...\n", "Name: bbox, dtype: object"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values['bbox'].head() #change -ve to +ve"]}, {"cell_type": "code", "execution_count": 24, "id": "bb221425", "metadata": {}, "outputs": [{"data": {"text/plain": ["[3136.4668019044, 1525.14141578463, -60.9661676, 57.9178592245257]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["x = result_values['bbox'][0]\n", "x"]}, {"cell_type": "code", "execution_count": 25, "id": "959a750a", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1456.64331726931, 1710.85830811493, 115.457725081363, -107.8867267]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values['bbox'][2]"]}, {"cell_type": "code", "execution_count": 26, "id": "ac40a197", "metadata": {}, "outputs": [], "source": ["x[-2] = abs(x[-2])\n", "x[0] = x[0] + int(x[-2])"]}, {"cell_type": "code", "execution_count": 27, "id": "6cd7c2b4", "metadata": {}, "outputs": [{"data": {"text/plain": ["[3196.4668019044, 1525.14141578463, 60.9661676, 57.9178592245257]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["x"]}, {"cell_type": "code", "execution_count": 33, "id": "4ea3306b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1456.64331726931, 1710.85830811493, 115.457725081363, -107.8867267]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values['bbox'].values[2]"]}, {"cell_type": "code", "execution_count": 38, "id": "c57ceb0f", "metadata": {}, "outputs": [{"data": {"text/plain": ["0    [3196.4668019044, 1525.14141578463, 60.9661676...\n", "1    [3136.4668019044, 1576.96265824868, 67.0627843...\n", "2    [1456.64331726931, 1710.85830811493, 115.45772...\n", "3    [1346.86384096244, 1915.27526399669, 149.52721...\n", "4    [1519.10405378873, 1877.42027216673, 52.996988...\n", "Name: bbox, dtype: object"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["test = result_values[\"bbox\"][0:5]\n", "test"]}, {"cell_type": "code", "execution_count": 41, "id": "0d405e61", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[3196.4668019044, 1525.14141578463, 60.9661676, 57.9178592245257]\n", "[3136.4668019044, 1576.96265824868, 67.0627843652402, 79.25601789]\n", "[1456.64331726931, 1710.85830811493, 115.457725081363, -107.8867267]\n", "[1346.86384096244, 1915.27526399669, 149.527217728322, -136.2779706]\n", "[1519.10405378873, 1877.42027216673, 52.996988561937, -83.28098203]\n"]}], "source": ["for item in test:\n", "    print(item)"]}, {"cell_type": "code", "execution_count": 53, "id": "1a7b4779", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3196.4668019044\n", "1525.14141578463\n", "60.9661676\n", "57.9178592245257\n", "3136.4668019044\n", "1576.96265824868\n", "67.0627843652402\n", "79.25601789\n", "1456.64331726931\n", "1710.85830811493\n", "115.457725081363\n", "-107.8867267\n", "1346.86384096244\n", "1915.27526399669\n", "149.527217728322\n", "-136.2779706\n", "1519.10405378873\n", "1877.42027216673\n", "52.996988561937\n", "-83.28098203\n"]}], "source": ["for item in test:\n", "    for i in item:\n", "        print(i)"]}, {"cell_type": "code", "execution_count": 58, "id": "454c8f06", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1.5, 2.0, 3.7, 0.5]\n"]}], "source": ["real_numbers = [1.5, 2.0, 3.7, 0.5, 0.0]\n", "greater_than_zero = [num for num in real_numbers if num > 0]\n", "\n", "print(greater_than_zero) "]}, {"cell_type": "code", "execution_count": 56, "id": "41ca8e9f", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'>' not supported between instances of 'list' and 'int'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[56], line 5\u001b[0m\n\u001b[1;32m      1\u001b[0m bboxpos \u001b[38;5;241m=\u001b[39m[]\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m item \u001b[38;5;129;01min\u001b[39;00m test:\n\u001b[0;32m----> 5\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mitem\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m>\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m0\u001b[39;49m:\n\u001b[1;32m      6\u001b[0m         bboxpos\u001b[38;5;241m.\u001b[39mappend(item)\n\u001b[1;32m      8\u001b[0m \u001b[38;5;124;03m''' elif item[-2]<0:\u001b[39;00m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;124;03m        item[-2] = abs(item[-2])\u001b[39;00m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;124;03m        item[0]  = item[0] + int(abs(item[-2]))\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     26\u001b[0m \u001b[38;5;124;03m        bboxpos.append(item)\u001b[39;00m\n\u001b[1;32m     27\u001b[0m \u001b[38;5;124;03m    '''\u001b[39;00m\n", "\u001b[0;31mTypeError\u001b[0m: '>' not supported between instances of 'list' and 'int'"]}], "source": ["bboxpos =[]\n", "\n", "for item in test:\n", "    greater_than_zero = [num for num in item if num > 0]\n", "    if item > 0:\n", "        bboxpos.append(item)\n", "        \n", "''' elif item[-2]<0:\n", "        item[-2] = abs(item[-2])\n", "        item[0]  = item[0] + int(abs(item[-2]))\n", "        bboxpos.append(item)\n", "    \n", "    elif item[0]<0:\n", "        item[0] = abs(item[0])\n", "        item[-2] = item[-2] + int(abs(item[0]))\n", "        bboxpos.append(item)\n", "    \n", "    elif item[1]<0:\n", "        item[1] = abs(item[1])\n", "        item[-1] = item[-1] + int(abs(item[1]))\n", "        bboxpos.append(item)\n", "    \n", "    elif item[-1]<0:\n", "        item[-1] = abs(item[-1])\n", "        item[1] = item[1] + int(abs(item[-1]))\n", "        bboxpos.append(item)\n", "'''"]}, {"cell_type": "code", "execution_count": 37, "id": "294c6ce6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[3196.4668019044, 1525.14141578463, 60.9661676, 57.9178592245257]\n", "[3136.4668019044, 1576.96265824868, 67.0627843652402, 79.25601789]\n", "[1456.64331726931, 1710.85830811493, 115.457725081363, -107.8867267]\n", "[1346.86384096244, 1915.27526399669, 149.527217728322, -136.2779706]\n", "[1519.10405378873, 1877.42027216673, 52.996988561937, -83.28098203]\n", "[854.748976054088, 2299.5033733086, 124.921473038852, -117.3504747]\n", "[576.514786103918, 2541.775321, 153.312716911318, 117.350474672861]\n", "[1935.50893503717, 1477.7346693495, 98.4229787578831, -94.63747957]\n", "[2200.49387784686, 1318.74370366369, 43.5332406044483, -88.9592308]\n", "[5904.09440901982, 1128.02177303186, 135.933667418808, 101.520080730503]\n", "[5967.75954439318, 1386.12367319415, 70.5478527110275, 89.4753253895961]\n", "[5809.45704562698, 1584.00179665191, 56.7824180357043, 60.2237767045357]\n", "[5513.50020010754, 1804.24875145707, 53.3410593668753, 113.564836071411]\n", "[5585.76873215299, 1981.47872290185, 135.933667418807, 151.419781428547]\n", "[5783.64685561075, 2115.69171098624, -43.01698336, -101.5200807]\n", "[5382.72857069198, 2444.34146385956, 106.68211873375, 125.609591412317]\n", "[5375.84585335432, 2724.81209034733, 125.609591412317, -91.19600472]\n", "[5337.99090799718, 2943.33836581807, 178.950650779192, -132.4923087]\n", "[949.5569966, 809.953298330734, 75.5775079265794, 80.61600846]\n", "[863.902487655161, 840.184301501366, 108.32776136143, 105.808511097211]\n", "[957.1147474, 872.934554936217, 73.0582576623601, 55.4235058128249]\n", "[919.325993467986, 893.088557049971, 73.0582576623601, 88.1737592476759]\n", "[1020.09600403676, 850.261302558243, 85.6545089834566, 95.731510040334]\n", "[949.5569966, 956.069813655454, 47.8657550201669, 70.5390073981408]\n", "[654.804715724958, 996.377817882963, 95.7315100403339, 75.5775079265794]\n", "[2859.22354220469, 155.123349266615, 37.4766038610314, 52.0508386958769]\n", "[2676.0045899952, 153.04131571878, 66.6250735307225, 47.8867716002068]\n", "[2322.05888686324, 209.256221510327, 83.2813419134036, 64.54303998]\n", "[2234.61347785416, 215.502322153832, 64.54303998, 49.9688051480419]\n", "[2053.476559, 217.584355701667, 77.0352412698979, 58.2969393393822]\n", "[2336.63312169808, 302.947731162905, 74.9532077220628, 54.132872243712]\n", "[772.5223889, 948.970083903112, 75.5775079265794, 95.7315100403339]\n", "[1034.52441640054, 1306.70362142225, 60.46200634, 95.7315100403339]\n", "[1545.93222003706, 646.202031156478, 45.3465047559476, 88.1737592476759]\n", "[4.15105833483866, 782.241545424321, 57.9427560770442, 83.1352587192373]\n", "[2665.16634969055, 2247.7579946563, 106.037540837724, 125.962513210966]\n", "[1040.25000615051, 1112.49233094703, 52.9042555486056, 85.6545089834566]\n", "[5226.684713492, 1154.87798356477, 64.54303998, 70.7891406263926]\n", "[4995.5789896823, 1161.12408420828, 70.7891406263926, 68.7071070785573]\n", "[4983.08678839529, 1233.9952583825, 37.4766038610314, 49.9688051480416]\n", "[4918.5437484124, 1233.9952583825, 64.54303998, 89.5274425569081]\n", "[4987.25085549096, 1288.12813062621, 60.3789728872173, 47.8867716002068]\n", "[5235.01284768334, 1242.32339257384, 45.8047380523717, 45.8047380523717]\n", "[6411.92983748596, 386.229073076308, 49.9688051480419, 58.2969393393822]\n", "[6195.39834851112, 348.752469215277, 93.6915096525785, 39.5586374088665]\n", "[6226.628852, 382.065005980638, 83.28134191, 52.0508386958769]\n", "[5976.78482598843, 336.260267928266, 47.8867716002068, 54.132872243712]\n", "[5983.03092663194, 440.36194532002, -62.46100644, 35.3945703131963]\n", "[5933.06212148389, 461.1822808, 87.4454090090732, 49.9688051480419]\n", "[5847.69874602266, 446.608045963525, 72.87117417, 56.2149057915471]\n", "[5676.971995, 452.854146607031, 85.3633754612382, 64.54303998]\n", "[5974.7027924406, 544.463622711774, 95.7735432004136, 45.8047380523717]\n", "[5966.37465824926, 586.104293668476, 64.54303998, 33.3125367653612]\n", "[5235.58088295914, 123.892846049088, 37.4766038610314, 49.9688051480419]\n", "[5289.71375520286, 100.990477022903, 45.8047380523717, 45.8047380523717]\n", "[5079.42836687151, 119.728778953418, 62.4610064350523, 41.6406709567015]\n", "[5033.62362881914, 132.220980240429, 43.7227045045366, 41.6406709567015]\n", "[2822.62542532725, 3410.05539849786, 139.703786977608, 151.154917057739]\n", "[5180.69088450218, 976.958724232217, 70.7891406263926, 89.5274425569083]\n", "[6103.78887240637, 1169.45221839962, 66.6250735307215, 66.6250735307227]\n", "[6507.70338068638, 1311.0304996524, 43.7227045045366, 83.28134191]\n", "[6738.80910449607, 1521.694388, 49.9688051480419, 112.429811583094]\n", "[6526.44168261689, 795.632571671217, 45.8047380523717, 120.757945774434]\n", "[6210.68762162243, 4545.0657318142, 160.316106894042, 124.921641735617]\n", "[5968.72775973038, 2676.03778701362, 33.6120688223933, 23.2698938001184]\n", "[1018.30478078503, 1063.73101635908, -52.99727484, 71.9248729922512]\n", "[6357.852095, 3384.47677603945, 94.3723470782581, 280.5314975]\n", "[6191.08452270929, 3481.43466687327, 82.7374001781989, 210.72181607885]\n", "[5628.07499740518, 1456.97274492378, -68.82734237, 75.0843734933305]\n", "[5049.29961839409, 1425.68758930156, -59.44179568, 48.49199121]\n", "[766.371352470399, 737.599074694101, -65.41425702, 59.72606075]\n", "[235.947051015478, 831.454313021245, 39.8173738357582, 32.7071285079442]\n", "[2858.7225656503, 2824.132854, 69.6804042125769, 66.8363060814513]\n", "[3837.60944728952, 4009.73397455409, 136.516710294028, 78.21269861]\n", "[307.9753834, 3845.05441830514, 99.9376102960837, 93.6915096525785]\n", "[1024.57345522466, 3836.53719317635, 112.429811583094, 62.4610064350523]\n", "[1324.38628611292, 3501.3297919749, -93.69150965, 93.6915096525785]\n", "[1368.10899061745, 3919.81853508975, -74.95320772, 68.7071070785576]\n", "[1688.74215698405, 3940.6388705681, -83.28134191, 74.9532077220628]\n", "[1359.78085642611, 4221.90249046329, -114.5118451, 99.9376103]\n", "[4228.52103436211, 2148.43956879327, 79.15144372, 99.7996464348851]\n", "[4077.100881, 2343.18967208907, 58.5032410135533, 79.15144372]\n", "[3954.93234844579, 2439.54795140551, 34.4136711844431, 72.2687094873306]\n", "[3564.33718050236, 2518.69939512973, 65.385975250442, 55.061873895109]\n", "[3287.62009359347, 2950.43459045374, -25.81025339, 77.4307601649971]\n", "[3124.15515546737, 3019.26193282263, 67.1066588096641, 56.7825574543312]\n", "[2703.99550591347, 3371.37634025578, 72.2687094873306, 60.2239245727755]\n", "[5832.82362372017, 1793.19665534537, 61.9446081319976, 82.5928108426635]\n", "[406.7895354, 72.881435069376, 117.350157728707, 56.782334384858]\n", "[1080.60657007837, 353.007618034676, -64.3533123, 81.3880126182965]\n", "[1411.83685399004, 279.19058333436, -92.7444795, 71.9242902208202]\n", "[862.940954936416, 633.133800999975, 41.6403785488959, 96.5299684542587]\n", "[238.335276702977, 269.726860936884, 113.564668769716, 73.8170347003155]\n", "[3255.02571563239, 595.2789114, 71.9242902208202, 105.993690851735]\n", "[2961.65032131063, 557.424021820165, 98.4227129337539, 77.602523659306]\n", "[2868.90584181536, 498.748942955811, 92.7444794952681, 70.0315457413249]\n", "[3302.34432761978, 156.162192167168, 51.1041009463722, 52.9968454258675]\n", "[3431.05095222545, 65.3104571513949, -54.88958991, 37.8548895899054]\n", "[3508.65347588476, 69.0959461103855, -70.03154574, 35.9621451104101]\n", "[3836.09827083744, 42.5975233974517, 51.1041009463722, 56.782334384858]\n", "[4079.91816283024, 35.0265454794706, 43.5331230283912, 71.9242902208202]\n", "[2552.81751373965, 12.3136117255274, 85.1735015772866, 52.9968454258675]\n", "[3337.56530074344, 952.716283595605, 108.075728982772, 92.4331892615815]\n", "[1770.56826030076, 170.443787854888, -110.8474898, 91.4491790832305]\n", "[1601.52583835903, 1035.30611036579, -91.44917908, 102.533928063016]\n", "[2000.5768016313, 586.373776684475, -96.99155357, 83.1356173483914]\n", "[2527.10237817112, 1110.12816597934, 80.3644301, 47.1101831640885]\n", "[2399.62776490358, 1054.70442108041, -80.3644301, 127.474613267533]\n", "[2319.26333480014, 1132.29766393891, 116.389864287748, 155.186485716997]\n", "[3089.90523812666, 1107.10507664676, -55.4237449, 38.7966214292493]\n", "[3051.10861669741, 1068.30845521751, 110.847489797855, 63.7373066337667]\n", "[3017.85436975805, 1301.59200911075, 105.305115307962, 119.161051532694]\n", "[3150.87135751548, 1271.10894941634, 94.2203663281769, 102.533928063016]\n", "[3646.91387436088, 181.528536834673, 96.9915535731233, 77.5932428584987]\n", "[3638.60031262604, 261.892966938118, 63.7373066337667, 44.338995919142]\n", "[3907.40547538584, 236.952281733601, 110.847489797855, 80.364430103445]\n", "[4004.39702895897, 375.51164398092, 74.8220556135523, 74.8220556135523]\n", "[4076.44789732757, 766.24904551836, 58.194932143874, 69.2796811236595]\n", "[4129.10045498155, 780.104981743091, 113.618677042801, 80.3644301]\n", "[3699.56643201486, 741.308360313842, -47.11018316, 72.0508683686059]\n", "[3538.83757180797, 680.342240925022, 58.194932143874, 124.703426022587]\n", "[3577.63419323722, 442.020137859633, 91.4491790832305, 91.4491790832305]\n", "[3732.82067895422, 2588.430626, 135.788175002373, 146.872923982158]\n", "[4264.13274400995, 2857.23578873108, 146.872923982158, 138.559362247319]\n", "[4578.03278866287, 1220.47179874573, -96.99155357, 85.9068045933378]\n", "[4591.8887248876, 1123.48024517261, 91.4491790832305, 99.7627408180697]\n", "[5085.16005448805, 887.929329352168, 119.161051532694, 102.533928063016]\n", "[5484.21101776033, 1201.07348803111, -110.8474898, 121.932238777641]\n", "[5520.23645194464, 1314.692165, 52.6525576539812, 49.8813704090348]\n", "[5675.42293766163, 1336.86166303348, -133.0169878, 116.389864287748]\n", "[5428.7872728614, 1381.20065895262, 69.2796811236595, 85.9068045933378]\n", "[5112.87192693752, 1611.20920028317, 102.533928063016, 152.415298472051]\n", "[5758.55855501002, 1822.07134355797, -69.27968112, 55.4237448989276]\n", "[5858.32129582809, 1750.02047518936, -102.5339281, 144.101736737212]\n", "[5813.98229990895, 1730.62216447474, 127.474613267533, 102.533928063016]\n", "[6265.68582083521, 1500.61362314419, -108.0763026, 105.305115307962]\n", "[6196.40613971155, 1353.74069916203, 66.5084938787131, 58.194932143874]\n", "[6188.09257797671, 1251.20677109901, 63.7373066337667, 119.161051532694]\n", "[4295.37168967834, 613.077945642253, 77.5932428584986, 99.7627408180697]\n", "[4400.6768049863, 577.05251145795, 52.6525576539812, 74.8220556135523]\n", "[4522.60904376394, 510.544017579237, 69.2796811236595, 83.1356173483914]\n", "[4641.77009529663, 577.05251145795, -91.44917908, 72.0508683686059]\n", "[4519.83785651899, 607.53557115236, 52.6525576539812, 69.27968112]\n", "[4422.34243534317, 101.164106731228, 119.161051532694, 80.364430103445]\n", "[5566.842768, 101.164106731228, 58.194932143874, 69.2796811236595]\n", "[6491.15972288128, 854.927037356644, 52.6525576539812, 130.24580051248]\n", "[6795.99031982538, 907.579595010625, -69.27968112, 63.7373066337667]\n", "[6568.75296573977, 943.605029194928, -55.4237449, 55.4237448989276]\n", "[6311.03255195976, 752.393109293628, 77.5932428584986, 77.5932428584986]\n", "[4911.58299326184, 461.418448574258, 74.8220556135523, 63.7373066337667]\n", "[2017.34295673551, 797.974622, 58.6750788643533, 64.3533123028391]\n", "[102.057674179318, 1918.13514300325, -96.52996845, 100.315457413249]\n", "[2430.5564723854, 1059.11142294555, 130.828514031777, 85.3229439337676]\n", "[3740.52213723976, 30.5821485421296, 100.965483654958, 59.72606075]\n", "[6887.64604386072, 2.14116723087371, 45.5055700980094, 45.5055700980094]\n", "[2240.26054666098, 2.14116723087371, 127.984415900651, 38.3953247701954]\n", "[4600.25076399842, 2044.77700796037, 92.7444794952681, 58.6750788643533]\n", "[4797.09618986593, 1980.42369565753, -79.49526814, 134.384858044164]\n", "[5211.60723087539, 163.733170085149, -105.9936909, 81.3880126182965]\n", "[5507.73579209385, 0.957144848555796, 143.84858044164, 71.9242902208202]\n", "[5634.54967222003, 606.979495268139, 109.779179810726, 79.4952681388013]\n", "[5878.71371007492, 824.645110410095, 71.9242902208202, 77.602523659306]\n", "[5693.22475108439, 1078.27287066246, -77.60252366, 94.63722397]\n", "[6681.40926902603, 1171.70561300091, -87.06624606, 49.211356466877]\n", "[6492.1348210765, 1118.70876757504, 130.599369085173, 83.2807570977918]\n", "[6482.67109867902, 1111.13778965706, -79.49526814, 121.135646687697]\n", "[6444.81620908912, 1201.98952467284, 83.2807570977918, 51.1041009463722]\n", "[6492.1348210765, 1217.1314805088, 83.2807570977918, 113.564668769716]\n", "[6446.70895356861, 1239.84441426274, 45.4258675078864, 51.1041009463722]\n", "[3124.02030234775, 38.5294705878503, 112.429811583094, 64.54303998]\n", "[3969.32592276879, 117.646745405583, 56.2149057915471, 89.52744256]\n", "[4065.0994659692, 411.213475650329, 49.9688051480423, 124.922012870105]\n", "[5857.35178754858, 252.978926014863, 106.183710939589, 77.0352412698979]\n", "[6013.50430363621, 315.439932449916, 120.757945774434, 89.5274425569083]\n", "[6361.20390612466, 375.818905337133, 77.0352412698979, 108.265744487424]\n", "[6811.6802786703, 719.354440729921, -91.6094761, 114.511845130929]\n", "[2027.16718574529, 169.69758410146, 60.3789728872173, 60.3789728872173]\n", "[6590.98472259978, 633.991065268682, -43.7227045, 35.3945703131963]\n", "[1524.8293215813, 269.635194397544, 45.8047380523717, 72.87117417]\n", "[4603.1720134822, 2039.6674060384, 87.02858861, 70.997006496817]\n", "[4717.683314, 1980.12152962171, 73.28723251, 139.703786977608]\n", "[5212.37213374521, 170.634873080193, -103.0601707, 75.5774585288697]\n", "[5507.81128981261, 5.73859992629601, 144.28423900966, 68.70678048]\n", "[5636.06394671009, 610.358268157253, 103.060170721186, 77.867684544896]\n", "[5874.24745237683, 830.6363482, 73.28723251, 68.70678048]\n", "[5684.15869304664, 1078.18893169125, -66.41655446, 96.1894926731068]\n", "[6488.86085954759, 1115.66538282338, 132.833108929528, 89.3188146250278]\n", "[6585.0503522207, 1168.34058119198, 91.6090406410541, 45.8045203205271]\n", "[6481.99018149951, 1106.50447875927, -61.83610243, 139.703786977608]\n", "[6449.92701727514, 1202.69397143238, 82.4481365769487, 52.6751983686061]\n", "[6488.86085954759, 1211.85487549648, 91.6090406410541, 125.962430881449]\n", "[6436.18566117899, 1248.4984917529, 57.2556504006588, 41.2240682884744]\n", "[1773.91624294965, 175.943684744965, -106.1837109, 81.199308365568]\n", "[1996.693832568, 590.268360764146, -87.44540901, 74.9532077220628]\n", "[1513.66204947027, 1036.01279772718, 74.9532077220628, 93.6915096525785]\n", "[2024.89587064414, 800.553749095489, 47.8867716002065, 58.2969393393823]\n", "[2526.66595567239, 1109.0732455124, 77.0352412698979, 45.8047380523717]\n", "[2420.48224473281, 1061.18647391219, 139.49624770495, 79.1172748177329]\n", "[2337.2009028194, 1144.46781582559, 77.0352412698979, 39.5586374088665]\n", "[2316.38056734105, 1057.02240681652, 81.199308365568, 127.00404641794]\n", "[2328.87276862806, 1159.04205066044, 108.265744487424, 116.593878678764]\n", "[3064.01989256716, 1071.59664165137, 93.6915096525785, 72.87117417]\n", "[3151.46530157623, 1271.47186224353, 93.6915096525785, 91.6094761047434]\n", "[3022.37922161046, 1300.62033191322, 102.019643843919, 102.019643843919]\n", "[3655.31742, 744.338843303942, 31.2305032175262, 62.4610064350523]\n", "[3542.88760856923, 688.123937512394, 54.132872243712, 110.347778035259]\n", "[3584.52827952593, 444.52601241569, 85.3633754612382, 89.5274425569083]\n", "[3644.90725241314, 190.517919579811, 97.8555767482486, 58.2969393393822]\n", "[3645.67875697462, 260.693352541364, 53.3613676822335, 46.4184457172109]\n", "[3909.3255129882, 236.322657632183, 112.429811583094, 72.87117417]\n", "[4011.34515683212, 377.900938884968, 74.9532077220628, 66.6250735307225]\n", "[4544.15639998596, 513.4223772, 54.132872243712, 81.199308365568]\n", "[4639.92994318638, 577.965417203466, -83.28134191, 72.87117417]\n", "[4525.41809805545, 605.031853325322, 60.3789728872201, 77.0352412698979]\n", "[4402.57811873318, 584.211517846972, 45.8047380523717, 60.3789728872173]\n", "[4304.72254198493, 623.770155255838, 54.132872243712, 104.101677391754]\n", "[4081.94495236658, 775.7586042, 45.8047380523717, 56.214905791547]\n", "[4127.74969041895, 782.004704891304, 124.922012870105, 85.3633754612382]\n", "[4423.39845421153, 100.990477022903, 110.347778035259, 79.117274817733]\n", "[4912.67633795277, 467.428381441876, 79.1172748177329, 56.2149057915471]\n", "[4484.34520822007, 1215.44620623599, 95.7735432004136, 95.7735432]\n", "[4598.857053351, 1130.08283077475, 81.199308365568, 95.7735432004136]\n", "[5090.21697064007, 892.731006321553, 116.593878678764, 83.28134191]\n", "[6301.96049548009, 754.938268769448, 93.6915096525785, 72.87117417]\n", "[6495.58961542875, 944.40332162244, 68.7071070785576, 54.132872243712]\n", "[6487.261481, 848.629778422027, 58.2969393393822, 129.086079965775]\n", "[6787.07431212566, 911.090784857079, -58.29693934, 66.6250735307225]\n", "[6208.26898582751, 1352.86042039311, 52.0508386958769, 58.2969393393817]\n", "[6181.20254970566, 1265.41501138403, 70.7891406263917, 104.101677391754]\n", "[6172.87441551431, 1500.6848022894, 87.4454090090732, 108.265744487424]\n", "[5760.63177304297, 1746.74329227125, 102.019643843919, 145.742348348455]\n", "[5691.92466596441, 1823.77853354115, 68.7071070785576, 54.132872243712]\n", "[5829.33888012153, 1740.49719162775, 108.265744487424, 91.6094761047434]\n", "[5522.90151256038, 1321.62991717558, 45.8047380523717, 47.8867716002068]\n", "[5660.3157267175, 1340.36821910609, -118.6759122, 112.429811583094]\n", "[5429.21000290781, 1382.0088900628, 68.7071070785576, 85.3633754612382]\n", "[5391.73339904677, 1207.11807204465, 95.7735432004136, 116.593878678764]\n", "[5123.151071, 1615.57517875764, 93.6915096525785, 149.906415444126]\n", "[5564.54218351709, 86.4162421880571, 60.3789728872173, 85.3633754612382]\n", "[4258.160678, 2865.93086970133, 160.316583183301, 135.33218060928]\n", "[3727.24212302136, 2599.05213954906, 137.414214157115, 127.00404641794]\n", "[2252.97554413788, 2.35528968912804, 118.88359136444, 50.0562489955537]\n", "[3734.61198656051, 28.9476719680159, 104.80527133444, 56.3132801199979]\n", "[6886.95117115769, 2.94480241194778, 54.06128784, 38.7830978]\n", "[1286.91571834336, 70.9886905898807, 90.8517350157729, 43.5331230283912]\n", "[1182.81477197112, 69.0959461103855, 85.1735015772871, 45.4258675078864]\n", "[1146.85262686071, 72.881435069376, 37.8548895899054, 35.9621451104101]\n", "[1449.69174357995, 2.84988932805106, 51.1041009463722, 32.1766561514196]\n", "[626.347895, 2.84988932805106, 54.8895899053627, 83.2807570977918]\n", "[722.877863453766, 42.5975233974517, 30.2839116719243, 64.3533123028391]\n", "[1412.18095653065, 419.25367481701, 58.6750788643533, 88.95899054]\n", "[1325.11471047386, 513.890898791773, -111.6719243, 79.4952681388013]\n", "[2059.84369993962, 201.588059675054, 124.921135646688, 96.5299684542587]\n", "[2137.44622359893, 493.242760787627, 113.564668769716, 132.492113564669]\n", "[2258.58187028662, 603.021940598353, 41.6403785488959, 39.7476340694006]\n", "[2220.72698069672, 629.520363311286, 126.813880126183, 113.564668769716]\n", "[2090.12761161154, 680.624464257658, 43.5331230283912, 71.9242902208202]\n", "[2061.73644441911, 1100.9858044164, 58.6750788643533, 52.9968454258675]\n", "[2127.98250120145, 1029.06151419558, 105.993690851735, 143.84858044164]\n", "[2233.97619205318, 1013.91955835962, 117.350157728707, 102.208201892744]\n", "[1586.6575800658, 883.320189274448, 62.4605678233439, 153.312302839117]\n", "[2937.90506549438, 653.953990274423, 43.5331230283907, 45.4258675078864]\n", "[2954.93976580984, 566.887744217641, 45.4258675078864, 96.5299684542587]\n", "[2839.48235256063, 776.982381441616, -52.99684543, 96.5299684542587]\n", "[3562.51074372782, 264.048627498398, 43.5331230283912, 49.211356466877]\n", "[3551.15427685085, 182.660614880102, 60.5678233438486, 104.10094637224]\n", "[3791.53282574675, 231.871971346979, 54.8895899053628, 51.1041009463722]\n", "[4226.17564878253, 116.414558097767, 88.95899054, 105.993690851735]\n", "[4337.84757307275, 148.591214249187, 87.0662460567823, 60.5678233438486]\n", "[153.737040849115, 133.809247198435, 60.7602782558648, 28.4409813112559]\n", "[91.6839907154657, 203.61892859879, 130.56995965622, 108.592837733886]\n", "[1414.48154156046, 1398.85440822948, 97.8555767482486, 87.4454090090732]\n", "[19.1463513041597, 215.290028849186, 74.78846828, 54.4887411766499]\n", "[543.734035965633, 150.117220775154, 79.0620950406293, 66.2412147637705]\n", "[5268.87350804831, 231.086202317851, 125.962430881449, -105.3503967]\n", "[6290.31431119606, 515.074228305119, -139.703787, -144.284239]\n", "[1410.59708692577, 4395.26886304274, 196.959437378266, 229.022601602635]\n", "[2343.96818797774, 3925.77252975733, 318.341416227663, 224.442149570583]\n", "[3005.84350660936, 4308.24027443373, 105.350396737212, 146.574465025687]\n", "[3092.87209521836, 4388.39818499466, 148.864691041713, 96.1894926731068]\n", "[4142.42030660736, 4060.89586470289, 212.99101949045, 151.154917057739]\n", "[3901.94657492459, 4134.18309721573, 290.858704035347, 231.312827618662]\n", "[4653.55698583478, 4376.94705491453, 267.956443875084, 242.763957698793]\n", "[5578.80829630943, 4063.18609071891, 148.864691041713, 318.341416227663]\n", "[5514.68196786069, 3508.95139484054, 137.413560961581, 137.413560961581]\n", "[5326.883435, 2855.40428518163, 84.7383625929751, 185.508307298135]\n", "[4600.88178746618, 2491.67476607129, 135.123334945554, 96.1894926731068]\n", "[4129.09522816475, 2464.19205387897, 112.221074785291, 146.574465025687]\n", "[6516.34357173991, 3641.78450377007, 281.697799971241, 171.766951201977]\n", "[6470.53905141938, 2837.08247705342, 153.445143073766, 112.221074785291]\n", "[6383.510463, 2530.19219090589, 219.86169753853, 158.025595105818]\n", "[6575.88944815659, 861.242086325526, 137.413560961581, 135.123334945555]\n", "[5849.88780107624, 601.197364093148, 82.4481365769487, 112.221074785291]\n", "[5634.60655556976, 530.2003576, 112.221074785291, 153.445143073766]\n", "[6223.19464168853, 305.758208025748, 73.28723251, 84.7383625929751]\n", "[6268.99916200906, 326.370242169985, 87.0285886090014, 105.350396737212]\n", "[6905.68199446439, 1249.95588289323, 98.47971869, 153.445143073766]\n", "[5029.98688733881, 4537.262876, 270.24666989111, 128.252656897476]\n", "[5300.23355722991, 4534.97265002034, 203.830115426345, 132.833108929528]\n", "[15.9065005322737, 2491.01647533521, 219.093824901868, 166.34901520327]\n", "[547.41189057199, 3343.04801662025, 231.265704063083, 186.635480471961]\n", "[1176.29231390143, 2214.38289743175, 198.807359633176, 174.463601310747]\n", "[1257.43817497619, 2072.37764055091, 178.520894364485, 129.833377719625]\n", "[3407.80349345748, 2226.55477659297, -174.4636013, 166.34901520327]\n", "[2730.235553, 1975.00260726119, 93.31774024, 170.406308257008]\n", "[2150.04264679861, 1861.39840175652, 154.177136042055, 129.833377719625]\n", "[2588.230297, 1581.0763714013, 85.2031541285041, 117.66149855841]\n", "[2320.44895505562, 1568.90449224008, 178.520894364485, 154.177136042055]\n", "[2340.73542032431, 1255.75529233032, -77.08856802, 85.2031541285041]\n", "[2251.47497314207, 1288.21363676023, -68.97398191, 97.375033289719]\n", "[1995.86551075656, 1211.1250687392, 101.432326343457, 129.833377719625]\n", "[1691.56853172619, 1263.8698784378, 202.864652686915, 166.34901520327]\n", "[2320.44895505562, 983.916657729855, 113.604205504672, 93.31774024]\n", "[2133.81347458366, 963.630192461163, 109.546912450934, 154.177136042055]\n", "[1768.65709974722, 910.885382762566, 105.489619397196, 125.776084665887]\n", "[1241.20900276124, 626.874869, 77.0885680210275, 129.833377719625]\n", "[1111.37562504161, 570.0727662, 77.08856802, 121.718791612149]\n", "[1886.31859830563, 359.093527454158, 101.432326343457, 129.833377719625]\n", "[5244.2816986649, 4040.90242186324, 279.953220707942, 190.6927735257]\n", "[9.8766480848144, 3949.39714858831, 239.328775100835, 267.040528007247]\n", "[1592.65283596484, 4004.82065440114, 90.6930095118953, 136.039514267843]\n", "[2615.92645466802, 2360.43724420605, 133.520264003624, 154.132325598403]\n", "[2920.75573663856, 3129.95364661408, 118.404762418308, 183.90526928801]\n", "[2510.11794357081, 3805.79988352781, 98.2507603, 95.7315100403339]\n", "[2734.33121708633, 3830.99238617001, 269.559778271466, 236.809524836615]\n", "[3371.70153393381, 3977.10890149473, 173.828268231133, 153.674266117378]\n", "[3388.19109864008, 1229.06490349213, 216.655522722861, 146.11651532472]\n", "[3599.8081208345, 1261.81515692698, 68.0197571339214, 136.039514267843]\n", "[3521.7113626437, 1357.54666696731, 55.4235058128249, 57.9427560770442]\n", "[3332.76759282725, 1405.41242198748, 146.11651532472, 100.770010568773]\n", "[3564.53861713543, 1665.353201, 128.481763475185, 163.751267174255]\n", "[4211.98593503979, 1703.14195498532, 103.289260832992, 188.943769816449]\n", "[4597.431225, 1457.85863727541, 204.059271401764, 158.712766645817]\n", "[4980.35726562669, 1440.22388542588, 115.885512154088, 88.1737592476759]\n", "[5317.93680103207, 1849.25854719146, 236.809524836615, 168.789767702694]\n", "[5328.013802, 2055.837069, 60.46200634, 143.597265060501]\n", "[5179.378037, 1965.14405934554, 178.866768759571, 188.943769816448]\n", "[5212.12828993486, 2567.47382535306, 80.61600846, 201.5400211]\n", "[4370.99029509038, 1124.99288811452, 67.0625218302465, 93.88753056]\n", "[5255.30106640762, 1403.60715428391, 90.53440447, 164.303178484108]\n", "[4745.6259, 2960.6769014146, 248.131330771918, 194.481313307719]\n", "[5899.101275978, 2830.209925, 187.775061124694, 127.418791477472]\n", "[5513.49177545407, 2934.15683395913, 271.603213412504, 191.128187216207]\n", "[5479.96051453895, 3044.80999497904, 137.478169752008, 90.53440447]\n", "[3675.97867730527, 4217.48950783706, 130.771917568984, 204.540691582256]\n", "[3843.63498188089, 4318.08329058243, 124.065665385959, 174.362556758645]\n", "[3964.34752117534, 4365.02705586361, 254.837582954942, 221.306322039818]\n", "[4279.54137377751, 4589.68650399494, -70.41564792, -160.9500524]\n", "[872.393973159576, 2313.45355425908, 201.539889410319, 174.057177218003]\n", "[1764.95749325308, 2354.88576137433, 96.1894926731068, 174.057177218003]\n", "[1939.01467047108, 2611.59935378036, 121.381978849397, 190.088759330187]\n", "[872.206474895386, 2315.706232, 199.875220592167, 183.218952209487]\n", "[1763.88462243245, 2361.51097000392, 99.937610296084, 170.726750922476]\n", "[1947.10357464193, 2613.6263743839, 114.511845130929, 185.300985757322]\n", "[1674.80584375235, 2496.67163554119, 109.930848769265, 91.6090406410541]\n", "[2609.2180582911, 2673.43545621307, -91.60904064, 121.381978849397]\n", "[1850.94504700397, 3246.40823787321, 66.4165544647643, 164.896273153897]\n", "[1447.86526818333, 3646.36495580212, -52.67519837, 169.47672518595]\n", "[996.690743, 3939.51388585349, 139.703786977608, 137.413560961581]\n", "[4035.37606157424, 2379.88474030458, 68.8273423688863, 136.090426956662]\n", "[3876.37210231922, 2273.20453851826, 64.35383373, 81.388672070179]\n", "[4126.21639797652, 2256.16970017799, 41.6407159428823, 117.351108566305]\n", "[4345.87476891146, 2449.069804, -94.71984166, 74.652078594157]\n", "[3716.54971925749, 2768.54859216395, -80.27105225, 74.652078594157]\n", "[4577.05539939659, 3745.44729806813, 102.746946882281, 74.652078594157]\n", "[4615.58550447745, 3371.38419457483, 72.2439470266028, 77.0602101617112]\n", "[5200.76147539294, 3584.90519356457, -37.72739456, 105.957788972352]\n", "[1872.17532173511, 1618.32104082187, 94.0201040419388, 89.3190988398418]\n", "[1793.43348459998, 1586.58925570772, 85.7933449382691, 79.917088435648]\n", "[3170.47125858346, 1191.68195321505, -42.6616451, 93.8556192269606]\n", "[3295.61208421941, 854.65495690005, -83.90123537, 58.3042483076573]\n", "[3562.95839353257, 877.407834288405, -48.34986445, 41.2395902663918]\n", "[3474.7909936527, 839.0123537, 44.083699939936, 69.6806870018344]\n", "[3508.92030973523, 831.902079511696, 66.8365773282901, 54.038083797341]\n", "[3524.56291293973, 817.6815311, 51.1939741237967, 24.1749322251262]\n", "[3612.7303128196, 813.415366633659, -56.88219347, 39.8175354296197]\n", "[3336.14064706742, 1185.99373386796, -63.99246765, 48.3498644502524]\n", "[5981.88807513007, 3655.52585986622, 217.571471522504, 212.991019490451]\n", "[6919.85804201653, 2595.74965403903, 64.6384963298737, 76.3909502080325]\n", "[6780.00384086644, 2638.0584880004, 216.245151358123, 203.317452092148]\n", "[6552.006236, 2675.66634041051, 199.7917159287, 142.204691925722]\n", "[6887.69271708218, 3653.81871393051, 117.524572987176, 181.628885525637]\n", "[6880.89377484325, 3172.15354128033, 126.266070151513, 180.657608062934]\n", "[816.418158182304, 2610.1151487878, -170.5040981, 197.096480419993]\n", "[736.64101134564, 2084.24011514931, 192.403707076659, 178.32538704666]\n", "[933.737491765633, 2063.90476399487, 229.945893823325, 206.482027106659]\n", "[1162.11912780785, 2024.79831946709, 79.7771468366636, 125.140622488884]\n", "[1281.00271917229, 2103.01120852264, 67.2630845877752, 120.447849145551]\n", "[1499.1455524641, 2269.10695250559, 75.0843734933305, 100.112497991107]\n", "[1496.01703690187, 3009.56972134147, 96.98398243, 145.475973643328]\n", "[2135.08741992156, 3200.55123701753, -156.4257781, 131.397653613328]\n", "[2300.89874471933, 3086.36041899642, 253.40976053999, 176.761129265548]\n", "[2432.29639833266, 2450.41859778453, 259.666791664435, 211.174800449992]\n", "[2158.55128663823, 2232.986766, 229.945893823325, 170.504098141105]\n", "[2144.47296660823, 2394.24757499448, -106.3695291, 170.504098141105]\n", "[2164.80831776267, 2503.74561967225, 67.2630845877752, 114.190818021107]\n", "[2255.10864028935, 2564.75167313558, 111.062302458885, 129.69113850227]\n", "[2213.30030897711, 2774.64653951492, -142.3474581, 95.4197246477741]\n", "[1688.42074248674, 1619.22897311892, 187.710933733326, 136.090426956662]\n", "[3293.63354961714, 3611.52445240933, 168.939840359994, 217.431831574436]\n", "[4961.08530440461, 2647.447099, 58.990915622672, 62.6112773239382]\n", "[6727.46370286636, 186.937707860232, 73.5201157122194, 84.4699201799968]\n", "[6577.2949558797, 305.821299224672, 109.498044677774, 75.0843734933305]\n", "[6430.25472445526, 318.33536147356, 64.1345690255531, 65.6988268066642]\n", "[6966.08404767605, 950.579900358922, 39.1064445277763, 98.5482402099963]\n", "[2679.63139386583, 962.4713404, 52.9968454258679, 64.3533123028391]\n", "[2632.312782, 309.474495006284, 75.7097791798107, 60.5678233438486]\n", "[2825.37271878697, 303.796261567799, 66.2460567823344, 60.5678233438486]\n", "[2514.96262414974, 797.802570716064, 49.211356466877, 83.2807570977918]\n", "[2666.38218250936, 1038.18111961196, 41.6403785488959, 43.5331230283912]\n", "[248.569785912787, 1.46244424724178, 61.1904801503484, 40.7936534335656]\n", "[6659.95508565397, 1198.32466700934, 90.72674879, 125.140343162749]\n", "[175.411058082152, 1751.79624201327, 87.0285886090014, -155.7353691]\n", "[335.726879203997, 2880.87759802211, 125.962430881449, -192.3789853]\n", "[931.185643370849, 2402.22036067261, 116.801526817344, -148.864691]\n", "[1.35388086414937, 2248.77521759884, 84.7383625929751, 174.057177218003]\n", "[372.370495460419, 2200.68054115444, 75.5774585288697, 123.672204865423]\n", "[349.468235300155, 1735.76465990109, 158.025595105818, 121.381978849397]\n", "[326.565975139892, 1799.89098834983, 70.997006496817, 100.76994470516]\n", "[395.272755620682, 2031.20381596849, 201.539889410319, 121.381978849397]\n", "[83.8020174410981, 1809.05189241393, 84.7383625929751, 146.574465025687]\n", "[120.44563369752, 1857.14663875049, 77.867684544896, 167.186499169924]\n", "[344.887783268102, 1962.4970354877, 61.8361024327115, 84.7383625929751]\n", "[70.06066134494, 2088.45946636915, 107.640622753239, 167.186499169924]\n", "[205.183996290495, 1877.75867289472, 96.1894926731068, 169.47672518595]\n", "[216.635126370627, 1825.08347452612, 77.867684544896, 112.221074785291]\n", "[2785.88597055288, 674.931831430288, 201.255384615385, -129.3784615]\n", "[6398.95352259392, 1611.28312325226, 104.101368113014, 181.136380516645]\n", "[6528.03921905406, 1298.97901891322, 79.1170397658908, -154.0700248]\n", "[5066.456265, 370.394847114345, 131.167723822396, 149.90597008274]\n", "[4876.99177493538, 613.992048498799, 60.3787935055482, 139.495833271439]\n", "[5722.29462985933, 940.870344373663, 187.382462603426, -147.8239427]\n", "[5778.50962279408, 1078.28411851363, 129.085696460138, 133.249751184658]\n", "[5657.75203578299, 1294.81496418869, 74.95298504, -166.562189]\n", "[5518.25620251155, 980.428848372, 79.1170397658908, 160.316106894042]\n", "[5420.40091648532, 643.140431570443, 120.757587011096, -151.9879974]\n", "[5449.5493, 593.171774876196, 77.0350124036305, 81.1990671281511]\n", "[5443.30321747018, 684.780978815648, 108.265422837535, 133.249751184658]\n", "[5330.87373990812, 630.648267396881, 79.1170397658908, 183.218407878905]\n", "[5203.87007081025, 638.9763768, 60.3787935055482, 156.152052169521]\n", "[4802.03878989401, 3903.24826571993, 122.839614373357, 85.3631218526716]\n", "[4159.03925479805, 1596.36194833554, -93.6912313, -81.19906713]\n", "[4182.17629103154, 461.300227371444, 28.1565772116185, 39.106357238359]\n", "[4252.56773406059, 386.216021473794, 51.620391554634, 43.7991201069621]\n", "[5415.18583239028, 2117.99823364024, 68.1393533610801, 77.6031524390079]\n", "[4292.77926174805, 2852.38904208744, 66.2465935454946, 52.9972748363957]\n", "[4417.70140957669, 2541.97643233141, 68.1393533610801, 75.7103926234224]\n", "[302.067709553264, 3723.88295314217, 36.9085947364117, 82.5586987524999]\n", "[2501.04293279632, 4567.92423803538, -138.8928697, 66.0469590019999]\n", "[1747.82757876445, 527.450926136018, -115.0566971, 90.49403144]\n", "[529.473780683162, 2404.39845906447, 117.350157728707, 107.88643533123]\n", "[6182.402183, 2741.80013926974, 106.007293978317, 89.2012595671207]\n", "[4100.98795970781, 831.469165590221, 48.0947463365539, 80.1579105609224]\n", "[4245.27219871747, 682.604474548508, 77.8676845448965, 84.7383625929751]\n", "[5853.247609, 900.354889589905, 73.8170347003155, 98.4227129337539]\n", "[6229.90376084385, 572.9100946, 52.9968454258675, 73.8170347003155]\n", "[6347.25391857256, 906.033123028391, 41.6403785488959, 81.3880126182965]\n", "[6184.47789333596, 764.077287066246, 35.9621451104101, 54.8895899053628]\n", "[6006.55991226341, 924.960567823344, 64.3533123028391, 79.4952681388013]\n", "[2718.69778186857, 19.5621252813496, 20.3353511544437, 20.3353511544437]\n", "[2790.65364, 43.0259919980154, 29.72089784111, 26.5923822788879]\n", "[2656.12747062413, 2.35528968912804, 25.0281244977765, 32.8494134033321]\n", "[2606.07122162858, 36.7689608735712, 56.3132801199979, 35.9779289655542]\n", "[755.612596216622, 1867.50123804569, 81.199308365568, 62.4610064350523]\n", "[484.948234998062, 2123.78070952134, -29.14846967, 45.8047380523717]\n", "[532.835006598268, 2077.97597146897, -31.23050322, 62.4610064350523]\n", "[576.557711102805, 2198.733917, 29.1484696696911, 74.9532077220628]\n", "[166.397102179295, 2213.30815207825, 81.199308365568, 74.9532077220628]\n", "[31.0649215700146, 2175.83154821721, 68.7071070785576, 110.347778035259]\n", "[5731.59201961007, 189.133293290693, -49.77191929, 31.2852064089869]\n", "[5710.26119705849, 173.4906900862, 39.8175354296197, 35.5513709193032]\n", "[5713.10530673203, 162.114251392023, 27.0190418986705, 25.5969870618983]\n", "[3128.56293255005, 1517.53032045608, -54.13287224, 79.1172748177329]\n", "[3145.21920093273, 1577.9092933433, 60.3789728872173, 77.0352412698979]\n", "[2871.73724717995, 1717.27009585271, 554.237448989276, 188.440732656353]\n", "[4063.85154553968, 1320.73839659527, 448.932333681313, 166.271234696783]\n", "[6301.09823656769, 3674.2252558713, 79.1170397658908, 166.562188980823]\n", "[4590.39301310044, 403.8635736, -21.33082255, 51.1939741237967]\n", "[207.264155997214, 498.998232174426, -34.41367118, 54.7490223388868]\n", "[3692.47552696861, 3945.44185148324, 112.429477562055, 66.6248755923291]\n", "[4281.68927048827, 3799.69993612502, 93.6912313017128, 60.3787935055482]\n", "[4458.6615962804, 3839.25845600796, 97.8552860262333, 54.1327114187674]\n", "[3550.89766633491, 4095.34782156598, 81.1990671281515, 66.6248755923291]\n"]}], "source": ["for item in result_values[\"bbox\"]:\n", "    \n", "    print(item)"]}, {"cell_type": "code", "execution_count": null, "id": "651e221f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a1f8a437", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d7b3eec2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "71e6f0a6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bc83cdbd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2cf464d8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "066b6085", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6e19bfc6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "af278aa8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fc895799", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3f989161", "metadata": {}, "outputs": [], "source": ["result_values['bbox']."]}, {"cell_type": "code", "execution_count": null, "id": "431bd5e3-19b8-4473-b5f2-07460645bd12", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 14, "id": "2e2b725d-2c17-4191-87d6-b76cd869ca62", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'MTrightsorted' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[14], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Compare image 'ID' columns and return annotations \u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m result_valuesright \u001b[38;5;241m=\u001b[39m annotationsdf[annotationsdf[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mimage_id\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39misin(\u001b[43mMTrightsorted\u001b[49m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mid\u001b[39m\u001b[38;5;124m'\u001b[39m])]\n", "\u001b[0;31mNameError\u001b[0m: name 'MTrightsorted' is not defined"]}], "source": ["# Compare image 'ID' columns and return annotations \n", "result_valuesright = annotationsdf[annotationsdf['image_id'].isin(MTrightsorted['id'])]"]}, {"cell_type": "code", "execution_count": 62, "id": "438e2ee3-45d1-4789-b428-c18564fd830f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(529, 7)"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["result_valuesright.shape"]}, {"cell_type": "code", "execution_count": 63, "id": "329c5336-6af2-4697-8242-d9471608b45f", "metadata": {}, "outputs": [], "source": ["result_valuesright.to_csv(\"MTonlyrightannotations.csv\",index=False)"]}, {"cell_type": "markdown", "id": "368d46a6-a4bf-443e-b5ee-4e21fd1d5a89", "metadata": {}, "source": ["### MT left COCO file"]}, {"cell_type": "code", "execution_count": 26, "id": "ac1a6bcd-0ce9-4b87-8c4a-679c4619136d", "metadata": {}, "outputs": [], "source": ["datapath = \"/home/<USER>/Dropbox/Conservation/MWS_LabArusha/MTonlyleftannotations.csv\""]}, {"cell_type": "code", "execution_count": 15, "id": "e844f260-cf39-4a71-9c92-22e761e93057", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>3531.029913</td>\n", "      <td>[3136.4668019044, 1525.14141578463, -60.966167...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>5315.129237</td>\n", "      <td>[3136.4668019044, 1576.96265824868, 67.0627843...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>12456.356031</td>\n", "      <td>[1456.64331726931, 1710.85830811493, 115.45772...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>20377.265781</td>\n", "      <td>[1346.86384096244, 1915.27526399669, 149.52721...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>4413.641252</td>\n", "      <td>[1519.10405378873, 1877.42027216673, 52.996988...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1           14           []   3531.029913   \n", "1   2         1           14           []   5315.129237   \n", "2   3         2            9           []  12456.356031   \n", "3   4         2            9           []  20377.265781   \n", "4   5         2            9           []   4413.641252   \n", "\n", "                                                bbox  iscrowd  \n", "0  [3136.4668019044, 1525.14141578463, -60.966167...        0  \n", "1  [3136.4668019044, 1576.96265824868, 67.0627843...        0  \n", "2  [1456.64331726931, 1710.85830811493, 115.45772...        0  \n", "3  [1346.86384096244, 1915.27526399669, 149.52721...        0  \n", "4  [1519.10405378873, 1877.42027216673, 52.996988...        0  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["mtleftanno = pd.read_csv(homedir+\"/MTstratum/MTonlyleftannotations.csv\")\n", "mtleftanno.head()"]}, {"cell_type": "code", "execution_count": 17, "id": "c8281f42-a5ea-4e4f-beaf-0d88136baa5e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053746_M0808781.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054011_M0808853.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054013_M0808854.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>7</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054203_M0808909.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   3   7008    4672  KES22_WOT-L_20220909A-053746_M0808781.jpg        1   \n", "1   2   7008    4672  KES22_WOT-L_20220909A-053748_M0808782.jpg        1   \n", "2   6   7008    4672  KES22_WOT-L_20220909A-054011_M0808853.jpg        1   \n", "3   4   7008    4672  KES22_WOT-L_20220909A-054013_M0808854.jpg        1   \n", "4   7   7008    4672  KES22_WOT-L_20220909A-054203_M0808909.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-09  \n", "1    2022-09-09  \n", "2    2022-09-09  \n", "3    2022-09-09  \n", "4    2022-09-09  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["mtleftimages = pd.read_csv(homedir+\"/MTstratum/MTonlyleftimages.csv\")\n", "mtleftimages.head()"]}, {"cell_type": "code", "execution_count": 20, "id": "bc5b969d-9351-4fad-83cb-27aec08851b4", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = mtleftimages.to_dict(orient='records')\n", "coco_dict['annotations'] = mtleftanno.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'mtonlyleftannotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": 19, "id": "25d27e8d-9b16-49d6-80ad-d0457b4526a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'id': 1,\n", "  'name': 'KAZA Elephant Survey',\n", "  'url': 'https://www.kavangozambezi.org/kaza-elephant-survey/'}]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["coco_data[\"licenses\"]"]}, {"cell_type": "code", "execution_count": 21, "id": "36226de4-c23f-45dd-8d40-92c76b860f57", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>484</td>\n", "      <td>60</td>\n", "      <td>18</td>\n", "      <td>[]</td>\n", "      <td>2619.726101</td>\n", "      <td>[6305.47752808989, 2412.17850990197, 54.488741...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>485</td>\n", "      <td>61</td>\n", "      <td>26</td>\n", "      <td>[]</td>\n", "      <td>2843.458701</td>\n", "      <td>[6193.65061344198, 1428.3539729611, 56.6255545...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>486</td>\n", "      <td>61</td>\n", "      <td>26</td>\n", "      <td>[]</td>\n", "      <td>3871.943763</td>\n", "      <td>[6258.82342151602, 1434.76441309953, 56.625554...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>487</td>\n", "      <td>62</td>\n", "      <td>19</td>\n", "      <td>[]</td>\n", "      <td>3725.801057</td>\n", "      <td>[5034.88627559047, 1701.07912114624, 75.709983...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>488</td>\n", "      <td>62</td>\n", "      <td>19</td>\n", "      <td>[]</td>\n", "      <td>1766.173001</td>\n", "      <td>[5214.69748678276, 1981.2060606879, 32.1767430...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  image_id  category_id segmentation         area  \\\n", "0  484        60           18           []  2619.726101   \n", "1  485        61           26           []  2843.458701   \n", "2  486        61           26           []  3871.943763   \n", "3  487        62           19           []  3725.801057   \n", "4  488        62           19           []  1766.173001   \n", "\n", "                                                bbox  iscrowd  \n", "0  [6305.47752808989, 2412.17850990197, 54.488741...        0  \n", "1  [6193.65061344198, 1428.3539729611, 56.6255545...        0  \n", "2  [6258.82342151602, 1434.76441309953, 56.625554...        0  \n", "3  [5034.88627559047, 1701.07912114624, 75.709983...        0  \n", "4  [5214.69748678276, 1981.2060606879, 32.1767430...        0  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["mtrightanno = pd.read_csv(homedir+\"/MTstratum/MTonlyrightannotations.csv\")\n", "mtrightanno.head()"]}, {"cell_type": "code", "execution_count": 23, "id": "a55bfcb9-13ee-4468-bb3e-31c973044267", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>60</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220909A-053330_M0707703.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>61</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220909A-053927_M0707881.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>62</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220909A-053936_M0707885.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>65</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220909A-054030_M0707912.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>66</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220909A-054032_M0707913.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0  60   7008    4672  KES22_WOT-R_20220909A-053330_M0707703.jpg        1   \n", "1  61   7008    4672  KES22_WOT-R_20220909A-053927_M0707881.jpg        1   \n", "2  62   7008    4672  KES22_WOT-R_20220909A-053936_M0707885.jpg        1   \n", "3  65   7008    4672  KES22_WOT-R_20220909A-054030_M0707912.jpg        1   \n", "4  66   7008    4672  KES22_WOT-R_20220909A-054032_M0707913.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-09  \n", "1    2022-09-09  \n", "2    2022-09-09  \n", "3    2022-09-09  \n", "4    2022-09-09  "]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["mtrightimages = pd.read_csv(homedir+\"/MTstratum/MTonlyrightimagespos.csv\")\n", "mtrightimages.head()"]}, {"cell_type": "code", "execution_count": 24, "id": "6df4b37a-be3d-44d3-a10a-b92fe5a7ec73", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dictright = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dictright['images'] = mtrightimages.to_dict(orient='records')\n", "coco_dictright['annotations'] = mtrightanno.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'mtonlyrightannotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dictright, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "id": "254708b5-3db5-489a-bc35-918c449acee8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16dd420b-4f4d-42de-9aa8-1917a1d7a760", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "043cfaf2-580a-4a72-9871-0e8bbad31a7f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}