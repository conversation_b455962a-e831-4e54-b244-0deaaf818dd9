## Deleting Images   
### The steps to delete images on Scout   
1. Get complete list of images in  `.scout-hidden/images`   
2. Export unfiltered `CSV` of all tasks from Scout   
3. Use the` CSV` as a filter of the directory list to remove images still associated with active Tasks in Scout.   
4. Write a script to delete the images that didn't get filtered out because these are unreferenced by <PERSON>.   
   
### Executing the steps above   
1. Getting complete list of images   
   
On the Scout server terminal, run the script ![imagelistscout.sh](_utilslab/imagelistscout.sh). This script will save the list of images to ![filelistscout.csv](_media/filelistscout.csv) file, in this case a list of `31680` images   
![Imageslist](_media/filelist.png)    
2. [Deleting](https://docs.wildme.org/product-docs/en/scout/lab-lead/managing-tasks/) Images and Tasks on Scout interface that are longer used:   
  a) Filter the task first on Scout   
  b) Delete images associated with the task   
  c) Delete the task   
  d) Repeat steps a-c for all tasks that you want to delete    
  The steps a-d remove references of the images to Scout Tasks   
3. Export Unfiltered CSV of all tasks from Scout   
Exported [images CSV data](_media/allscout-export-images.csv) from Scout for all images to the CSV file. This was achieved by:   
  - creating on Scout a task called AllImages for all images (in this case `31198`)    
  - filtered the AllImages task on Scout and exported CSV image data to CSV ![file](_media/allscout-export-images.csv)   
   
  ![Allimages](_media/allImgs.png)    
  ![Export](_media/export.png)    
4. Comparing the lists from steps 1 and 3 to remove images still associated with Tasks in Scout   
  - achieved using the Jupyter notebook file ![scoutdeleteAnalysis.ipynb](_utilslab/scoutdeleteAnalysis.ipynb)    
  - on the analysis, the data on the CSV file from exported CSV image data contains the `Image Filename` which is the original filename of the image. The list of images on step 1 is the new filename after ingesting data to Scout. So the two files can't be compared.   
  - the complete image data details are then obtained from scout dabatase (Mongodb)   
5. Export images data from scout database (Mongodb):   
*Accessing Mongodb to export data*   
> check the details of the active docker container running scout   

`mwsadmin@scoutbeast:~$ docker ps`   
`CONTAINER ID IMAGE COMMAND CREATED STATUS PORTS NAMES`   
`88a6061eb7ab wildme/scout:latest "/opt/nvidia/nvidia\_…" 3 days ago Up 3 days 0.0.0.0:1337→1337/tcp, :::1337→1337/tcp funny\_kalam`   
> get into the docker container to access the Mongodb   

`mwsadmin@scoutbeast:~$ docker exec -it funny\_kalam bash`   
`root@88a6061eb7ab:/usr/src/app# mongo`   
> on the Mongodb prompt, select the database with data (scout-db-1680159579745 0.018GB), view the tables (collections) and sample record on images table (collection)   

> show dbs   

`admin 0.000GB`   
`config 0.000GB`   
`local 0.000GB`   
`scout-db-1679213305604 0.000GB`   
`scout-db-1679663729406 0.000GB`   
`scout-db-1680159579745 0.018GB`   
`test 0.000GB`   
> use scout-db-scout-db-1680159579745   

`switched to db scout-db-scout-db-1680159579745`   
> show collections   

`annotations`   
`errors`   
`exports`   
`groundtruths`   
`images`   
`labels`   
`linedivisions`   
`queuedexportpieces`   
`queuedimages`   
`sequencedpairs`   
`tags`   
`tasks`   
`unqueuedtasks`   
`users`   
`workers`   
> db.images.find().pretty()   

`{`   
`"\_id" : ObjectId("641da782dce9f2004c174a83"),`   
`"originalFilenameLower" : "kes22\_iim-l\_20220830a-074943\_m0309912",`   
`"originalFilename" : "KES22\_IIM-L\_20220830A-074943\_M0309912",`   
`"filename" : "1679665025865.jpg",`   
`"exifTimestamp" : 1661845783000,`   
`"fullPath" : "/mnt/nas/scout/.scout-hidden/images/1679665025865.jpg",`   
`"currentExtension" : "jpg",`   
`"originalExtension" : "jpg",`   
`"createdAt" : 1679665026001,`   
`"updatedAt" : 1679665026001,`   
`"taskIds" : null,`   
`"gtComplete" : false`   
`}`   
`{`   
`"\_id" : ObjectId("641da7aadce9f2004c174a91"),`   
`"originalFilenameLower" : "kes22\_iim-l\_20220830a-075119\_m0300008",`   
`"originalFilename" : "KES22\_IIM-L\_20220830A-075119\_M0300008",`   
`"filename" : "1679665066190.jpg",`   
`"exifTimestamp" : 1661845879000,`   
`"fullPath" : "/mnt/nas/scout/.scout-hidden/images/1679665066190.jpg",`   
`"currentExtension" : "jpg",`   
`"originalExtension" : "jpg",`   
`"createdAt" : 1679665066317,`   
`"updatedAt" : 1679666910928,`   
`"taskIds" : [`   
`"641da8ffd7a7fe0045375f38"`   
`],`   
`"gtComplete" : true`   
`}`   
> on docker prompt export the images table from mongodb   

`root@88a6061eb7ab:/usr/src/app# mongoexport --db scout-db-1680159579745 --collection images --out imagesdbdata.json -- fields "\_id,originalFilename,filename,taskIds"`   
`2023-11-06T10:03:07.820+0000 connected to: localhost`   
`2023-11-06T10:03:08.080+0000 exported 31226 records`   
`root@88a6061eb7ab:/usr/src/app#`   
> copy the exported db file from docker to local machine (mwsadmin)   

`mwsadmin@scoutbeast:~/Documents$ docker cp 88a6061eb7ab://usr/src/app/imagesdbdata.json ./`   
`Successfully copied 8.22MB to /home/<USER>/Documents/ ./`   
`mwsadmin@scoutbeast:~/Documents$`   
6. Continue with analysis on Jupyter notebook file ![scoutdeleteAnalysis.ipynb](_utilslab/scoutdeleteAnalysis.ipynb)    
  - the output is `![filestodelete.csv](_media/filestodelete.csv)`   
  - There are 454 files filtered, they're not referenced to Scout   
7. Deleting the files   
  > on terminal check the status before deleting files   

  **Before**
   
  `mwsadmin@scoutbeast:~$ ls /nas/.scout-hidden/images \|wc`   
  `31680 31680 570240`   
  `mwsadmin@scoutbeast:~$ df -h /`   
  `Filesystem Size Used Avail Use% Mounted on`   
  `/dev/nvme0n1p2 938G 775G 116G 88% /`   
  `mwsadmin@scoutbeast:~$`   
  > use the script to delete the 454 files    

  - run the script from terminal using `./delete\_files.sh /path/to/directory /path/to/csvfile.csv`   
   
  `mwsadmin@scoutbeast:~/Documents$ ./delete\_files.sh /nas/.scout-hidden/images/ /home/<USER>/Documents/filestodelete.csv
[sudo] password for mwsadmin:`
`Deleted: /nas/.scout-hidden/images//1696413597700.jpg`
`Deleted: /nas/.scout-hidden/images//1696413600737.jpg`
`Deleted: /nas/.scout-hidden/images//1696413603520.jpg
Deleted: /nas/.scout-hidden/images//1696413606286.jpg Deleted: /nas/.scout-hidden/images//1696413609152.jpg`   
     
  > on terminal check the status after deleting files   

  **After**
   
  `mwsadmin@scoutbeast:~$ ls /nas/.scout-hidden/images \|wc
31226   31226  562068`
`mwsadmin@scoutbeast:~$ df -h /
Filesystem      Size  Used Avail Use% Mounted on
/dev/nvme0n1p2  938G  769G  122G  87% /
mwsadmin@scoutbeast:~$`   
   
