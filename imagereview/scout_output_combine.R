# Make DB of the annotations
library(tidyverse); library(readxl)
# Read annotation CSV ----
csvs <-
  dir("~/Google Drive/My Drive/_projects/MWS-ArushaLab/Annotations/",
      full.names = T, pattern = ".csv")

scoutdb <- read.csv(csvs[1])
for (c in csvs[2:length(csvs)]) {
  cat(c)
  
  # Check if the file size is greater than zero
  if (file.info(c)$size > 0) {
    # Use tryCatch to handle potential errors during reading
    tmp <- tryCatch({
      read.csv(c)
    }, error = function(e) {
      cat(" - Error reading file:", e$message, "\n")
      return(NULL)  # Return NULL if there's an error
    })
    
    # If tmp is not NULL, bind the data
    if (!is.null(tmp)) {
      scoutdb <- rbind(scoutdb, tmp)
      cat(' - OK\n')
    }
  } else {
    cat(" - File is empty\n")
  }
}

scoutdb %>% write.csv(file = "imagereview/scoutdb.csv")

# 

glimpse(scoutdb)

scoutdb %>% filter