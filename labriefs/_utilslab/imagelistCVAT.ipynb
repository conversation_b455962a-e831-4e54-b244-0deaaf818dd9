{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b4ddb430-6614-4eaa-ab27-339b39f0a1e3", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "be1ceb01-4d1c-44b6-8181-728d886fe035", "metadata": {}, "outputs": [], "source": ["homedir = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023\""]}, {"cell_type": "code", "execution_count": 3, "id": "d01e21f1-4b37-4945-95f9-24a2150a9557", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>Task ID</th>\n", "      <th>ImageFilename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060454_M0309398.jpg</td>\n", "      <td>653a4d3f4a2456004cd8a583</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060452_M0309397.jpg</td>\n", "      <td>653a4d414a2456004cd8a584</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060559_M0309430.jpg</td>\n", "      <td>653a4d444a2456004cd8a585</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060611_M0309436.jpg</td>\n", "      <td>653a4d474a2456004cd8a586</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060615_M0309438.jpg</td>\n", "      <td>653a4d494a2456004cd8a587</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    TaskName                   Task ID  \\\n", "0  SH01-L_85  653a60f36f30500045ef6b69   \n", "1  SH01-L_85  653a60f36f30500045ef6b69   \n", "2  SH01-L_85  653a60f36f30500045ef6b69   \n", "3  SH01-L_85  653a60f36f30500045ef6b69   \n", "4  SH01-L_85  653a60f36f30500045ef6b69   \n", "\n", "                               ImageFilename                  Image ID  \\\n", "0  KES22_IIM-L_20220904A-060454_M0309398.jpg  653a4d3f4a2456004cd8a583   \n", "1  KES22_IIM-L_20220904A-060452_M0309397.jpg  653a4d414a2456004cd8a584   \n", "2  KES22_IIM-L_20220904A-060559_M0309430.jpg  653a4d444a2456004cd8a585   \n", "3  KES22_IIM-L_20220904A-060611_M0309436.jpg  653a4d474a2456004cd8a586   \n", "4  KES22_IIM-L_20220904A-060615_M0309438.jpg  653a4d494a2456004cd8a587   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                False   \n", "1          4672         7008             NaN                False   \n", "2          4672         7008             NaN                False   \n", "3          4672         7008             NaN                False   \n", "4          4672         7008             NaN                False   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg = pd.read_csv(homedir+\"/scoutexports/SHscout-export-images.csv\")\n", "shimg.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "734d87dc-01e9-42be-b4ec-84cf6ec05215", "metadata": {}, "outputs": [{"data": {"text/plain": ["(5581, 11)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "1cad88d3-b5ce-4090-86c8-e56904113a29", "metadata": {}, "outputs": [{"data": {"text/plain": ["5232"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg[\"ImageFilename\"].nunique()"]}, {"cell_type": "code", "execution_count": 6, "id": "f08781c5-39ee-4044-92b7-2a2bcce773f6", "metadata": {}, "outputs": [], "source": ["shimgunique =shimg[\"ImageFilename\"].unique()"]}, {"cell_type": "code", "execution_count": 7, "id": "e469493d-e90f-41a9-9ed3-73e9bd0b8179", "metadata": {}, "outputs": [], "source": ["shimguniquedf = pd.DataFrame(shimgunique,columns=[\"ImageFilename\"])"]}, {"cell_type": "code", "execution_count": 10, "id": "d09071a8-0790-461c-bf88-da23c12674a6", "metadata": {}, "outputs": [], "source": ["shimgdf=shimguniquedf.sort_values(by=['ImageFilename'],ascending=True)"]}, {"cell_type": "code", "execution_count": 11, "id": "12827654-9fa7-4947-b216-de68cf1f0469", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>KES22_IIM-L_20220904A-060442_M0309392.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>KES22_IIM-L_20220904A-060444_M0309393.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>KES22_IIM-L_20220904A-060446_M0309394.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>KES22_IIM-L_20220904A-060448_M0309395.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>KES22_IIM-L_20220904A-060450_M0309396.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                ImageFilename\n", "77  KES22_IIM-L_20220904A-060442_M0309392.jpg\n", "17  KES22_IIM-L_20220904A-060444_M0309393.jpg\n", "10  KES22_IIM-L_20220904A-060446_M0309394.jpg\n", "27  KES22_IIM-L_20220904A-060448_M0309395.jpg\n", "60  KES22_IIM-L_20220904A-060450_M0309396.jpg"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["shimgdf.head()"]}, {"cell_type": "code", "execution_count": 12, "id": "9a906ecd-a5a5-49ee-9cf0-b8d66f5ef322", "metadata": {}, "outputs": [{"data": {"text/plain": ["72"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg[\"TaskName\"].nunique()"]}, {"cell_type": "code", "execution_count": 12, "id": "81965318-76ce-4e39-b9c5-c3f5dd06d1dd", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['SH01-L_85', 'SH01-L_159', 'SH02-L_100', 'SH02-L_196',\n", "       'SH02-L_062_36', 'SH03-L_126', 'SH03-L_206', 'SH03-L_36',\n", "       'SH03-L_63', 'SH04-L_135', 'SH04-L_224', 'SH04-L_064_58',\n", "       'SH05-L_138', 'SH05-L_218', 'SH05-L_38', 'SH05-L_65', 'SH06-L_155',\n", "       'SH06-L_235', 'SH06-L_37', 'SH07-L_148', 'SH07-L_248',\n", "       'SH08-L_343', 'SH08-L_428', 'SH09-L_513', 'SH09-L_586',\n", "       'SH10-L_74', 'SH10-L_138', 'SH11-L_219', 'SH11-L_76', 'SH12-L_157',\n", "       'SH12-L_206', 'SH12-L_32', 'SH13-L_118', 'SH13-L_191', 'SH01-R_85',\n", "       'SH01-R_159', 'SH02-R_100', 'SH02-L_197', 'SH02-R_062_37',\n", "       'SH03-R_126', 'SH03-R_208', 'SH03-R_63', 'SH04-R_149',\n", "       'SH04-R_225', 'SH04-R_064_58', 'SH05-R_138', 'SH05-R_219',\n", "       'SH05-R_65', 'SH06-R_155', 'SH06-R_235', 'SH06-R_37', 'SH07-R_149',\n", "       'SH07-R_249', 'SH08-R_343', 'SH08-R_430', 'SH09-R_515',\n", "       'SH09-R_589', 'SH10-R_75', 'SH10-R_139', 'SH11-R_221', 'SH11-R_76',\n", "       'SH12-R_157', 'SH12-R_207', 'SH12-R_32', 'SH13-R_118',\n", "       'SH13-R_192', 'SH14-R_80', 'SH14-R_151', 'SH15-R', 'SH14-L_80',\n", "       'SH14-L_151', 'SH15-L_197'], dtype=object)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg[\"TaskName\"].unique()"]}, {"cell_type": "code", "execution_count": 13, "id": "7dd8b6c9-967f-4b2a-9649-04b7dbdc4d31", "metadata": {}, "outputs": [], "source": ["shtasks = shimg[\"TaskName\"].unique()"]}, {"cell_type": "code", "execution_count": 14, "id": "83777c7d-1aa0-4cc2-bf91-44634f862a13", "metadata": {}, "outputs": [], "source": ["shtaskdf = pd.DataFrame(shtasks,columns=[\"TaskName\"])"]}, {"cell_type": "code", "execution_count": 15, "id": "3d864db6-202c-4c65-b529-c23452740dd3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SH01-L_85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SH01-L_159</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SH02-L_100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SH02-L_196</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SH02-L_062_36</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        TaskName\n", "0      SH01-L_85\n", "1     SH01-L_159\n", "2     SH02-L_100\n", "3     SH02-L_196\n", "4  SH02-L_062_36"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["shtaskdf.head()"]}, {"cell_type": "code", "execution_count": 16, "id": "42bb5619-625f-4c5b-a38d-e6c8545f95ec", "metadata": {}, "outputs": [{"data": {"text/plain": ["(72, 1)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["shtaskdf.shape"]}, {"cell_type": "code", "execution_count": 20, "id": "d107c8e6-183e-4da7-8d04-ecf82f8f49e9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>2992.284408</td>\n", "      <td>3582.150410</td>\n", "      <td>83.281342</td>\n", "      <td>108.265744</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1.698390e+12</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>3119.288454</td>\n", "      <td>3278.173512</td>\n", "      <td>110.347778</td>\n", "      <td>127.004046</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1.698390e+12</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>3221.308098</td>\n", "      <td>2886.751205</td>\n", "      <td>112.429812</td>\n", "      <td>189.465053</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1.698390e+12</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>3106.796253</td>\n", "      <td>2818.044098</td>\n", "      <td>81.199308</td>\n", "      <td>106.183711</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1.698390e+12</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>312.139451</td>\n", "      <td>3096.658157</td>\n", "      <td>77.035241</td>\n", "      <td>74.953208</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1.698390e+12</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     TaskName                   TaskID   \\\n", "0  SH01-L_159  653a61286f30500045ef6b6b   \n", "1  SH01-L_159  653a61286f30500045ef6b6b   \n", "2  SH01-L_159  653a61286f30500045ef6b6b   \n", "3  SH01-L_159  653a61286f30500045ef6b6b   \n", "4  SH01-L_159  653a61286f30500045ef6b6b   \n", "\n", "                               ImageFilename                 ImageID    \\\n", "0  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "1  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "2  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "3  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "4  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "\n", "          BoxX         BoxY        BoxW        BoxH     Label  \\\n", "0  2992.284408  3582.150410   83.281342  108.265744  elephant   \n", "1  3119.288454  3278.173512  110.347778  127.004046  elephant   \n", "2  3221.308098  2886.751205  112.429812  189.465053  elephant   \n", "3  3106.796253  2818.044098   81.199308  106.183711  elephant   \n", "4   312.139451  3096.658157   77.035241   74.953208  elephant   \n", "\n", "   LabelConfidence Assignee     Timestamp  IsGroundTruth  ExcludedByLine  \n", "0              NaN  hgeorge  1.698390e+12          False             NaN  \n", "1              NaN  hgeorge  1.698390e+12          False             NaN  \n", "2              NaN  hgeorge  1.698390e+12          False             NaN  \n", "3              NaN  hgeorge  1.698390e+12          False             NaN  \n", "4              NaN  hgeorge  1.698390e+12          False             NaN  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["shannodf = pd.read_csv(homedir+\"/scoutexports/SHscout-export-annotations.csv\")\n", "shannodf.head()"]}, {"cell_type": "code", "execution_count": 23, "id": "3c0acbc2-2914-4a06-a54a-c51e656e696c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1090, 14)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["shannodf.shape"]}, {"cell_type": "code", "execution_count": 68, "id": "a7436419-97c4-4a8b-83d8-501debb9fe63", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>SH01-L_159</td>\n", "      <td>KES22_IIM-L_20220904A-060755_M0309488.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>SH01-L_159</td>\n", "      <td>KES22_IIM-L_20220904A-060755_M0309488.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>SH01-L_159</td>\n", "      <td>KES22_IIM-L_20220904A-060755_M0309488.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>SH01-L_159</td>\n", "      <td>KES22_IIM-L_20220904A-060755_M0309488.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>SH01-L_159</td>\n", "      <td>KES22_IIM-L_20220904A-060755_M0309488.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      TaskName                              ImageFilename\n", "23  SH01-L_159  KES22_IIM-L_20220904A-060755_M0309488.jpg\n", "28  SH01-L_159  KES22_IIM-L_20220904A-060755_M0309488.jpg\n", "27  SH01-L_159  KES22_IIM-L_20220904A-060755_M0309488.jpg\n", "26  SH01-L_159  KES22_IIM-L_20220904A-060755_M0309488.jpg\n", "25  SH01-L_159  KES22_IIM-L_20220904A-060755_M0309488.jpg"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["#task names for positive images\n", "tasksbyimg = shannodf[['TaskName','ImageFilename']]\n", "tasksbyimg= tasksbyimg.sort_values(by='ImageFilename',ascending=True)\n", "tasksbyimg.head()"]}, {"cell_type": "code", "execution_count": 69, "id": "687a85e3-72f1-4c12-97f3-0127295b4566", "metadata": {}, "outputs": [], "source": ["tasksbyimg.to_csv(\"TasknameSHbyimage.csv\",index=False)"]}, {"cell_type": "markdown", "id": "70c4a0d3-6db7-4c6c-bbcc-b8f170ab8dec", "metadata": {}, "source": ["### Positive images"]}, {"cell_type": "code", "execution_count": 21, "id": "cef93329-87c8-4a33-aa4e-e4132ab36a20", "metadata": {}, "outputs": [{"data": {"text/plain": ["204"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["#positive images\n", "\n", "shannodf[\"ImageFilename\"].nunique()"]}, {"cell_type": "code", "execution_count": 22, "id": "33f52840-fa1e-45f5-b39d-46ae06dca693", "metadata": {}, "outputs": [], "source": ["posimageSH = shannodf[\"ImageFilename\"].unique()"]}, {"cell_type": "code", "execution_count": 24, "id": "1cb7547d-d42d-4574-81a7-284b735a159a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220904A-060823_M0309502.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220904A-060847_M0309514.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220904A-060951_M0309546.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220904A-060755_M0309488.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename\n", "0  KES22_IIM-L_20220904A-060827_M0309504.jpg\n", "1  KES22_IIM-L_20220904A-060823_M0309502.jpg\n", "2  KES22_IIM-L_20220904A-060847_M0309514.jpg\n", "3  KES22_IIM-L_20220904A-060951_M0309546.jpg\n", "4  KES22_IIM-L_20220904A-060755_M0309488.jpg"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["posimageSHdf = pd.DataFrame(posimageSH,columns=[\"ImageFilename\"])\n", "posimageSHdf.head()"]}, {"cell_type": "code", "execution_count": 25, "id": "aa785cfb-a0fa-4c65-be4a-06574eea0ee2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220904A-060755_M0309488.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>KES22_IIM-L_20220904A-060757_M0309489.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220904A-060823_M0309502.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>KES22_IIM-L_20220904A-060825_M0309503.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename\n", "4  KES22_IIM-L_20220904A-060755_M0309488.jpg\n", "7  KES22_IIM-L_20220904A-060757_M0309489.jpg\n", "1  KES22_IIM-L_20220904A-060823_M0309502.jpg\n", "6  KES22_IIM-L_20220904A-060825_M0309503.jpg\n", "0  KES22_IIM-L_20220904A-060827_M0309504.jpg"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["posimageSHdf= posimageSHdf.sort_values(by=\"ImageFilename\",ascending=True)\n", "posimageSHdf.head()"]}, {"cell_type": "markdown", "id": "fa9aeb65-5d31-4472-bd81-0308fcc6c805", "metadata": {}, "source": ["### Negative images"]}, {"cell_type": "code", "execution_count": 26, "id": "1883518f-b727-47aa-a9a2-afd3cf53bbae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ImageFilename    5232\n", "negative_img1    5232\n", "negative_img2     204\n", "dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "      <th>negative_img1</th>\n", "      <th>negative_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220904A-060442_M0309392.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220904A-060444_M0309393.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220904A-060446_M0309394.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220904A-060448_M0309395.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220904A-060450_M0309396.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename negative_img1 negative_img2\n", "0  KES22_IIM-L_20220904A-060442_M0309392.jpg      imageid1           NaN\n", "1  KES22_IIM-L_20220904A-060444_M0309393.jpg      imageid1           NaN\n", "2  KES22_IIM-L_20220904A-060446_M0309394.jpg      imageid1           NaN\n", "3  KES22_IIM-L_20220904A-060448_M0309395.jpg      imageid1           NaN\n", "4  KES22_IIM-L_20220904A-060450_M0309396.jpg      imageid1           NaN"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["#find the negative images 5232  - 204  = 5028 images\n", "shimgdf['negative'] = 'imageid1'\n", "posimageSHdf['negative'] ='imageid2'\n", "\n", "\n", "merged_df = shimgdf.merge(posimageSHdf, on='ImageFilename',how='left', suffixes=('_img1','_img2'))\n", "print(merged_df.count())\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": 35, "id": "4269f2f0-4944-4fb4-bd71-3bcfe7a22647", "metadata": {}, "outputs": [{"data": {"text/plain": ["ImageFilename    5028\n", "negative_img1    5028\n", "negative_img2       0\n", "dtype: int64"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["negative_images = merged_df[merged_df['negative_img2'].isna()]\n", "\n", "\n", "negative_images.count()"]}, {"cell_type": "code", "execution_count": 36, "id": "4e85342d-32a9-402e-9feb-d9b34e2e7f63", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "      <th>negative_img1</th>\n", "      <th>negative_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220904A-060442_M0309392.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220904A-060444_M0309393.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220904A-060446_M0309394.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220904A-060448_M0309395.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220904A-060450_M0309396.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename negative_img1 negative_img2\n", "0  KES22_IIM-L_20220904A-060442_M0309392.jpg      imageid1           NaN\n", "1  KES22_IIM-L_20220904A-060444_M0309393.jpg      imageid1           NaN\n", "2  KES22_IIM-L_20220904A-060446_M0309394.jpg      imageid1           NaN\n", "3  KES22_IIM-L_20220904A-060448_M0309395.jpg      imageid1           NaN\n", "4  KES22_IIM-L_20220904A-060450_M0309396.jpg      imageid1           NaN"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["negative_images.head()"]}, {"cell_type": "code", "execution_count": 37, "id": "c7d6975d-3653-4fe3-b11a-ac03cd830e8e", "metadata": {}, "outputs": [], "source": ["negative_images= negative_images.drop(['negative_img1','negative_img2'],axis=1)"]}, {"cell_type": "code", "execution_count": 38, "id": "d85b9bae-47ae-4640-a519-a940bba549f7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220904A-060442_M0309392.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220904A-060444_M0309393.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220904A-060446_M0309394.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220904A-060448_M0309395.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220904A-060450_M0309396.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename\n", "0  KES22_IIM-L_20220904A-060442_M0309392.jpg\n", "1  KES22_IIM-L_20220904A-060444_M0309393.jpg\n", "2  KES22_IIM-L_20220904A-060446_M0309394.jpg\n", "3  KES22_IIM-L_20220904A-060448_M0309395.jpg\n", "4  KES22_IIM-L_20220904A-060450_M0309396.jpg"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["negative_images.head()"]}, {"cell_type": "code", "execution_count": 39, "id": "f95fd2a6-eb52-41dc-98d5-bb1bb546387f", "metadata": {}, "outputs": [], "source": ["negative_images.to_csv(\"SHnegative.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 40, "id": "c1bccb06-b51f-4a65-845b-22a9a9aba51b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "      <th>negative</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220904A-060755_M0309488.jpg</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>KES22_IIM-L_20220904A-060757_M0309489.jpg</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220904A-060823_M0309502.jpg</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>KES22_IIM-L_20220904A-060825_M0309503.jpg</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename  negative\n", "4  KES22_IIM-L_20220904A-060755_M0309488.jpg  imageid2\n", "7  KES22_IIM-L_20220904A-060757_M0309489.jpg  imageid2\n", "1  KES22_IIM-L_20220904A-060823_M0309502.jpg  imageid2\n", "6  KES22_IIM-L_20220904A-060825_M0309503.jpg  imageid2\n", "0  KES22_IIM-L_20220904A-060827_M0309504.jpg  imageid2"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["posimageSHdf.head()"]}, {"cell_type": "code", "execution_count": 52, "id": "5758ec20-75ed-4a82-9d81-9754cb1dc685", "metadata": {}, "outputs": [], "source": ["posimageSHdf.drop(['negative'],axis=1,inplace=True)"]}, {"cell_type": "code", "execution_count": 53, "id": "857fa2e3-0fbc-4c3a-937d-45dbf6d5d76f", "metadata": {}, "outputs": [], "source": ["posimageSHdf.reset_index(inplace=True,drop=True)"]}, {"cell_type": "code", "execution_count": 54, "id": "1cfffcbf-9f4f-4254-900f-7bd4dce76044", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>index</th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>4</td>\n", "      <td>KES22_IIM-L_20220904A-060755_M0309488.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>7</td>\n", "      <td>KES22_IIM-L_20220904A-060757_M0309489.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>KES22_IIM-L_20220904A-060823_M0309502.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6</td>\n", "      <td>KES22_IIM-L_20220904A-060825_M0309503.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   index                              ImageFilename\n", "0      4  KES22_IIM-L_20220904A-060755_M0309488.jpg\n", "1      7  KES22_IIM-L_20220904A-060757_M0309489.jpg\n", "2      1  KES22_IIM-L_20220904A-060823_M0309502.jpg\n", "3      6  KES22_IIM-L_20220904A-060825_M0309503.jpg\n", "4      0  KES22_IIM-L_20220904A-060827_M0309504.jpg"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["posimageSHdf.head()"]}, {"cell_type": "code", "execution_count": 55, "id": "239e84b6-cbc4-4cee-9970-2a6f6f224645", "metadata": {}, "outputs": [], "source": ["posimageSHdf.drop(['index'],axis=1,inplace=True)"]}, {"cell_type": "markdown", "id": "3083545c-0e63-4b4b-b237-fc1daf28b94e", "metadata": {}, "source": ["### CVAT image list\n", "\n", "- positive image\n", "- adjacent to positive\n", "- a negative (10%)"]}, {"cell_type": "code", "execution_count": 56, "id": "eceaa1d1-c90d-413d-9a89-13bbcb0543d7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220904A-060755_M0309488.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220904A-060757_M0309489.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220904A-060823_M0309502.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220904A-060825_M0309503.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename\n", "0  KES22_IIM-L_20220904A-060755_M0309488.jpg\n", "1  KES22_IIM-L_20220904A-060757_M0309489.jpg\n", "2  KES22_IIM-L_20220904A-060823_M0309502.jpg\n", "3  KES22_IIM-L_20220904A-060825_M0309503.jpg\n", "4  KES22_IIM-L_20220904A-060827_M0309504.jpg"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["posimageSHdf.head()"]}, {"cell_type": "code", "execution_count": 57, "id": "d60afc70-0dfc-4d01-a097-eb72a698ebdd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220904A-060442_M0309392.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220904A-060444_M0309393.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220904A-060446_M0309394.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220904A-060448_M0309395.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220904A-060450_M0309396.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename\n", "0  KES22_IIM-L_20220904A-060442_M0309392.jpg\n", "1  KES22_IIM-L_20220904A-060444_M0309393.jpg\n", "2  KES22_IIM-L_20220904A-060446_M0309394.jpg\n", "3  KES22_IIM-L_20220904A-060448_M0309395.jpg\n", "4  KES22_IIM-L_20220904A-060450_M0309396.jpg"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["negative_images.head()"]}, {"cell_type": "code", "execution_count": 58, "id": "1c8902b6-698a-48aa-8a85-835531a8030a", "metadata": {}, "outputs": [], "source": ["posimageSHdf.to_csv(\"SHpositive.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "de596083-ad70-4fa1-9eed-10826c241e65", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1e645e22-525c-4a14-a551-2839928325e1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c4ac0f52-6fa0-4cd5-803d-d3cb298c7606", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "07b08c21-31b5-4d49-bda3-96ea5aad50ea", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}