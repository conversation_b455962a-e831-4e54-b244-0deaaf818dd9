{"cells": [{"cell_type": "code", "execution_count": 1, "id": "44beacae", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "d9fa3b83", "metadata": {}, "outputs": [], "source": ["homedir=\"/home/<USER>/Dropbox/Conservation/MWSLab-2023\""]}, {"cell_type": "markdown", "id": "ec5fa45a", "metadata": {}, "source": ["1. The complete list of images in `/nas/.scout-hidden/images` that was exported to `filelistscout.csv`"]}, {"cell_type": "code", "execution_count": 3, "id": "dabbe268", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1692193522043.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1692193526742.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1692193531544.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1692193541212.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1692193545986.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            filename\n", "0  1692193522043.jpg\n", "1  1692193526742.jpg\n", "2  1692193531544.jpg\n", "3  1692193541212.jpg\n", "4  1692193545986.jpg"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["#list of all images \n", "imagelistdf = pd.read_csv(homedir+\"/_media/filelistscout.csv\")\n", "imagelistdf.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "82c6e2c4", "metadata": {}, "outputs": [{"data": {"text/plain": ["filename    31680\n", "dtype: int64"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["#total no of images in scout-hidden\n", "imagelistdf.count()"]}, {"cell_type": "markdown", "id": "6d574461", "metadata": {}, "source": ["2. View the images list from Export CSV image data of all tasks from Scout\n", "    "]}, {"cell_type": "code", "execution_count": 5, "id": "01380143", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AllImages</td>\n", "      <td>6548a375492c290045f0b7b4</td>\n", "      <td>DSC03452.jpg</td>\n", "      <td>64dcd2f2e31598004c4c378d</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AllImages</td>\n", "      <td>6548a375492c290045f0b7b4</td>\n", "      <td>DSC03462.jpg</td>\n", "      <td>64dcd2f6e31598004c4c378e</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AllImages</td>\n", "      <td>6548a375492c290045f0b7b4</td>\n", "      <td>DSC03822.jpg</td>\n", "      <td>64dcd2fbe31598004c4c378f</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AllImages</td>\n", "      <td>6548a375492c290045f0b7b4</td>\n", "      <td>DSC03852.jpg</td>\n", "      <td>64dcd305e31598004c4c3791</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AllImages</td>\n", "      <td>6548a375492c290045f0b7b4</td>\n", "      <td>DSC03842.jpg</td>\n", "      <td>64dcd30ae31598004c4c3792</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Task Name                   Task ID Image Filename  \\\n", "0  AllImages  6548a375492c290045f0b7b4   DSC03452.jpg   \n", "1  AllImages  6548a375492c290045f0b7b4   DSC03462.jpg   \n", "2  AllImages  6548a375492c290045f0b7b4   DSC03822.jpg   \n", "3  AllImages  6548a375492c290045f0b7b4   DSC03852.jpg   \n", "4  AllImages  6548a375492c290045f0b7b4   DSC03842.jpg   \n", "\n", "                   Image ID  Image Height  Image Width  WIC Confidence  \\\n", "0  64dcd2f2e31598004c4c378d          6336         9504             NaN   \n", "1  64dcd2f6e31598004c4c378e          6336         9504             NaN   \n", "2  64dcd2fbe31598004c4c378f          6336         9504             NaN   \n", "3  64dcd305e31598004c4c3791          6336         9504             NaN   \n", "4  64dcd30ae31598004c4c3792          6336         9504             NaN   \n", "\n", "   Ground Truth Status Exclusion Side  Inclusion Top X Fraction  \\\n", "0                False          right                       NaN   \n", "1                False          right                       NaN   \n", "2                False          right                       NaN   \n", "3                False          right                       NaN   \n", "4                False          right                       NaN   \n", "\n", "   Inclusion Bottom X Fraction  \n", "0                          NaN  \n", "1                          NaN  \n", "2                          NaN  \n", "3                          NaN  \n", "4                          NaN  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# exported CSV image data\n", "scoutdatadf = pd.read_csv(homedir+ \"/_media/allscout-export-images.csv\")\n", "scoutdatadf.head()\n"]}, {"cell_type": "code", "execution_count": 6, "id": "d6e7cdc6", "metadata": {}, "outputs": [{"data": {"text/plain": ["Task Name                      31226\n", "Task ID                        31226\n", "Image Filename                 31226\n", "Image ID                       31226\n", "Image Height                   31226\n", "Image Width                    31226\n", "WIC Confidence                     0\n", "Ground Truth Status            31226\n", "Exclusion Side                 31226\n", "Inclusion Top X Fraction           0\n", "Inclusion Bottom X Fraction        0\n", "dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["#count of scoutdb images\n", "scoutdatadf.count()"]}, {"cell_type": "code", "execution_count": 8, "id": "e98e1503", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>originalFilename</th>\n", "      <th>filename</th>\n", "      <th>taskIds</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'$oid': '64dcd2f2e31598004c4c378d'}</td>\n", "      <td>DSC03452</td>\n", "      <td>1692193522043.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>{'$oid': '64dcd2f6e31598004c4c378e'}</td>\n", "      <td>DSC03462</td>\n", "      <td>1692193526742.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>{'$oid': '64dcd2fbe31598004c4c378f'}</td>\n", "      <td>DSC03822</td>\n", "      <td>1692193531544.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>{'$oid': '64dcd305e31598004c4c3791'}</td>\n", "      <td>DSC03852</td>\n", "      <td>1692193541212.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>{'$oid': '64dcd30ae31598004c4c3792'}</td>\n", "      <td>DSC03842</td>\n", "      <td>1692193545986.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                    _id originalFilename           filename  \\\n", "0  {'$oid': '64dcd2f2e31598004c4c378d'}         DSC03452  1692193522043.jpg   \n", "1  {'$oid': '64dcd2f6e31598004c4c378e'}         DSC03462  1692193526742.jpg   \n", "2  {'$oid': '64dcd2fbe31598004c4c378f'}         DSC03822  1692193531544.jpg   \n", "3  {'$oid': '64dcd305e31598004c4c3791'}         DSC03852  1692193541212.jpg   \n", "4  {'$oid': '64dcd30ae31598004c4c3792'}         DSC03842  1692193545986.jpg   \n", "\n", "                                             taskIds  \n", "0  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "1  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "2  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "3  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "4  [653d8463c6072200454e977f, 6540a9478a297200456...  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["#JSON mongodb data\n", "dbscoutdatadf = pd.read_json(homedir+\"/_media/imagesdbdata.json\")\n", "dbscoutdatadf.head()"]}, {"cell_type": "code", "execution_count": 9, "id": "d1c092c3", "metadata": {}, "outputs": [{"data": {"text/plain": ["_id                 31226\n", "originalFilename    31226\n", "filename            31226\n", "taskIds             31226\n", "dtype: int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["#count of scoutdb images\n", "dbscoutdatadf.count()"]}, {"cell_type": "code", "execution_count": null, "id": "17117e96", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "ce40ecbc", "metadata": {}, "source": ["\n", "3. Compare dbscoutdatadf and imagelistdf to filter of the images still associated with active Tasks in Scout\n", "- Filter images that are not associated to tasks"]}, {"cell_type": "code", "execution_count": 10, "id": "6e1e4391", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["filename            31680\n", "deleteimg_img1      31680\n", "_id                 31226\n", "originalFilename    31226\n", "taskIds             31226\n", "deleteimg_img2      31226\n", "dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "      <th>deleteimg_img1</th>\n", "      <th>_id</th>\n", "      <th>originalFilename</th>\n", "      <th>taskIds</th>\n", "      <th>deleteimg_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1692193522043.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd2f2e31598004c4c378d'}</td>\n", "      <td>DSC03452</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1692193526742.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd2f6e31598004c4c378e'}</td>\n", "      <td>DSC03462</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1692193531544.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd2fbe31598004c4c378f'}</td>\n", "      <td>DSC03822</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1692193541212.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd305e31598004c4c3791'}</td>\n", "      <td>DSC03852</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1692193545986.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd30ae31598004c4c3792'}</td>\n", "      <td>DSC03842</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            filename deleteimg_img1                                   _id  \\\n", "0  1692193522043.jpg       imageid1  {'$oid': '64dcd2f2e31598004c4c378d'}   \n", "1  1692193526742.jpg       imageid1  {'$oid': '64dcd2f6e31598004c4c378e'}   \n", "2  1692193531544.jpg       imageid1  {'$oid': '64dcd2fbe31598004c4c378f'}   \n", "3  1692193541212.jpg       imageid1  {'$oid': '64dcd305e31598004c4c3791'}   \n", "4  1692193545986.jpg       imageid1  {'$oid': '64dcd30ae31598004c4c3792'}   \n", "\n", "  originalFilename                                            taskIds  \\\n", "0         DSC03452  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "1         DSC03462  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "2         DSC03822  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "3         DSC03852  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "4         DSC03842  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "\n", "  deleteimg_img2  \n", "0       imageid2  \n", "1       imageid2  \n", "2       imageid2  \n", "3       imageid2  \n", "4       imageid2  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["#compare dataframes to find files to delete\n", "# 31680 (scout hidden) - 31226 (in tasks) = 454\n", "\n", "imagelistdf['deleteimg'] ='imageid1'\n", "dbscoutdatadf['deleteimg'] = 'imageid2'\n", "\n", "merged_df = imagelistdf.merge(dbscoutdatadf, on='filename',how='left', suffixes=('_img1','_img2'))\n", "print(merged_df.count())\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "bfe6c018", "metadata": {}, "outputs": [{"data": {"text/plain": ["filename            454\n", "deleteimg_img1      454\n", "_id                   0\n", "originalFilename      0\n", "taskIds               0\n", "deleteimg_img2        0\n", "dtype: int64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["rows_not_in_tasks = merged_df[merged_df['deleteimg_img2'].isna()]\n", "\n", "\n", "rows_not_in_tasks.count()"]}, {"cell_type": "code", "execution_count": 12, "id": "9409ae67", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "      <th>deleteimg_img1</th>\n", "      <th>_id</th>\n", "      <th>originalFilename</th>\n", "      <th>taskIds</th>\n", "      <th>deleteimg_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>860</th>\n", "      <td>1696413597700.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>861</th>\n", "      <td>1696413600737.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>862</th>\n", "      <td>1696413603520.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>863</th>\n", "      <td>1696413606286.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>864</th>\n", "      <td>1696413609152.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              filename deleteimg_img1  _id originalFilename taskIds  \\\n", "860  1696413597700.jpg       imageid1  NaN              NaN     NaN   \n", "861  1696413600737.jpg       imageid1  NaN              NaN     NaN   \n", "862  1696413603520.jpg       imageid1  NaN              NaN     NaN   \n", "863  1696413606286.jpg       imageid1  NaN              NaN     NaN   \n", "864  1696413609152.jpg       imageid1  NaN              NaN     NaN   \n", "\n", "    deleteimg_img2  \n", "860            NaN  \n", "861            NaN  \n", "862            NaN  \n", "863            NaN  \n", "864            NaN  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["rows_not_in_tasks.head()"]}, {"cell_type": "code", "execution_count": 13, "id": "fbb3f104", "metadata": {}, "outputs": [], "source": ["#save the filenames to delete\n", "rows_not_in_tasks['filename'].to_csv(homedir+\"/_media/filestodelete.csv\",header=None,index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "6d2337aa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "8378f4e8", "metadata": {}, "source": ["### Deleting DAN and SH-B Blocks 17/11/23"]}, {"cell_type": "code", "execution_count": 3, "id": "324f36f2", "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '/home/<USER>/Dropbox/Conservation/MWSLab-2023/_media/filelistscout17nov.csv'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m#list of all images in scout-hidden\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m imagelistdf \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhomedir\u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m/_media/filelistscout17nov.csv\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m      3\u001b[0m imagelistdf\u001b[38;5;241m.\u001b[39mhead()\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/util/_decorators.py:211\u001b[0m, in \u001b[0;36mdeprecate_kwarg.<locals>._deprecate_kwarg.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    209\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    210\u001b[0m         kwargs[new_arg_name] \u001b[38;5;241m=\u001b[39m new_arg_value\n\u001b[0;32m--> 211\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/util/_decorators.py:331\u001b[0m, in \u001b[0;36mdeprecate_nonkeyword_arguments.<locals>.decorate.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    325\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(args) \u001b[38;5;241m>\u001b[39m num_allow_args:\n\u001b[1;32m    326\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[1;32m    327\u001b[0m         msg\u001b[38;5;241m.\u001b[39mformat(arguments\u001b[38;5;241m=\u001b[39m_format_argument_list(allow_args)),\n\u001b[1;32m    328\u001b[0m         \u001b[38;5;167;01mFutureWarning\u001b[39;00m,\n\u001b[1;32m    329\u001b[0m         stacklevel\u001b[38;5;241m=\u001b[39mfind_stack_level(),\n\u001b[1;32m    330\u001b[0m     )\n\u001b[0;32m--> 331\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/io/parsers/readers.py:950\u001b[0m, in \u001b[0;36mread_csv\u001b[0;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, squeeze, prefix, mangle_dupe_cols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, error_bad_lines, warn_bad_lines, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options)\u001b[0m\n\u001b[1;32m    935\u001b[0m kwds_defaults \u001b[38;5;241m=\u001b[39m _refine_defaults_read(\n\u001b[1;32m    936\u001b[0m     dialect,\n\u001b[1;32m    937\u001b[0m     delimiter,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    946\u001b[0m     defaults\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdelimiter\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m,\u001b[39m\u001b[38;5;124m\"\u001b[39m},\n\u001b[1;32m    947\u001b[0m )\n\u001b[1;32m    948\u001b[0m kwds\u001b[38;5;241m.\u001b[39mupdate(kwds_defaults)\n\u001b[0;32m--> 950\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/io/parsers/readers.py:605\u001b[0m, in \u001b[0;36m_read\u001b[0;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[1;32m    602\u001b[0m _validate_names(kwds\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnames\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[1;32m    604\u001b[0m \u001b[38;5;66;03m# Create the parser.\u001b[39;00m\n\u001b[0;32m--> 605\u001b[0m parser \u001b[38;5;241m=\u001b[39m \u001b[43mTextFileReader\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    607\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mor\u001b[39;00m iterator:\n\u001b[1;32m    608\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m parser\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/io/parsers/readers.py:1442\u001b[0m, in \u001b[0;36mTextFileReader.__init__\u001b[0;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[1;32m   1439\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m kwds[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m   1441\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles: IOHandles \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m-> 1442\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_engine\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mengine\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/io/parsers/readers.py:1735\u001b[0m, in \u001b[0;36mTextFileReader._make_engine\u001b[0;34m(self, f, engine)\u001b[0m\n\u001b[1;32m   1733\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode:\n\u001b[1;32m   1734\u001b[0m         mode \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m-> 1735\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;241m=\u001b[39m \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1736\u001b[0m \u001b[43m    \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1737\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1738\u001b[0m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1739\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcompression\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1740\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmemory_map\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmemory_map\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1741\u001b[0m \u001b[43m    \u001b[49m\u001b[43mis_text\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mis_text\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1742\u001b[0m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding_errors\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstrict\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1743\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstorage_options\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1744\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1745\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1746\u001b[0m f \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles\u001b[38;5;241m.\u001b[39mhandle\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/io/common.py:856\u001b[0m, in \u001b[0;36mget_handle\u001b[0;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[1;32m    851\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[1;32m    852\u001b[0m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[1;32m    853\u001b[0m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[1;32m    854\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mencoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mmode:\n\u001b[1;32m    855\u001b[0m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[0;32m--> 856\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m    857\u001b[0m \u001b[43m            \u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    858\u001b[0m \u001b[43m            \u001b[49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    859\u001b[0m \u001b[43m            \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    860\u001b[0m \u001b[43m            \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    861\u001b[0m \u001b[43m            \u001b[49m\u001b[43mnewline\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    862\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    863\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    864\u001b[0m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[1;32m    865\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(handle, ioargs\u001b[38;5;241m.\u001b[39mmode)\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '/home/<USER>/Dropbox/Conservation/MWSLab-2023/_media/filelistscout17nov.csv'"]}], "source": ["#list of all images in scout-hidden\n", "imagelistdf = pd.read_csv(homedir+\"/_media/filelistscout17nov.csv\")\n", "imagelistdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "fa2994e3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "df27be68", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "365d03d9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}