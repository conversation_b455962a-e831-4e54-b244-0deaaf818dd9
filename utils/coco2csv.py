import json
import csv
import zipfile
import os
from datetime import datetime

def extract_coco_to_multiple_csvs(zip_path, output_dir="coco_csvs"):
    """
    Extract COCO format data into separate CSV files
    
    Args:
        zip_path: Path to the zip file containing instances.json
        output_dir: Directory where CSV files will be saved
    """
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Extract and read the instances_default.json file
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        json_file = None
        for file_name in zip_ref.namelist():
            if file_name.endswith('instances_default.json'):
                json_file = file_name
                break
        
        if not json_file:
            raise FileNotFoundError("No instances_default.json found in the zip file")
        
        with zip_ref.open(json_file) as f:
            coco_data = json.load(f)
    
    # 1. Dataset Info CSV
    info_path = os.path.join(output_dir, "dataset_info.csv")
    if 'info' in coco_data and coco_data['info']:
        with open(info_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['key', 'value'])
            for key, value in coco_data['info'].items():
                writer.writerow([key, value])
        print(f"Extracted dataset info to {info_path}")
    
    # 2. Licenses CSV
    licenses_path = os.path.join(output_dir, "licenses.csv")
    if 'licenses' in coco_data and coco_data['licenses']:
        with open(licenses_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['id', 'name', 'url']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for license_info in coco_data['licenses']:
                writer.writerow({
                    'id': license_info.get('id'),
                    'name': license_info.get('name'),
                    'url': license_info.get('url')
                })
        print(f"Extracted {len(coco_data['licenses'])} licenses to {licenses_path}")
    
    # 3. Categories CSV
    categories_path = os.path.join(output_dir, "categories.csv")
    if 'categories' in coco_data and coco_data['categories']:
        with open(categories_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['id', 'name', 'supercategory']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for category in coco_data['categories']:
                writer.writerow({
                    'id': category.get('id'),
                    'name': category.get('name'),
                    'supercategory': category.get('supercategory')
                })
        print(f"Extracted {len(coco_data['categories'])} categories to {categories_path}")
    
    # 4. Images CSV
    images_path = os.path.join(output_dir, "images.csv")
    if 'images' in coco_data and coco_data['images']:
        with open(images_path, 'w', newline='', encoding='utf-8') as csvfile:
            # Get all possible fieldnames from all images
            all_fields = set()
            for img in coco_data['images']:
                all_fields.update(img.keys())
            
            fieldnames = sorted(list(all_fields))
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for image in coco_data['images']:
                writer.writerow(image)
        print(f"Extracted {len(coco_data['images'])} images to {images_path}")
    
    # 5. Annotations CSV (Main annotations table)
    annotations_path = os.path.join(output_dir, "annotations.csv")
    if 'annotations' in coco_data and coco_data['annotations']:
        category_map = {cat['id']: cat['name'] for cat in coco_data.get('categories', [])}
        image_map = {img['id']: img['file_name'] for img in coco_data.get('images', [])}
        
        # Find all possible attribute keys across all annotations
        all_attribute_keys = set()
        for annotation in coco_data['annotations']:
            if 'attributes' in annotation and annotation['attributes']:
                all_attribute_keys.update(annotation['attributes'].keys())
        
        # Build fieldnames including all attributes
        fieldnames = [
            'id', 'image_id', 'image_filename', 'category_id', 'category_name',
            'bbox_x', 'bbox_y', 'bbox_width', 'bbox_height',
            'area', 'iscrowd', 'has_segmentation'
        ]
        
        # Add all found attribute fields
        for attr_key in sorted(all_attribute_keys):
            fieldnames.append(f'attr_{attr_key}')
        
        with open(annotations_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for annotation in coco_data['annotations']:
                bbox = annotation.get('bbox', [0, 0, 0, 0])
                attributes = annotation.get('attributes', {})
                
                row = {
                    'id': annotation.get('id'),
                    'image_id': annotation.get('image_id'),
                    'image_filename': image_map.get(annotation.get('image_id'), ''),
                    'category_id': annotation.get('category_id'),
                    'category_name': category_map.get(annotation.get('category_id'), ''),
                    'bbox_x': bbox[0] if len(bbox) > 0 else 0,
                    'bbox_y': bbox[1] if len(bbox) > 1 else 0,
                    'bbox_width': bbox[2] if len(bbox) > 2 else 0,
                    'bbox_height': bbox[3] if len(bbox) > 3 else 0,
                    'area': annotation.get('area'),
                    'iscrowd': annotation.get('iscrowd', 0),
                    'has_segmentation': 1 if 'segmentation' in annotation else 0
                }
                
                # Add all attribute values
                for attr_key in all_attribute_keys:
                    row[f'attr_{attr_key}'] = attributes.get(attr_key, '')
                
                writer.writerow(row)
        print(f"Extracted {len(coco_data['annotations'])} annotations to {annotations_path}")
        if all_attribute_keys:
            print(f"Found attributes: {', '.join(sorted(all_attribute_keys))}")
    
    # 6. Segmentation CSV (if segmentation data exists)
    segmentation_path = os.path.join(output_dir, "segmentations.csv")
    segmentation_rows = []
    
    if 'annotations' in coco_data:
        for annotation in coco_data['annotations']:
            if 'segmentation' in annotation and annotation['segmentation']:
                seg_data = annotation['segmentation']
                if isinstance(seg_data, list) and len(seg_data) > 0:
                    # Handle polygon segmentation
                    for i, polygon in enumerate(seg_data):
                        if isinstance(polygon, list):
                            segmentation_rows.append({
                                'annotation_id': annotation.get('id'),
                                'image_id': annotation.get('image_id'),
                                'polygon_index': i,
                                'coordinates': ','.join(map(str, polygon)),
                                'num_points': len(polygon) // 2
                            })
    
    if segmentation_rows:
        with open(segmentation_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['annotation_id', 'image_id', 'polygon_index', 'coordinates', 'num_points']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(segmentation_rows)
        print(f"Extracted {len(segmentation_rows)} segmentation polygons to {segmentation_path}")
    
    # 7. Summary Statistics CSV
    summary_path = os.path.join(output_dir, "dataset_summary.csv")
    with open(summary_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['metric', 'count'])
        writer.writerow(['total_images', len(coco_data.get('images', []))])
        writer.writerow(['total_annotations', len(coco_data.get('annotations', []))])
        writer.writerow(['total_categories', len(coco_data.get('categories', []))])
        writer.writerow(['total_licenses', len(coco_data.get('licenses', []))])
        writer.writerow(['annotations_with_segmentation', len(segmentation_rows)])
        
        # Category distribution
        category_counts = {}
        category_map = {cat['id']: cat['name'] for cat in coco_data.get('categories', [])}
        for annotation in coco_data.get('annotations', []):
            cat_id = annotation.get('category_id')
            cat_name = category_map.get(cat_id, f'category_{cat_id}')
            category_counts[cat_name] = category_counts.get(cat_name, 0) + 1
        
        for cat_name, count in category_counts.items():
            writer.writerow([f'annotations_for_{cat_name}', count])
    
    print(f"Extracted summary statistics to {summary_path}")
    print(f"\nAll CSV files saved to: {output_dir}")

# Usage example:
if __name__ == "__main__":
    zip_file_path = "/Users/<USER>/Downloads/Usangu-Review_2024.zip"  # Replace with your zip file path
    extract_coco_to_multiple_csvs(zip_file_path, "coco_output")