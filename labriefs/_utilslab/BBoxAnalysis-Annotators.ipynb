{"cells": [{"cell_type": "markdown", "id": "52a621fb", "metadata": {}, "source": ["### DAN transects 01 to 25 (L &R)\n", "#### Session 20220906A\n", "#### Bounding box analysis for each annotator"]}, {"cell_type": "code", "execution_count": 1, "id": "24d16aba", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "59588650", "metadata": {}, "outputs": [], "source": ["datadir =\"/home/<USER>/Dropbox/Conservation/MWSLab-2023/scoutexports\""]}, {"cell_type": "code", "execution_count": 3, "id": "a0022119", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DAN05-L_174</th>\n", "      <th>6530de5f05667e0045cadc4e</th>\n", "      <th>KES22_IIM-L_20220906A-062714_M0309369.jpg</th>\n", "      <th>6530d51b0e8be9004ca31586</th>\n", "      <th>6351.04301708898</th>\n", "      <th>1166.6234531526222</th>\n", "      <th>60.22392457277549</th>\n", "      <th>46.458456098998234</th>\n", "      <th>vervet monkey</th>\n", "      <th>Unnamed: 9</th>\n", "      <th>annotator1</th>\n", "      <th>1697720924067</th>\n", "      <th>false</th>\n", "      <th>Unnamed: 13</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6482.702368</td>\n", "      <td>1211.366282</td>\n", "      <td>41.640716</td>\n", "      <td>35.962436</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6102.257645</td>\n", "      <td>1084.551374</td>\n", "      <td>43.533476</td>\n", "      <td>28.391397</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6230.965313</td>\n", "      <td>1126.192090</td>\n", "      <td>39.747956</td>\n", "      <td>26.498637</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6405.099216</td>\n", "      <td>1175.403845</td>\n", "      <td>-39.747956</td>\n", "      <td>30.284157</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_201_2</td>\n", "      <td>6530df2905667e0045cadc59</td>\n", "      <td>KES22_IIM-L_20220906A-062738_M0309381.jpg</td>\n", "      <td>6530d4680e8be9004ca31544</td>\n", "      <td>6616.536906</td>\n", "      <td>42.693538</td>\n", "      <td>31.230503</td>\n", "      <td>14.574235</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1697703725045</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     DAN05-L_174  6530de5f05667e0045cadc4e  \\\n", "0  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "1  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "2  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "3  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "4  DAN05-L_201_2  6530df2905667e0045cadc59   \n", "\n", "   KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586  \\\n", "0  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "1  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "2  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "3  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "4  KES22_IIM-L_20220906A-062738_M0309381.jpg  6530d4680e8be9004ca31544   \n", "\n", "   6351.04301708898  1166.6234531526222  60.22392457277549  \\\n", "0       6482.702368         1211.366282          41.640716   \n", "1       6102.257645         1084.551374          43.533476   \n", "2       6230.965313         1126.192090          39.747956   \n", "3       6405.099216         1175.403845         -39.747956   \n", "4       6616.536906           42.693538          31.230503   \n", "\n", "   46.458456098998234  vervet monkey  Unnamed: 9 annotator1  1697720924067  \\\n", "0           35.962436  vervet monkey         NaN    wmagesa  1697717018183   \n", "1           28.391397  vervet monkey         NaN    wmagesa  1697717018183   \n", "2           26.498637  vervet monkey         NaN    wmagesa  1697717018183   \n", "3           30.284157  vervet monkey         NaN    wmagesa  1697717018183   \n", "4           14.574235           bird         <PERSON><PERSON>  1697703725045   \n", "\n", "   false  Unnamed: 13  \n", "0  False          NaN  \n", "1  False          NaN  \n", "2  False          NaN  \n", "3  False          NaN  \n", "4  False          NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["dandf = pd.read_csv(datadir+\"/DANscout-export-annotations.csv\")\n", "dandf.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "ceb82f1b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1371, 14)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["dandf.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "53b9717b", "metadata": {}, "outputs": [], "source": ["dandf.columns=['TaskName', 'TaskID', 'ImageFilename', 'ImageID', 'BoxX', 'BoxY',\n", "       'BoxW', 'BoxH', 'Label', 'LabelConfidence', 'Assignee', 'Timestamp',\n", "       'IsGroundTruth', 'ExcludedByLine']"]}, {"cell_type": "code", "execution_count": 6, "id": "be1eea4c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6482.702368</td>\n", "      <td>1211.366282</td>\n", "      <td>41.640716</td>\n", "      <td>35.962436</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6102.257645</td>\n", "      <td>1084.551374</td>\n", "      <td>43.533476</td>\n", "      <td>28.391397</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6230.965313</td>\n", "      <td>1126.192090</td>\n", "      <td>39.747956</td>\n", "      <td>26.498637</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6405.099216</td>\n", "      <td>1175.403845</td>\n", "      <td>-39.747956</td>\n", "      <td>30.284157</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_201_2</td>\n", "      <td>6530df2905667e0045cadc59</td>\n", "      <td>KES22_IIM-L_20220906A-062738_M0309381.jpg</td>\n", "      <td>6530d4680e8be9004ca31544</td>\n", "      <td>6616.536906</td>\n", "      <td>42.693538</td>\n", "      <td>31.230503</td>\n", "      <td>14.574235</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1697703725045</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        TaskName                    TaskID  \\\n", "0  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "1  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "2  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "3  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "4  DAN05-L_201_2  6530df2905667e0045cadc59   \n", "\n", "                               ImageFilename                   ImageID  \\\n", "0  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "1  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "2  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "3  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "4  KES22_IIM-L_20220906A-062738_M0309381.jpg  6530d4680e8be9004ca31544   \n", "\n", "          BoxX         BoxY       BoxW       BoxH          Label  \\\n", "0  6482.702368  1211.366282  41.640716  35.962436  vervet monkey   \n", "1  6102.257645  1084.551374  43.533476  28.391397  vervet monkey   \n", "2  6230.965313  1126.192090  39.747956  26.498637  vervet monkey   \n", "3  6405.099216  1175.403845 -39.747956  30.284157  vervet monkey   \n", "4  6616.536906    42.693538  31.230503  14.574235           bird   \n", "\n", "   LabelConfidence Assignee      Timestamp  IsGroundTruth  ExcludedByLine  \n", "0              NaN  wmagesa  1697717018183          False             NaN  \n", "1              NaN  wmagesa  1697717018183          False             NaN  \n", "2              NaN  wmagesa  1697717018183          False             NaN  \n", "3              NaN  wmagesa  1697717018183          False             NaN  \n", "4              NaN   zrahim  1697703725045          False             NaN  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["dandf.head()"]}, {"cell_type": "code", "execution_count": 7, "id": "d589a0fe", "metadata": {}, "outputs": [{"data": {"text/plain": ["elephant            1133\n", "white_bones          104\n", "giraffe               26\n", "zebra                 19\n", "bird                  18\n", "unknown mammal        15\n", "impala                10\n", "gazelle_grants         8\n", "vervet monkey          7\n", "elephant bull          7\n", "kudu                   6\n", "unknown antelope       5\n", "buffalo                5\n", "gazelle_thomsons       4\n", "roof_mabati            2\n", "duiker                 2\n", "Name: Label, dtype: int64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["#Bounding boxes count for all DAN tasks\n", "dandf['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 8, "id": "ce52548e", "metadata": {}, "outputs": [], "source": ["# Group the bbox by 'TaskName' \n", "result = dandf.groupby('TaskName')['Label'].value_counts().reset_index(name='Count')\n"]}, {"cell_type": "code", "execution_count": 9, "id": "5b4e470b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>Label</th>\n", "      <th>Count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN01-L</td>\n", "      <td>elephant</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN01-L</td>\n", "      <td>unknown antelope</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN01-R</td>\n", "      <td>elephant</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN02-R</td>\n", "      <td>unknown mammal</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN03-L_060_120</td>\n", "      <td>elephant</td>\n", "      <td>64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>138</th>\n", "      <td>DAN20-L_2</td>\n", "      <td>elephant</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>139</th>\n", "      <td>DAN20-R_09_121</td>\n", "      <td>elephant</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>140</th>\n", "      <td>DAN20-R_09_71</td>\n", "      <td>elephant</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>141</th>\n", "      <td>DAN21-L_091_2</td>\n", "      <td>white_bones</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>142</th>\n", "      <td>DAN25-R_09_441</td>\n", "      <td>giraffe</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>143 rows × 3 columns</p>\n", "</div>"], "text/plain": ["            TaskName             Label  Count\n", "0            DAN01-L          elephant      5\n", "1            DAN01-L  unknown antelope      1\n", "2            DAN01-R          elephant      7\n", "3            DAN02-R    unknown mammal      1\n", "4    DAN03-L_060_120          elephant     64\n", "..               ...               ...    ...\n", "138        DAN20-L_2          elephant      7\n", "139   DAN20-R_09_121          elephant      4\n", "140    DAN20-R_09_71          elephant      6\n", "141    DAN21-L_091_2       white_bones      6\n", "142   DAN25-R_09_441           giraffe      2\n", "\n", "[143 rows x 3 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": 12, "id": "9433d4a7", "metadata": {"scrolled": true}, "outputs": [], "source": ["bboxbytask = result.groupby('TaskName')['Count'].sum().reset_index(name='BBoxes')"]}, {"cell_type": "code", "execution_count": 13, "id": "aff51fb5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>BBoxes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN01-L</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN01-R</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN02-R</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN03-L_060_120</td>\n", "      <td>65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN03-L_061_72</td>\n", "      <td>28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>DAN20-L_2</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89</th>\n", "      <td>DAN20-R_09_121</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>DAN20-R_09_71</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>DAN21-L_091_2</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>DAN25-R_09_441</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>93 rows × 2 columns</p>\n", "</div>"], "text/plain": ["           TaskName  BBoxes\n", "0           DAN01-L       6\n", "1           DAN01-R       7\n", "2           DAN02-R       1\n", "3   DAN03-L_060_120      65\n", "4    DAN03-L_061_72      28\n", "..              ...     ...\n", "88        DAN20-L_2       7\n", "89   DAN20-R_09_121       4\n", "90    DAN20-R_09_71       6\n", "91    DAN21-L_091_2       6\n", "92   DAN25-R_09_441       2\n", "\n", "[93 rows x 2 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["bboxbytask"]}, {"cell_type": "code", "execution_count": 14, "id": "dc03c6fc", "metadata": {}, "outputs": [], "source": ["bboxbytask.to_csv(\"/home/<USER>/Dropbox/Conservation/MWSLab-2023/labriefs/_media/DANBBox.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "da815c14", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "d79abd3a", "metadata": {}, "source": ["SH"]}, {"cell_type": "code", "execution_count": null, "id": "77312620", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "80720af4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "95e81fe0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "edc5a608", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "61d5cd32", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "037ae925", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "51dc271c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}