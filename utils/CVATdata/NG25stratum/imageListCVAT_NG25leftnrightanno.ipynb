{"cells": [{"cell_type": "markdown", "id": "ff9ed367", "metadata": {}, "source": ["Divide stratum to left and right"]}, {"cell_type": "code", "execution_count": 1, "id": "8daf5dd7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "b489dc6c-8845-4df1-9680-2fd060786fc4", "metadata": {}, "outputs": [], "source": ["coco_file_path = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023/utils/CVATdata/NG25stratum/annotationsNG25.json\""]}, {"cell_type": "code", "execution_count": 3, "id": "23b685d4", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "images_df = pd.DataFrame(coco_data['images'])\n"]}, {"cell_type": "code", "execution_count": 4, "id": "4a1f8fd3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(896, 6)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "8e9230e2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145132_M0804846.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145128_M0804844.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145428_M0804934.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   1   7008    4672  KES22_WOT-L_20220923B-145132_M0804846.jpg        1   \n", "1   2   7008    4672  KES22_WOT-L_20220923B-145454_M0804947.jpg        1   \n", "2   3   7008    4672  KES22_WOT-L_20220923B-145256_M0804888.jpg        1   \n", "3   4   7008    4672  KES22_WOT-L_20220923B-145128_M0804844.jpg        1   \n", "4   5   7008    4672  KES22_WOT-L_20220923B-145428_M0804934.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "60719a18", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "id": "4e4444a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(445, 6)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "ng25leftposve_df = pd.read_csv(\"NG25leftimagespos.csv\")\n", "\n", "# Print the resulting filtered DataFrame\n", "ng25leftposve_df.shape"]}, {"cell_type": "code", "execution_count": 7, "id": "060a581a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>814</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140119_M0803345.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>818</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140128_M0803349.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>816</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140130_M0803350.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>822</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140132_M0803351.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>817</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140136_M0803353.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0  814   7008    4672  KES22_WOT-L_20220923B-140119_M0803345.jpg        1   \n", "1  818   7008    4672  KES22_WOT-L_20220923B-140128_M0803349.jpg        1   \n", "2  816   7008    4672  KES22_WOT-L_20220923B-140130_M0803350.jpg        1   \n", "3  822   7008    4672  KES22_WOT-L_20220923B-140132_M0803351.jpg        1   \n", "4  817   7008    4672  KES22_WOT-L_20220923B-140136_M0803353.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25leftposve_df.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "164fc814", "metadata": {}, "outputs": [{"data": {"text/plain": ["(235, 1)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25left1img = pd.read_csv(\"NG25left1cvat.csv\")\n", "NG25left1img.shape"]}, {"cell_type": "code", "execution_count": 9, "id": "932d181b-abf7-45cb-b26d-c73c7d9163f0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_WOT-L_20220923B-141346_M0803717.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_WOT-L_20220923B-141348_M0803718.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_WOT-L_20220923B-141350_M0803719.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_WOT-L_20220923B-141420_M0803734.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_WOT-L_20220923B-141422_M0803735.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                c<PERSON><PERSON><PERSON><PERSON>\n", "0  KES22_WOT-L_20220923B-141346_M0803717.jpg\n", "1  KES22_WOT-L_20220923B-141348_M0803718.jpg\n", "2  KES22_WOT-L_20220923B-141350_M0803719.jpg\n", "3  KES22_WOT-L_20220923B-141420_M0803734.jpg\n", "4  KES22_WOT-L_20220923B-141422_M0803735.jpg"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25left1img.head()"]}, {"cell_type": "code", "execution_count": 15, "id": "7bc344e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(135, 6)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>320</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-141348_M0803718.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>324</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-141422_M0803735.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>327</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-141426_M0803737.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>322</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-141428_M0803738.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89</th>\n", "      <td>321</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-141430_M0803739.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     id  width  height                                  file_name  license  \\\n", "85  320   7008    4672  KES22_WOT-L_20220923B-141348_M0803718.jpg        1   \n", "86  324   7008    4672  KES22_WOT-L_20220923B-141422_M0803735.jpg        1   \n", "87  327   7008    4672  KES22_WOT-L_20220923B-141426_M0803737.jpg        1   \n", "88  322   7008    4672  KES22_WOT-L_20220923B-141428_M0803738.jpg        1   \n", "89  321   7008    4672  KES22_WOT-L_20220923B-141430_M0803739.jpg        1   \n", "\n", "   date_captured  \n", "85    2022-09-23  \n", "86    2022-09-23  \n", "87    2022-09-23  \n", "88    2022-09-23  \n", "89    2022-09-23  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25left1posvimg = ng25leftposve_df[ng25leftposve_df['file_name'].isin(NG25left1img['cvatfilename'])]\n", "\n", "print(ng25left1posvimg.shape)\n", "ng25left1posvimg.head()"]}, {"cell_type": "markdown", "id": "cbd7e416-7abf-4909-86c0-f9a7d7995982", "metadata": {}, "source": ["### Annotations"]}, {"cell_type": "code", "execution_count": 23, "id": "dd3775f6-a803-46b1-b0f4-3cb6d6ffa002", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7142, 7)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf = pd.DataFrame(coco_data['annotations'])\n", "annotationsdf.shape"]}, {"cell_type": "code", "execution_count": 24, "id": "6c3c7bf7-6c96-4c9e-873a-a57d5c1277b8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>[]</td>\n", "      <td>1384.146154</td>\n", "      <td>[6360.960244683219, 625.1656233772903, 54.6520...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>2535.911877</td>\n", "      <td>[193.9164645864987, 397.2349395771913, 39.9585...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>1178.689536</td>\n", "      <td>[26.1632, 457.3888, -25.2288, 46.72]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>7721.217171</td>\n", "      <td>[594.6750637808044, 2266.2291017559783, 135.74...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>15387.282791</td>\n", "      <td>[771.7848110372613, 2336.038783156333, 127.984...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1            7           []   1384.146154   \n", "1   2         2           23           []   2535.911877   \n", "2   3         2           23           []   1178.689536   \n", "3   4         3           15           []   7721.217171   \n", "4   5         3           15           []  15387.282791   \n", "\n", "                                                bbox  iscrowd  \n", "0  [6360.960244683219, 625.1656233772903, 54.6520...        0  \n", "1  [193.9164645864987, 397.2349395771913, 39.9585...        0  \n", "2               [26.1632, 457.3888, -25.2288, 46.72]        0  \n", "3  [594.6750637808044, 2266.2291017559783, 135.74...        0  \n", "4  [771.7848110372613, 2336.038783156333, 127.984...        0  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 25, "id": "75c1d751-58aa-4d17-971d-63cfc6562f9c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4415, 7)"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["#change bbox dimensions to positive\n", "greater_than_zero_mask = [all(item > 0 for item in row) for row in annotationsdf['bbox']]\n", "annotationsdf[greater_than_zero_mask].shape"]}, {"cell_type": "code", "execution_count": 26, "id": "8f5bc11b-06b0-41e3-b0a8-052639347d0a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2727, 7)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["filterpositive = annotationsdf[greater_than_zero_mask]['bbox'].tolist()\n", "less_than_zero_mask = annotationsdf[~annotationsdf['bbox'].isin(filterpositive)]\n", "less_than_zero_mask.shape"]}, {"cell_type": "code", "execution_count": 27, "id": "44bd0fbd-a606-41e3-a682-c2f5a7b31433", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>1178.689536</td>\n", "      <td>[26.1632, 457.3888, -25.2288, 46.72]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>18</td>\n", "      <td>8</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>763.433550</td>\n", "      <td>[3564.943483152086, 500.5624899555365, -20.335...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>65</td>\n", "      <td>35</td>\n", "      <td>13</td>\n", "      <td>[]</td>\n", "      <td>8171.675274</td>\n", "      <td>[1961.5792575132589, 0.0, 68.82734236888626, 1...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>74</td>\n", "      <td>37</td>\n", "      <td>1</td>\n", "      <td>[]</td>\n", "      <td>2275.010721</td>\n", "      <td>[4314.031718234143, 2406.5859765459336, -35.55...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>75</td>\n", "      <td>38</td>\n", "      <td>1</td>\n", "      <td>[]</td>\n", "      <td>1905.235406</td>\n", "      <td>[681.2907795923563, 3553.829892029198, -49.125...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  image_id  category_id segmentation         area  \\\n", "2    3         2           23           []  1178.689536   \n", "17  18         8           23           []   763.433550   \n", "64  65        35           13           []  8171.675274   \n", "73  74        37            1           []  2275.010721   \n", "74  75        38            1           []  1905.235406   \n", "\n", "                                                 bbox  iscrowd  \n", "2                [26.1632, 457.3888, -25.2288, 46.72]        0  \n", "17  [3564.943483152086, 500.5624899555365, -20.335...        0  \n", "64  [1961.5792575132589, 0.0, 68.82734236888626, 1...        0  \n", "73  [4314.031718234143, 2406.5859765459336, -35.55...        0  \n", "74  [681.2907795923563, 3553.829892029198, -49.125...        0  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["less_than_zero_mask.head()"]}, {"cell_type": "code", "execution_count": 28, "id": "cbad6219-0af1-4962-997f-34803fce4049", "metadata": {}, "outputs": [], "source": ["for item in less_than_zero_mask['bbox']:\n", "    \n", "    if((item[-2]<0) and (item[-1]<0)):\n", "        item[-2] = abs(item[-2])\n", "        item[0]  = item[0] + int(abs(item[-2]))\n", "        item[-1] = abs(item[-1])\n", "        item[1] = item[1] + int(abs(item[-1]))\n", "    \n", "    elif((item[0]<0) and (item[1]<0)):\n", "        item[0] = abs(item[0])\n", "        item[-2] = item[-2] + int(abs(item[0]))\n", "        item[1] = abs(item[1])\n", "        item[-1] = item[-1] + int(abs(item[1]))\n", "        \n", "    elif(item[-2]<0):\n", "        item[-2] = abs(item[-2])\n", "        item[0]  = item[0] + int(abs(item[-2]))\n", "    \n", "    elif(item[0]<0):\n", "        item[0] = abs(item[0])\n", "        item[-2] = item[-2] + int(abs(item[0]))\n", "           \n", "    elif(item[1]<0):\n", "        item[1] = abs(item[1])\n", "        item[-1] = item[-1] + int(abs(item[1]))\n", "            \n", "    elif(item[-1]<0):\n", "        item[-1] = abs(item[-1])\n", "        item[1] = item[1] + int(abs(item[-1]))"]}, {"cell_type": "code", "execution_count": 29, "id": "61f2485f-e424-4ab0-9f53-2d1febebcc5b", "metadata": {}, "outputs": [], "source": ["annotationsdf.update(less_than_zero_mask)"]}, {"cell_type": "code", "execution_count": 30, "id": "ecdb675a-3d15-4c20-8809-c7e02ec11ac0", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7109, 7)"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["#check if all are positives after\n", "greater_than_zero_mask = [all(item > 0 for item in row) for row in annotationsdf['bbox']]\n", "annotationsdf[greater_than_zero_mask].shape"]}, {"cell_type": "code", "execution_count": 31, "id": "1d4bf6dd-e081-4b44-8979-5e6c87e68fbe", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7142, 7)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.shape"]}, {"cell_type": "code", "execution_count": 32, "id": "151cebe6-7e03-4eef-9ce2-18f662dd8dfb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>7.0</td>\n", "      <td>[]</td>\n", "      <td>1384.146154</td>\n", "      <td>[6360.960244683219, 625.1656233772903, 54.6520...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>23.0</td>\n", "      <td>[]</td>\n", "      <td>2535.911877</td>\n", "      <td>[193.9164645864987, 397.2349395771913, 39.9585...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>23.0</td>\n", "      <td>[]</td>\n", "      <td>1178.689536</td>\n", "      <td>[51.1632, 457.3888, 25.2288, 46.72]</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.0</td>\n", "      <td>3.0</td>\n", "      <td>15.0</td>\n", "      <td>[]</td>\n", "      <td>7721.217171</td>\n", "      <td>[594.6750637808044, 2266.2291017559783, 135.74...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.0</td>\n", "      <td>3.0</td>\n", "      <td>15.0</td>\n", "      <td>[]</td>\n", "      <td>15387.282791</td>\n", "      <td>[771.7848110372613, 2336.038783156333, 127.984...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  image_id  category_id segmentation          area  \\\n", "0  1.0       1.0          7.0           []   1384.146154   \n", "1  2.0       2.0         23.0           []   2535.911877   \n", "2  3.0       2.0         23.0           []   1178.689536   \n", "3  4.0       3.0         15.0           []   7721.217171   \n", "4  5.0       3.0         15.0           []  15387.282791   \n", "\n", "                                                bbox  iscrowd  \n", "0  [6360.960244683219, 625.1656233772903, 54.6520...      0.0  \n", "1  [193.9164645864987, 397.2349395771913, 39.9585...      0.0  \n", "2                [51.1632, 457.3888, 25.2288, 46.72]      0.0  \n", "3  [594.6750637808044, 2266.2291017559783, 135.74...      0.0  \n", "4  [771.7848110372613, 2336.038783156333, 127.984...      0.0  "]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 33, "id": "6ac12ce8-743f-4844-93d4-13cbb5006961", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"id\"]=annotationsdf[\"id\"].astype(int)"]}, {"cell_type": "code", "execution_count": 34, "id": "da98f6a5-03bd-4630-800d-76e1f085925e", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"image_id\"] =annotationsdf[\"image_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": 35, "id": "1560c7d8-fcd8-400c-aa13-b5d9f981ef5f", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"iscrowd\"] = annotationsdf[\"iscrowd\"].astype(int)"]}, {"cell_type": "code", "execution_count": 36, "id": "4c006a27-977a-47f4-8677-2cff0d696946", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"category_id\"] = annotationsdf[\"category_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": 37, "id": "5e46ada1-1f2c-432b-9362-bef0f3e3ba33", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>[]</td>\n", "      <td>1384.146154</td>\n", "      <td>[6360.960244683219, 625.1656233772903, 54.6520...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>2535.911877</td>\n", "      <td>[193.9164645864987, 397.2349395771913, 39.9585...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>1178.689536</td>\n", "      <td>[51.1632, 457.3888, 25.2288, 46.72]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>7721.217171</td>\n", "      <td>[594.6750637808044, 2266.2291017559783, 135.74...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>15387.282791</td>\n", "      <td>[771.7848110372613, 2336.038783156333, 127.984...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1            7           []   1384.146154   \n", "1   2         2           23           []   2535.911877   \n", "2   3         2           23           []   1178.689536   \n", "3   4         3           15           []   7721.217171   \n", "4   5         3           15           []  15387.282791   \n", "\n", "                                                bbox  iscrowd  \n", "0  [6360.960244683219, 625.1656233772903, 54.6520...        0  \n", "1  [193.9164645864987, 397.2349395771913, 39.9585...        0  \n", "2                [51.1632, 457.3888, 25.2288, 46.72]        0  \n", "3  [594.6750637808044, 2266.2291017559783, 135.74...        0  \n", "4  [771.7848110372613, 2336.038783156333, 127.984...        0  "]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ce65f209-a8d6-46c0-a3cd-78f9717e3c3e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 38, "id": "60a7e96f-49e0-4569-8a9c-8f862a081c7f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(864, 7)"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return NG25 left1 annotations \n", "\n", "ng25left1_annot = annotationsdf[annotationsdf['image_id'].isin(ng25left1posvimg['id'])]\n", "ng25left1_annot.shape"]}, {"cell_type": "code", "execution_count": 39, "id": "3e44270a-c9d2-4535-a70a-17113c34d8b5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2848</th>\n", "      <td>2849</td>\n", "      <td>320</td>\n", "      <td>35</td>\n", "      <td>[]</td>\n", "      <td>3859.295063</td>\n", "      <td>[2155.1332094371123, 4277.160298610893, 66.223...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2849</th>\n", "      <td>2850</td>\n", "      <td>320</td>\n", "      <td>35</td>\n", "      <td>[]</td>\n", "      <td>1130.500574</td>\n", "      <td>[2131.516300752827, 4356.628594827858, 44.1490...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2850</th>\n", "      <td>2851</td>\n", "      <td>320</td>\n", "      <td>35</td>\n", "      <td>[]</td>\n", "      <td>1903.918898</td>\n", "      <td>[2293.9848174630674, 4591.501559202445, 65.340...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2851</th>\n", "      <td>2852</td>\n", "      <td>320</td>\n", "      <td>35</td>\n", "      <td>[]</td>\n", "      <td>1397.142778</td>\n", "      <td>[2141.2290925126786, 4564.1291460610455, 24.72...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2852</th>\n", "      <td>2853</td>\n", "      <td>320</td>\n", "      <td>28</td>\n", "      <td>[]</td>\n", "      <td>452.200230</td>\n", "      <td>[2234.825085834882, 4634.767631587237, 25.6064...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        id  image_id  category_id segmentation         area  \\\n", "2848  2849       320           35           []  3859.295063   \n", "2849  2850       320           35           []  1130.500574   \n", "2850  2851       320           35           []  1903.918898   \n", "2851  2852       320           35           []  1397.142778   \n", "2852  2853       320           28           []   452.200230   \n", "\n", "                                                   bbox  iscrowd  \n", "2848  [2155.1332094371123, 4277.160298610893, 66.223...        0  \n", "2849  [2131.516300752827, 4356.628594827858, 44.1490...        0  \n", "2850  [2293.9848174630674, 4591.501559202445, 65.340...        0  \n", "2851  [2141.2290925126786, 4564.1291460610455, 24.72...        0  \n", "2852  [2234.825085834882, 4634.767631587237, 25.6064...        0  "]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25left1_annot.head()"]}, {"cell_type": "code", "execution_count": null, "id": "449f5a3e-6aa8-4c2d-aa7b-c64739e55960", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "3074bfdb-2c61-423c-a05e-1be76ef23708", "metadata": {}, "source": ["### NG25 Left2"]}, {"cell_type": "code", "execution_count": 41, "id": "18c4b4b1-9afa-4a84-8164-68be3fbd98ba", "metadata": {}, "outputs": [{"data": {"text/plain": ["(207, 1)"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25left2img = pd.read_csv(\"NG25left2cvat.csv\")\n", "NG25left2img.shape"]}, {"cell_type": "code", "execution_count": 17, "id": "106efe92-98f0-41bd-8b3c-7da5617893dc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(109, 6)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>760</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-142815_M0804150.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>221</th>\n", "      <td>759</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-142833_M0804159.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>222</th>\n", "      <td>752</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-142843_M0804164.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>223</th>\n", "      <td>761</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-142851_M0804168.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>224</th>\n", "      <td>765</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-142903_M0804174.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "220  760   7008    4672  KES22_WOT-L_20220923B-142815_M0804150.jpg        1   \n", "221  759   7008    4672  KES22_WOT-L_20220923B-142833_M0804159.jpg        1   \n", "222  752   7008    4672  KES22_WOT-L_20220923B-142843_M0804164.jpg        1   \n", "223  761   7008    4672  KES22_WOT-L_20220923B-142851_M0804168.jpg        1   \n", "224  765   7008    4672  KES22_WOT-L_20220923B-142903_M0804174.jpg        1   \n", "\n", "    date_captured  \n", "220    2022-09-23  \n", "221    2022-09-23  \n", "222    2022-09-23  \n", "223    2022-09-23  \n", "224    2022-09-23  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25left2posvimg = ng25leftposve_df[ng25leftposve_df['file_name'].isin(NG25left2img['cvatfilename'])]\n", "print(ng25left2posvimg.shape)\n", "ng25left2posvimg.head()"]}, {"cell_type": "code", "execution_count": 42, "id": "f8b537d0-459b-48f9-bec6-d5111c066b18", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1048, 7)"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return NG25 left1 annotations \n", "\n", "ng25left2_annot = annotationsdf[annotationsdf['image_id'].isin(ng25left2posvimg['id'])]\n", "ng25left2_annot.shape"]}, {"cell_type": "code", "execution_count": 43, "id": "281118d4-d61d-4a56-98a2-45a0b49ce8f2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>248</th>\n", "      <td>249</td>\n", "      <td>93</td>\n", "      <td>17</td>\n", "      <td>[]</td>\n", "      <td>2750.903442</td>\n", "      <td>[4852.514170719437, 2856.828672128262, 34.9660...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>249</th>\n", "      <td>250</td>\n", "      <td>93</td>\n", "      <td>17</td>\n", "      <td>[]</td>\n", "      <td>2349.022486</td>\n", "      <td>[4758.156520304966, 2884.0819211584385, 29.138...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>250</th>\n", "      <td>251</td>\n", "      <td>93</td>\n", "      <td>17</td>\n", "      <td>[]</td>\n", "      <td>2445.247504</td>\n", "      <td>[4702.937234156084, 2786.638107170527, 34.9660...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>251</th>\n", "      <td>252</td>\n", "      <td>93</td>\n", "      <td>17</td>\n", "      <td>[]</td>\n", "      <td>3567.872708</td>\n", "      <td>[4451.370780973025, 2716.447542212792, 59.2480...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>252</th>\n", "      <td>253</td>\n", "      <td>93</td>\n", "      <td>17</td>\n", "      <td>[]</td>\n", "      <td>3160.331457</td>\n", "      <td>[4513.532624739613, 2770.0114826554386, 48.563...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  image_id  category_id segmentation         area  \\\n", "248  249        93           17           []  2750.903442   \n", "249  250        93           17           []  2349.022486   \n", "250  251        93           17           []  2445.247504   \n", "251  252        93           17           []  3567.872708   \n", "252  253        93           17           []  3160.331457   \n", "\n", "                                                  bbox  iscrowd  \n", "248  [4852.514170719437, 2856.828672128262, 34.9660...        0  \n", "249  [4758.156520304966, 2884.0819211584385, 29.138...        0  \n", "250  [4702.937234156084, 2786.638107170527, 34.9660...        0  \n", "251  [4451.370780973025, 2716.447542212792, 59.2480...        0  \n", "252  [4513.532624739613, 2770.0114826554386, 48.563...        0  "]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25left2_annot.head()"]}, {"cell_type": "markdown", "id": "6de1e373-2631-436f-a5fe-a60be2beccec", "metadata": {}, "source": ["### NG25 Left3"]}, {"cell_type": "code", "execution_count": 18, "id": "ac8d524c-a3f4-423b-b169-63d891fe60cc", "metadata": {}, "outputs": [{"data": {"text/plain": ["(191, 1)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25left3img = pd.read_csv(\"NG25left3cvat.csv\")\n", "NG25left3img.shape"]}, {"cell_type": "code", "execution_count": 45, "id": "64a42e39-c78a-4e43-afd5-ca950351f61f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(116, 6)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>329</th>\n", "      <td>319</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-144508_M0804655.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>330</th>\n", "      <td>318</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-144510_M0804656.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>331</th>\n", "      <td>143</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-144512_M0804657.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>332</th>\n", "      <td>147</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-144514_M0804658.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>333</th>\n", "      <td>144</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-144516_M0804659.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "329  319   7008    4672  KES22_WOT-L_20220923B-144508_M0804655.jpg        1   \n", "330  318   7008    4672  KES22_WOT-L_20220923B-144510_M0804656.jpg        1   \n", "331  143   7008    4672  KES22_WOT-L_20220923B-144512_M0804657.jpg        1   \n", "332  147   7008    4672  KES22_WOT-L_20220923B-144514_M0804658.jpg        1   \n", "333  144   7008    4672  KES22_WOT-L_20220923B-144516_M0804659.jpg        1   \n", "\n", "    date_captured  \n", "329    2022-09-23  \n", "330    2022-09-23  \n", "331    2022-09-23  \n", "332    2022-09-23  \n", "333    2022-09-23  "]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25left3posvimg = ng25leftposve_df[ng25leftposve_df['file_name'].isin(NG25left3img['cvatfilename'])]\n", "print(ng25left3posvimg.shape)\n", "ng25left3posvimg.head()"]}, {"cell_type": "code", "execution_count": 46, "id": "1ff444d9-202d-47fd-b94e-9dc9d4dd510e", "metadata": {}, "outputs": [{"data": {"text/plain": ["(589, 7)"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return NG25 left3 annotations \n", "\n", "ng25left3_annot = annotationsdf[annotationsdf['image_id'].isin(ng25left3posvimg['id'])]\n", "ng25left3_annot.shape"]}, {"cell_type": "code", "execution_count": 47, "id": "f256b5de-1420-45f9-9864-ff5b21520e41", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>[]</td>\n", "      <td>1384.146154</td>\n", "      <td>[6360.960244683219, 625.1656233772903, 54.6520...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>2535.911877</td>\n", "      <td>[193.9164645864987, 397.2349395771913, 39.9585...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>1178.689536</td>\n", "      <td>[51.1632, 457.3888, 25.2288, 46.72]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>7721.217171</td>\n", "      <td>[594.6750637808044, 2266.2291017559783, 135.74...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>15387.282791</td>\n", "      <td>[771.7848110372613, 2336.038783156333, 127.984...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1            7           []   1384.146154   \n", "1   2         2           23           []   2535.911877   \n", "2   3         2           23           []   1178.689536   \n", "3   4         3           15           []   7721.217171   \n", "4   5         3           15           []  15387.282791   \n", "\n", "                                                bbox  iscrowd  \n", "0  [6360.960244683219, 625.1656233772903, 54.6520...        0  \n", "1  [193.9164645864987, 397.2349395771913, 39.9585...        0  \n", "2                [51.1632, 457.3888, 25.2288, 46.72]        0  \n", "3  [594.6750637808044, 2266.2291017559783, 135.74...        0  \n", "4  [771.7848110372613, 2336.038783156333, 127.984...        0  "]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25left3_annot.head()"]}, {"cell_type": "markdown", "id": "f6e583e3-89d5-49c7-a444-8b4e6ed4b1fd", "metadata": {}, "source": ["### NG25 Right1"]}, {"cell_type": "code", "execution_count": 51, "id": "b417ceab-65b7-4c2d-8dd1-959f72b5073c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(451, 6)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>622</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140021_M0702364.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>629</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140023_M0702365.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>633</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140025_M0702366.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>624</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140027_M0702367.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>620</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140031_M0702369.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0  622   7008    4672  KES22_WOT-R_20220923B-140021_M0702364.jpg        1   \n", "1  629   7008    4672  KES22_WOT-R_20220923B-140023_M0702365.jpg        1   \n", "2  633   7008    4672  KES22_WOT-R_20220923B-140025_M0702366.jpg        1   \n", "3  624   7008    4672  KES22_WOT-R_20220923B-140027_M0702367.jpg        1   \n", "4  620   7008    4672  KES22_WOT-R_20220923B-140031_M0702369.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25rightposve_df = pd.read_csv(\"NG25rightimagespos.csv\")\n", "print(ng25rightposve_df.shape)\n", "ng25rightposve_df.head()"]}, {"cell_type": "code", "execution_count": 52, "id": "452a4d8c-5588-44d8-bb09-84f29dcf06a2", "metadata": {}, "outputs": [{"data": {"text/plain": ["(204, 1)"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25right1img = pd.read_csv(\"NG25right1cvat.csv\")\n", "NG25right1img.shape"]}, {"cell_type": "code", "execution_count": 53, "id": "dc14be6a-c766-425b-9c0b-cfb41c4a3e7c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(126, 6)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>401</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-141322_M0702753.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>109</th>\n", "      <td>498</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-141346_M0702765.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110</th>\n", "      <td>497</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-141352_M0702768.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>111</th>\n", "      <td>399</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-141354_M0702769.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>112</th>\n", "      <td>405</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-141356_M0702770.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "108  401   7008    4672  KES22_WOT-R_20220923B-141322_M0702753.jpg        1   \n", "109  498   7008    4672  KES22_WOT-R_20220923B-141346_M0702765.jpg        1   \n", "110  497   7008    4672  KES22_WOT-R_20220923B-141352_M0702768.jpg        1   \n", "111  399   7008    4672  KES22_WOT-R_20220923B-141354_M0702769.jpg        1   \n", "112  405   7008    4672  KES22_WOT-R_20220923B-141356_M0702770.jpg        1   \n", "\n", "    date_captured  \n", "108    2022-09-23  \n", "109    2022-09-23  \n", "110    2022-09-23  \n", "111    2022-09-23  \n", "112    2022-09-23  "]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25right1posvimg = ng25rightposve_df[ng25rightposve_df['file_name'].isin(NG25right1img['cvatfilename'])]\n", "print(ng25right1posvimg.shape)\n", "ng25right1posvimg.head()"]}, {"cell_type": "code", "execution_count": 54, "id": "c5b05199-3fbd-4c93-bc68-964797748123", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1087, 7)"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return NG25 right 1 annotations \n", "\n", "ng25right1_annot = annotationsdf[annotationsdf['image_id'].isin(ng25right1posvimg['id'])]\n", "ng25right1_annot.shape"]}, {"cell_type": "markdown", "id": "03f0550d-e3bd-47c9-9df0-624e2f345b04", "metadata": {}, "source": ["### NG25 Right2"]}, {"cell_type": "code", "execution_count": 56, "id": "5e032f14-ec06-44a5-8c5d-2a1c575e9781", "metadata": {}, "outputs": [{"data": {"text/plain": ["(182, 1)"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25right2img = pd.read_csv(\"NG25right2cvat.csv\")\n", "NG25right2img.shape"]}, {"cell_type": "code", "execution_count": 57, "id": "4e1b6815-2c86-472a-9d8c-e0006ff11be4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(105, 6)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>234</th>\n", "      <td>720</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-142745_M0703183.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>235</th>\n", "      <td>536</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-142751_M0703186.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>236</th>\n", "      <td>539</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-142753_M0703187.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>237</th>\n", "      <td>541</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-142755_M0703188.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>238</th>\n", "      <td>534</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-142757_M0703189.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "234  720   7008    4672  KES22_WOT-R_20220923B-142745_M0703183.jpg        1   \n", "235  536   7008    4672  KES22_WOT-R_20220923B-142751_M0703186.jpg        1   \n", "236  539   7008    4672  KES22_WOT-R_20220923B-142753_M0703187.jpg        1   \n", "237  541   7008    4672  KES22_WOT-R_20220923B-142755_M0703188.jpg        1   \n", "238  534   7008    4672  KES22_WOT-R_20220923B-142757_M0703189.jpg        1   \n", "\n", "    date_captured  \n", "234    2022-09-23  \n", "235    2022-09-23  \n", "236    2022-09-23  \n", "237    2022-09-23  \n", "238    2022-09-23  "]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25right2posvimg = ng25rightposve_df[ng25rightposve_df['file_name'].isin(NG25right2img['cvatfilename'])]\n", "print(ng25right2posvimg.shape)\n", "ng25right2posvimg.head()"]}, {"cell_type": "code", "execution_count": 58, "id": "110e568f-db2d-479c-bb1f-9223f3d0900d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(940, 7)"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return NG25 right 2 annotations \n", "\n", "ng25right2_annot = annotationsdf[annotationsdf['image_id'].isin(ng25right2posvimg['id'])]\n", "ng25right2_annot.shape"]}, {"cell_type": "code", "execution_count": null, "id": "7a388e1e-743a-473c-ba62-632d7e701f38", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "4e4cedaf-ed85-4fad-8853-f76ac7d92c37", "metadata": {}, "source": ["### NG25 Right3"]}, {"cell_type": "code", "execution_count": 60, "id": "9300d959-d8da-4807-84cb-fc2fa2faf636", "metadata": {}, "outputs": [{"data": {"text/plain": ["(201, 1)"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25right3img = pd.read_csv(\"NG25right3cvat.csv\")\n", "NG25right3img.shape"]}, {"cell_type": "code", "execution_count": 61, "id": "93e80a3c-8ca8-40f7-9861-1c7579921b60", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(112, 6)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>339</th>\n", "      <td>224</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-144408_M0703673.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>340</th>\n", "      <td>225</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-144410_M0703674.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>341</th>\n", "      <td>226</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-144414_M0703676.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>342</th>\n", "      <td>221</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-144416_M0703677.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>343</th>\n", "      <td>222</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-144418_M0703678.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "339  224   7008    4672  KES22_WOT-R_20220923B-144408_M0703673.jpg        1   \n", "340  225   7008    4672  KES22_WOT-R_20220923B-144410_M0703674.jpg        1   \n", "341  226   7008    4672  KES22_WOT-R_20220923B-144414_M0703676.jpg        1   \n", "342  221   7008    4672  KES22_WOT-R_20220923B-144416_M0703677.jpg        1   \n", "343  222   7008    4672  KES22_WOT-R_20220923B-144418_M0703678.jpg        1   \n", "\n", "    date_captured  \n", "339    2022-09-23  \n", "340    2022-09-23  \n", "341    2022-09-23  \n", "342    2022-09-23  \n", "343    2022-09-23  "]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25right3posvimg = ng25rightposve_df[ng25rightposve_df['file_name'].isin(NG25right3img['cvatfilename'])]\n", "print(ng25right3posvimg.shape)\n", "ng25right3posvimg.head()"]}, {"cell_type": "code", "execution_count": 62, "id": "d4946f02-73d7-46a4-8c0c-092924faac7e", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1090, 7)"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return NG25 right 3 annotations \n", "\n", "ng25right3_annot = annotationsdf[annotationsdf['image_id'].isin(ng25right3posvimg['id'])]\n", "ng25right3_annot.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c2eb36ab-1024-4562-b872-e1cae67bab66", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5259e8b4-d426-487d-91fd-92baeb3e1556", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "0d7f0383-e09b-4143-80f5-9c160d232903", "metadata": {}, "source": ["### Annotations COCO file"]}, {"cell_type": "code", "execution_count": 40, "id": "c73eabe2-7664-4bef-99bf-2cb52c3fa3a8", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "coco_dict['info']={\"description\": \"NG25 Stratum annotations\", \"version\": \"1.0\", \"year\": 2023, \"contributor\": \"MWS Lab Arusha\", \"date_created\": \"2023-09-23\"}\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = ng25left1posvimg.to_dict(orient='records')\n", "coco_dict['annotations'] = ng25left1_annot.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'NG25left1annotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": 44, "id": "254708b5-3db5-489a-bc35-918c449acee8", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "coco_dict['info']={\"description\": \"NG25 Stratum annotations\", \"version\": \"1.0\", \"year\": 2023, \"contributor\": \"MWS Lab Arusha\", \"date_created\": \"2023-09-23\"}\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = ng25left2posvimg.to_dict(orient='records')\n", "coco_dict['annotations'] = ng25left2_annot.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'NG25left2annotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": 48, "id": "16dd420b-4f4d-42de-9aa8-1917a1d7a760", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "coco_dict['info']={\"description\": \"NG25 Stratum annotations\", \"version\": \"1.0\", \"year\": 2023, \"contributor\": \"MWS Lab Arusha\", \"date_created\": \"2023-09-23\"}\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = ng25left3posvimg.to_dict(orient='records')\n", "coco_dict['annotations'] = ng25left3_annot.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'NG25left3annotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": 55, "id": "043cfaf2-580a-4a72-9871-0e8bbad31a7f", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "coco_dict['info']={\"description\": \"NG25 Stratum annotations\", \"version\": \"1.0\", \"year\": 2023, \"contributor\": \"MWS Lab Arusha\", \"date_created\": \"2023-09-23\"}\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = ng25right1posvimg.to_dict(orient='records')\n", "coco_dict['annotations'] = ng25right1_annot.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'NG25right1annotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": 59, "id": "b4a9d469-a6f1-4604-a256-31f434cc96e2", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "}\n", "coco_dict['info']={\"description\": \"NG25 Stratum annotations\", \"version\": \"1.0\", \"year\": 2023, \"contributor\": \"MWS Lab Arusha\", \"date_created\": \"2023-09-23\"}\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = ng25right2posvimg.to_dict(orient='records')\n", "coco_dict['annotations'] = ng25right2_annot.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'NG25right2annotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": 63, "id": "559ec43a-fb1f-467f-bba6-d87c816bf8f4", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "coco_dict['info']={\"description\": \"NG25 Stratum annotations\", \"version\": \"1.0\", \"year\": 2023, \"contributor\": \"MWS Lab Arusha\", \"date_created\": \"2023-09-23\"}\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = ng25right3posvimg.to_dict(orient='records')\n", "coco_dict['annotations'] = ng25right3_annot.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'NG25right3annotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "id": "55052d5c-c649-4f9d-8ed1-6ed39a61a070", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5f664c3c-e0ea-46b0-bf5d-19d93d789d00", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "404a8933-c6e7-42b9-be5d-3fbf433edf68", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "49c33739-657e-4fdb-a3ca-b116cdd1b687", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a90e2204-03dc-4587-a437-7140f9f0a890", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}