#!/usr/bin/env python
# coding: utf-8



def return_value_based_on_condition(json_data, condition_key, condition_value, return_key):
    """
    Return a value from a dictionary based on a condition.

    Parameters:
    - json_data: The input list of dictionaries
    - condition_key: The key for the condition
    - condition_value: The value that should fulfill the condition
    - return_key: The key of the element to return

    Returns:
    - The value of return_key if the condition is fulfilled, else None
    """
    for item in json_data:
        if item.get(condition_key) == condition_value:
            return item.get(return_key)
    return None

def csv_to_coco(csv_path, output_json_path):
    import pandas as pd
    import json
    # Load CSV annotations data using pandas
    df = pd.read_csv(csv_path)
    
       
    # Create a COCO-style dictionary
    coco_data = {
        "info": {"description": "DAN Block annotations", "version": "1.0", "year": 2023, "contributor": "MWS Lab Arusha", "date_created": "2023-11-29"},
        "licenses": [{"id": 1, "name": "KAZA Elephant Survey", "url": "https://www.kavangozambezi.org/kaza-elephant-survey/"}],
        "categories": [],
        "images": [],
        "annotations": []        
    }

    # Extract unique class labels from the "Label" column in the CSV
    classes = sorted(df['Label'].unique())

    # Create COCO categories
    for i, class_name in enumerate(classes, start=1):
        coco_data['categories'].append({"id": i, "name": class_name, "supercategory": "object"})

    # Create COCO images
    imagelist = df["ImageFilename"].unique()
    
    for image_id, row in enumerate(imagelist, start=1):
        image_info = {
            "id": image_id,
            "width": 7008,
            "height": 4672,
            "filename": row,
            "license": 1,
            "date_captured": "2022-09-06"
        }
        coco_data["images"].append(image_info)

        # Create COCO annotations
        annotation_id =  1
    for index, row in df.iterrows():
        imageid = return_value_based_on_condition(coco_data["images"], "filename", row["ImageFilename"], "id") 
        
        annotation_info = {
            "id": annotation_id,
            "image_id": imageid,
            "category_id": classes.index(row['Label']) + 1,
            "segmentation": [],
            "area": abs(row['BoxW'] * row['BoxH']),
            "bbox": [row['BoxX'], row['BoxY'], row['BoxW'], row['BoxH']],
            "iscrowd": 0
        }

        coco_data["annotations"].append(annotation_info)
        annotation_id += 1
        
    # Save the COCO formatted data to a JSON file
    with open(output_json_path, "w") as json_file:
        json.dump(coco_data, json_file, indent=4)

if __name__ == "__main__":
    csv_path = "/Users/<USER>/Dropbox/Conservation/MWSLab-2023/scoutexports/DANscout-export-annotations.csv"
    output_json_path = "/Users/<USER>/Dropbox/Conservation/MWSLab-2023/utils/_annotationsdan.coco.json"

    csv_to_coco(csv_path, output_json_path)





