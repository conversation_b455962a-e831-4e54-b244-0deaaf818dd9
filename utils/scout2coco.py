# Convert scout CSV to COCO

import csv
import json



def csv_to_coco(csv_filename, coco_filename):
    # Initialize COCO dictionary structure
    coco_data = {
        "info": {},
        "licenses": [], 
        "images": [],
        "annotations": [],
        "categories": []
    }

    # Placeholder for categories and their IDs
    categories = {}
    category_id = 1

    image_ids = set()
    annotation_id = 1

    with open(csv_filename, "r") as csv_file:
        csv_reader = csv.DictReader(csv_file)
        for row in csv_reader:
            # Dynamically add categories
            label = row["Label"]
            if label not in categories:
                categories[label] = category_id
                category_data = {
                    "id": category_id,
                    "name": label,
                    "supercategory": "animal"  # This can be changed or made dynamic if needed
                }
                coco_data["categories"].append(category_data)
                category_id += 1

            # Add image data if not added before
            image_id = row["Image Filename"]
            if image_id not in image_ids:
                image_data = {
                    "id": image_id,
                    "file_name": row["Image Filename"],
                    "width": 7008,  
                    "height": 4672  
                }
                coco_data["images"].append(image_data)
                image_ids.add(image_id)

            # Add annotation data
            annotation_data = {
                "id": annotation_id,
                "image_id": image_id,
                "category_id": categories[label],
                "bbox": [
                    float(row["Box X"]),
                    float(row["Box Y"]),
                    float(row["Box W"]),
                    float(row["Box H"])
                ],
                "area": float(row["Box W"]) * float(row["Box H"]),
                "iscrowd": 0
            }
            coco_data["annotations"].append(annotation_data)
            annotation_id += 1

    # Write COCO data to JSON file
    with open(coco_filename, "w") as json_file:
        json.dump(coco_data, json_file, indent=4)

# Usage
csv_to_coco("annotations.csv", "annotations_coco.json")
