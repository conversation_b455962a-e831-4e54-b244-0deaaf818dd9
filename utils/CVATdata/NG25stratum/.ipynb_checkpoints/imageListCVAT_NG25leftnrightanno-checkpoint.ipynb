{"cells": [{"cell_type": "markdown", "id": "ff9ed367", "metadata": {}, "source": ["Divide stratum to left and right"]}, {"cell_type": "code", "execution_count": 1, "id": "8daf5dd7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "b489dc6c-8845-4df1-9680-2fd060786fc4", "metadata": {}, "outputs": [], "source": ["coco_file_path = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023/utils/CVATdata/NG25stratum/annotationsNG25.json\""]}, {"cell_type": "code", "execution_count": 3, "id": "23b685d4", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "images_df = pd.DataFrame(coco_data['images'])\n"]}, {"cell_type": "code", "execution_count": 4, "id": "4a1f8fd3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(896, 6)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "8e9230e2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145132_M0804846.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145128_M0804844.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145428_M0804934.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   1   7008    4672  KES22_WOT-L_20220923B-145132_M0804846.jpg        1   \n", "1   2   7008    4672  KES22_WOT-L_20220923B-145454_M0804947.jpg        1   \n", "2   3   7008    4672  KES22_WOT-L_20220923B-145256_M0804888.jpg        1   \n", "3   4   7008    4672  KES22_WOT-L_20220923B-145128_M0804844.jpg        1   \n", "4   5   7008    4672  KES22_WOT-L_20220923B-145428_M0804934.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "60719a18", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "id": "4e4444a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(445, 6)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "ng25leftposve_df = pd.read_csv(\"NG25leftimagespos.csv\")\n", "\n", "# Print the resulting filtered DataFrame\n", "ng25leftposve_df.shape"]}, {"cell_type": "code", "execution_count": 7, "id": "060a581a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>814</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140119_M0803345.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>818</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140128_M0803349.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>816</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140130_M0803350.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>822</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140132_M0803351.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>817</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140136_M0803353.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0  814   7008    4672  KES22_WOT-L_20220923B-140119_M0803345.jpg        1   \n", "1  818   7008    4672  KES22_WOT-L_20220923B-140128_M0803349.jpg        1   \n", "2  816   7008    4672  KES22_WOT-L_20220923B-140130_M0803350.jpg        1   \n", "3  822   7008    4672  KES22_WOT-L_20220923B-140132_M0803351.jpg        1   \n", "4  817   7008    4672  KES22_WOT-L_20220923B-140136_M0803353.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25leftposve_df.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "164fc814", "metadata": {}, "outputs": [{"data": {"text/plain": ["(235, 1)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25left1img = pd.read_csv(\"NG25left1cvat.csv\")\n", "NG25left1img.shape"]}, {"cell_type": "code", "execution_count": 9, "id": "932d181b-abf7-45cb-b26d-c73c7d9163f0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_WOT-L_20220923B-141346_M0803717.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_WOT-L_20220923B-141348_M0803718.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_WOT-L_20220923B-141350_M0803719.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_WOT-L_20220923B-141420_M0803734.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_WOT-L_20220923B-141422_M0803735.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                c<PERSON><PERSON><PERSON><PERSON>\n", "0  KES22_WOT-L_20220923B-141346_M0803717.jpg\n", "1  KES22_WOT-L_20220923B-141348_M0803718.jpg\n", "2  KES22_WOT-L_20220923B-141350_M0803719.jpg\n", "3  KES22_WOT-L_20220923B-141420_M0803734.jpg\n", "4  KES22_WOT-L_20220923B-141422_M0803735.jpg"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25left1img.head()"]}, {"cell_type": "code", "execution_count": 15, "id": "7bc344e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(135, 6)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>320</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-141348_M0803718.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>324</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-141422_M0803735.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>327</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-141426_M0803737.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>322</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-141428_M0803738.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89</th>\n", "      <td>321</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-141430_M0803739.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     id  width  height                                  file_name  license  \\\n", "85  320   7008    4672  KES22_WOT-L_20220923B-141348_M0803718.jpg        1   \n", "86  324   7008    4672  KES22_WOT-L_20220923B-141422_M0803735.jpg        1   \n", "87  327   7008    4672  KES22_WOT-L_20220923B-141426_M0803737.jpg        1   \n", "88  322   7008    4672  KES22_WOT-L_20220923B-141428_M0803738.jpg        1   \n", "89  321   7008    4672  KES22_WOT-L_20220923B-141430_M0803739.jpg        1   \n", "\n", "   date_captured  \n", "85    2022-09-23  \n", "86    2022-09-23  \n", "87    2022-09-23  \n", "88    2022-09-23  \n", "89    2022-09-23  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25left1posvimg = ng25leftposve_df[ng25leftposve_df['file_name'].isin(NG25left1img['cvatfilename'])]\n", "\n", "print(ng25left1posvimg.shape)\n", "ng25left1posvimg.head()"]}, {"cell_type": "markdown", "id": "cbd7e416-7abf-4909-86c0-f9a7d7995982", "metadata": {}, "source": ["### Annotations"]}, {"cell_type": "code", "execution_count": 23, "id": "dd3775f6-a803-46b1-b0f4-3cb6d6ffa002", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7142, 7)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf = pd.DataFrame(coco_data['annotations'])\n", "annotationsdf.shape"]}, {"cell_type": "code", "execution_count": 24, "id": "6c3c7bf7-6c96-4c9e-873a-a57d5c1277b8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>[]</td>\n", "      <td>1384.146154</td>\n", "      <td>[6360.960244683219, 625.1656233772903, 54.6520...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>2535.911877</td>\n", "      <td>[193.9164645864987, 397.2349395771913, 39.9585...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>1178.689536</td>\n", "      <td>[26.1632, 457.3888, -25.2288, 46.72]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>7721.217171</td>\n", "      <td>[594.6750637808044, 2266.2291017559783, 135.74...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>15387.282791</td>\n", "      <td>[771.7848110372613, 2336.038783156333, 127.984...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1            7           []   1384.146154   \n", "1   2         2           23           []   2535.911877   \n", "2   3         2           23           []   1178.689536   \n", "3   4         3           15           []   7721.217171   \n", "4   5         3           15           []  15387.282791   \n", "\n", "                                                bbox  iscrowd  \n", "0  [6360.960244683219, 625.1656233772903, 54.6520...        0  \n", "1  [193.9164645864987, 397.2349395771913, 39.9585...        0  \n", "2               [26.1632, 457.3888, -25.2288, 46.72]        0  \n", "3  [594.6750637808044, 2266.2291017559783, 135.74...        0  \n", "4  [771.7848110372613, 2336.038783156333, 127.984...        0  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "75c1d751-58aa-4d17-971d-63cfc6562f9c", "metadata": {}, "outputs": [], "source": ["#change bbox dimensions to positive\n", "greater_than_zero_mask = [all(item > 0 for item in row) for row in annotationsdf['bbox']]\n", "annotationsdf[greater_than_zero_mask].shape"]}, {"cell_type": "code", "execution_count": null, "id": "8f5bc11b-06b0-41e3-b0a8-052639347d0a", "metadata": {}, "outputs": [], "source": ["filterpositive = annotationsdf[greater_than_zero_mask]['bbox'].tolist()\n", "less_than_zero_mask = annotationsdf[~annotationsdf['bbox'].isin(filterpositive)]\n", "less_than_zero_mask.shape"]}, {"cell_type": "code", "execution_count": null, "id": "44bd0fbd-a606-41e3-a682-c2f5a7b31433", "metadata": {}, "outputs": [], "source": ["less_than_zero_mask.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cbad6219-0af1-4962-997f-34803fce4049", "metadata": {}, "outputs": [], "source": ["for item in less_than_zero_mask['bbox']:\n", "    \n", "    if((item[-2]<0) and (item[-1]<0)):\n", "        item[-2] = abs(item[-2])\n", "        item[0]  = item[0] + int(abs(item[-2]))\n", "        item[-1] = abs(item[-1])\n", "        item[1] = item[1] + int(abs(item[-1]))\n", "    \n", "    elif((item[0]<0) and (item[1]<0)):\n", "        item[0] = abs(item[0])\n", "        item[-2] = item[-2] + int(abs(item[0]))\n", "        item[1] = abs(item[1])\n", "        item[-1] = item[-1] + int(abs(item[1]))\n", "        \n", "    elif(item[-2]<0):\n", "        item[-2] = abs(item[-2])\n", "        item[0]  = item[0] + int(abs(item[-2]))\n", "    \n", "    elif(item[0]<0):\n", "        item[0] = abs(item[0])\n", "        item[-2] = item[-2] + int(abs(item[0]))\n", "           \n", "    elif(item[1]<0):\n", "        item[1] = abs(item[1])\n", "        item[-1] = item[-1] + int(abs(item[1]))\n", "            \n", "    elif(item[-1]<0):\n", "        item[-1] = abs(item[-1])\n", "        item[1] = item[1] + int(abs(item[-1]))"]}, {"cell_type": "code", "execution_count": null, "id": "61f2485f-e424-4ab0-9f53-2d1febebcc5b", "metadata": {}, "outputs": [], "source": ["annotationsdf.update(less_than_zero_mask)"]}, {"cell_type": "code", "execution_count": null, "id": "ecdb675a-3d15-4c20-8809-c7e02ec11ac0", "metadata": {}, "outputs": [], "source": ["#check if all are positives after\n", "greater_than_zero_mask = [all(item > 0 for item in row) for row in annotationsdf['bbox']]\n", "annotationsdf[greater_than_zero_mask].shape"]}, {"cell_type": "code", "execution_count": null, "id": "1d4bf6dd-e081-4b44-8979-5e6c87e68fbe", "metadata": {}, "outputs": [], "source": ["annotationsdf.shape"]}, {"cell_type": "code", "execution_count": null, "id": "151cebe6-7e03-4eef-9ce2-18f662dd8dfb", "metadata": {}, "outputs": [], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6ac12ce8-743f-4844-93d4-13cbb5006961", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"id\"]=annotationsdf[\"id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "da98f6a5-03bd-4630-800d-76e1f085925e", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"image_id\"] =annotationsdf[\"image_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "1560c7d8-fcd8-400c-aa13-b5d9f981ef5f", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"iscrowd\"] = annotationsdf[\"iscrowd\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "4c006a27-977a-47f4-8677-2cff0d696946", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"category_id\"] = annotationsdf[\"category_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": null, "id": "5e46ada1-1f2c-432b-9362-bef0f3e3ba33", "metadata": {}, "outputs": [], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "ce65f209-a8d6-46c0-a3cd-78f9717e3c3e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3e44270a-c9d2-4535-a70a-17113c34d8b5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "449f5a3e-6aa8-4c2d-aa7b-c64739e55960", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "60a7e96f-49e0-4569-8a9c-8f862a081c7f", "metadata": {}, "outputs": [], "source": ["# Compare image 'ID' columns and return MTb left annotations \n", "\n", "ng25left1_annot = annotationsdf[annotationsdf['image_id'].isin(ng25left1posvimg['id'])]\n", "ng25left1_annot.shape"]}, {"cell_type": "code", "execution_count": null, "id": "d7d2b647-b9fe-4dbf-9b3d-2384c8bb4e56", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f8b537d0-459b-48f9-bec6-d5111c066b18", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "281118d4-d61d-4a56-98a2-45a0b49ce8f2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "273f1e21-ec51-49ce-ab39-a23d397ec232", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7e4d76b9-a7a3-4251-8abd-53d0652b9619", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "19a48256-6f24-4cbc-94ad-2f362501dc8e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "389d1c79-6b05-4ba0-99fd-01543531712c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 16, "id": "18c4b4b1-9afa-4a84-8164-68be3fbd98ba", "metadata": {}, "outputs": [{"data": {"text/plain": ["(207, 1)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25left2img = pd.read_csv(\"NG25left2cvat.csv\")\n", "NG25left2img.shape"]}, {"cell_type": "code", "execution_count": 17, "id": "106efe92-98f0-41bd-8b3c-7da5617893dc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(109, 6)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>760</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-142815_M0804150.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>221</th>\n", "      <td>759</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-142833_M0804159.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>222</th>\n", "      <td>752</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-142843_M0804164.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>223</th>\n", "      <td>761</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-142851_M0804168.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>224</th>\n", "      <td>765</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-142903_M0804174.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "220  760   7008    4672  KES22_WOT-L_20220923B-142815_M0804150.jpg        1   \n", "221  759   7008    4672  KES22_WOT-L_20220923B-142833_M0804159.jpg        1   \n", "222  752   7008    4672  KES22_WOT-L_20220923B-142843_M0804164.jpg        1   \n", "223  761   7008    4672  KES22_WOT-L_20220923B-142851_M0804168.jpg        1   \n", "224  765   7008    4672  KES22_WOT-L_20220923B-142903_M0804174.jpg        1   \n", "\n", "    date_captured  \n", "220    2022-09-23  \n", "221    2022-09-23  \n", "222    2022-09-23  \n", "223    2022-09-23  \n", "224    2022-09-23  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25left2posvimg = ng25leftposve_df[ng25leftposve_df['file_name'].isin(NG25left2img['cvatfilename'])]\n", "print(ng25left2posvimg.shape)\n", "ng25left2posvimg.head()"]}, {"cell_type": "code", "execution_count": 18, "id": "ac8d524c-a3f4-423b-b169-63d891fe60cc", "metadata": {}, "outputs": [{"data": {"text/plain": ["(191, 1)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25left3img = pd.read_csv(\"NG25left3cvat.csv\")\n", "NG25left3img.shape"]}, {"cell_type": "code", "execution_count": 19, "id": "64a42e39-c78a-4e43-afd5-ca950351f61f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(116, 6)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>329</th>\n", "      <td>319</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-144508_M0804655.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>330</th>\n", "      <td>318</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-144510_M0804656.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>331</th>\n", "      <td>143</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-144512_M0804657.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>332</th>\n", "      <td>147</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-144514_M0804658.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>333</th>\n", "      <td>144</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-144516_M0804659.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "329  319   7008    4672  KES22_WOT-L_20220923B-144508_M0804655.jpg        1   \n", "330  318   7008    4672  KES22_WOT-L_20220923B-144510_M0804656.jpg        1   \n", "331  143   7008    4672  KES22_WOT-L_20220923B-144512_M0804657.jpg        1   \n", "332  147   7008    4672  KES22_WOT-L_20220923B-144514_M0804658.jpg        1   \n", "333  144   7008    4672  KES22_WOT-L_20220923B-144516_M0804659.jpg        1   \n", "\n", "    date_captured  \n", "329    2022-09-23  \n", "330    2022-09-23  \n", "331    2022-09-23  \n", "332    2022-09-23  \n", "333    2022-09-23  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25left3posvimg = ng25leftposve_df[ng25leftposve_df['file_name'].isin(NG25left3img['cvatfilename'])]\n", "print(ng25left3posvimg.shape)\n", "ng25left3posvimg.head()"]}, {"cell_type": "code", "execution_count": null, "id": "1ff444d9-202d-47fd-b94e-9dc9d4dd510e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f256b5de-1420-45f9-9864-ff5b21520e41", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0a0db288-4e4a-44e4-9d4f-0b477cb22124", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "28dd70fc-3624-4354-a731-26a59cf96d70", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "27a65bdb-31fb-49fa-a2c5-702ed9aaba97", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8f1d0611-c610-43f4-96c3-0c8dc9d951e0", "metadata": {}, "outputs": [], "source": ["### Annotations"]}, {"cell_type": "code", "execution_count": 12, "id": "d70b352f-21fb-427c-a066-abddcdac483c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(445, 6)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return MTb left annotations \n", "\n", "mtbright_annot = annotationsdf[annotationsdf['image_id'].isin(MTbrightposve['id'])]\n", "mtbright_annot.shape"]}, {"cell_type": "code", "execution_count": 14, "id": "87ff32c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(6338, 11)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25imgs.shape"]}, {"cell_type": "code", "execution_count": 15, "id": "fbff7df6", "metadata": {}, "outputs": [{"data": {"text/plain": ["3050"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25imgs[\"ImageFilename\"].nunique()"]}, {"cell_type": "code", "execution_count": 16, "id": "6b578304", "metadata": {}, "outputs": [], "source": ["ng25imgsunique = ng25imgs[\"ImageFilename\"].unique()"]}, {"cell_type": "code", "execution_count": 17, "id": "7675cd62", "metadata": {}, "outputs": [{"data": {"text/plain": ["3050"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["len(ng25imgsunique)"]}, {"cell_type": "code", "execution_count": 18, "id": "c1e7f0f8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_WOT-L_20220923B-145114_M0804837.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_WOT-L_20220923B-145144_M0804852.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_WOT-L_20220923B-145158_M0804859.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_WOT-L_20220923B-145246_M0804883.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_WOT-L_20220923B-145236_M0804878.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   file_name\n", "0  KES22_WOT-L_20220923B-145114_M0804837.jpg\n", "1  KES22_WOT-L_20220923B-145144_M0804852.jpg\n", "2  KES22_WOT-L_20220923B-145158_M0804859.jpg\n", "3  KES22_WOT-L_20220923B-145246_M0804883.jpg\n", "4  KES22_WOT-L_20220923B-145236_M0804878.jpg"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25imgsdf = pd.DataFrame(ng25imgsunique,columns=[\"file_name\"])\n", "ng25imgsdf.head()"]}, {"cell_type": "code", "execution_count": 19, "id": "b6debf9d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1220, 1)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25imgsuniqueleft = ng25imgsdf[ng25imgsdf['file_name'].str.contains(pattern1, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "ng25imgsuniqueleft.shape"]}, {"cell_type": "code", "execution_count": 20, "id": "929403df-0066-490d-b8ef-5c1435665e37", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_WOT-L_20220923B-145114_M0804837.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_WOT-L_20220923B-145144_M0804852.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_WOT-L_20220923B-145158_M0804859.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_WOT-L_20220923B-145246_M0804883.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_WOT-L_20220923B-145236_M0804878.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   file_name\n", "0  KES22_WOT-L_20220923B-145114_M0804837.jpg\n", "1  KES22_WOT-L_20220923B-145144_M0804852.jpg\n", "2  KES22_WOT-L_20220923B-145158_M0804859.jpg\n", "3  KES22_WOT-L_20220923B-145246_M0804883.jpg\n", "4  KES22_WOT-L_20220923B-145236_M0804878.jpg"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25imgsuniqueleft.head()"]}, {"cell_type": "code", "execution_count": 21, "id": "247cec3a-dece-43d8-ab76-c7ff9b1a5244", "metadata": {}, "outputs": [], "source": ["NG25leftimgsorted = ng25imgsuniqueleft.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 22, "id": "6cc04368-35fb-4d16-a052-218249487290", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1197</th>\n", "      <td>KES22_WOT-L_20220923B-141314_M0803701.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1218</th>\n", "      <td>KES22_WOT-L_20220923B-141316_M0803702.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1198</th>\n", "      <td>KES22_WOT-L_20220923B-141318_M0803703.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1195</th>\n", "      <td>KES22_WOT-L_20220923B-141320_M0803704.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1178</th>\n", "      <td>KES22_WOT-L_20220923B-141322_M0803705.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      file_name\n", "1197  KES22_WOT-L_20220923B-141314_M0803701.jpg\n", "1218  KES22_WOT-L_20220923B-141316_M0803702.jpg\n", "1198  KES22_WOT-L_20220923B-141318_M0803703.jpg\n", "1195  KES22_WOT-L_20220923B-141320_M0803704.jpg\n", "1178  KES22_WOT-L_20220923B-141322_M0803705.jpg"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25leftimgsorted.head()"]}, {"cell_type": "code", "execution_count": 23, "id": "2b99b790-5318-4b36-a53a-16d3756b4037", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in NG25leftsorted[\"file_name\"]:\n", "    if item in NG25leftimgsorted[\"file_name\"].values:\n", "        index_in_pos = NG25leftimgsorted[\"file_name\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(NG25leftimgsorted[\"file_name\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(NG25leftimgsorted[\"file_name\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 24, "id": "2be9136b-e16e-42d9-b00a-93ec347ddb84", "metadata": {}, "outputs": [{"data": {"text/plain": ["1080"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 25, "id": "d7696c45-bfd9-436c-afda-4239c5007d50", "metadata": {}, "outputs": [{"data": {"text/plain": ["633"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 26, "id": "008a9c33-b2e3-4b6f-875e-1a9b32e5f81b", "metadata": {}, "outputs": [], "source": ["cvatimg = pd.DataFrame(fin,columns=[\"cvatfilename\"])"]}, {"cell_type": "code", "execution_count": 27, "id": "8a77236e-0e42-482c-892a-0504f417334f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>229</th>\n", "      <td>KES22_WOT-L_20220923B-141346_M0803717.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>245</th>\n", "      <td>KES22_WOT-L_20220923B-141348_M0803718.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>376</th>\n", "      <td>KES22_WOT-L_20220923B-141350_M0803719.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>KES22_WOT-L_20220923B-141420_M0803734.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>KES22_WOT-L_20220923B-141422_M0803735.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  c<PERSON><PERSON><PERSON><PERSON>\n", "229  KES22_WOT-L_20220923B-141346_M0803717.jpg\n", "245  KES22_WOT-L_20220923B-141348_M0803718.jpg\n", "376  KES22_WOT-L_20220923B-141350_M0803719.jpg\n", "163  KES22_WOT-L_20220923B-141420_M0803734.jpg\n", "20   KES22_WOT-L_20220923B-141422_M0803735.jpg"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin = cvatimg.sort_values(by=[\"cvatfilename\"],ascending=True)\n", "cvatfin.head()"]}, {"cell_type": "code", "execution_count": 28, "id": "ffdef2e9-a764-40b6-9132-94f29afa7e82", "metadata": {}, "outputs": [{"data": {"text/plain": ["(633, 1)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin.shape"]}, {"cell_type": "code", "execution_count": 29, "id": "afc94256-61ff-436c-a8a0-976acc8ab884", "metadata": {}, "outputs": [], "source": ["cvatfin.to_csv(\"NG25leftcvat.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "c2d70828-12c5-45b1-a911-9dc64154f5dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "ac85a34b-1a7e-40be-b824-ed9170a5fdb7", "metadata": {}, "source": ["### NG25 Left1 "]}, {"cell_type": "code", "execution_count": null, "id": "8ddae76b-c500-4d2d-958d-e20286f9ee1e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "911f1315-58f5-454e-ab44-d0f949c9d3e5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f2c8aa0c-0014-40b2-bbd0-fdf5356e47bc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ebdba58a-066d-4892-9203-703105041694", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "139c0d33-0250-4b42-aabc-e9a2f3839f49", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2fd5132f-0e0e-4079-b3b8-9927505a94c9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f27039eb-a5bc-4d75-9f51-e92edc3ce5c1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "613458d8-a4c1-44fc-9896-d4ee905c1c71", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f893560b-b2d5-4daf-b670-30a391a786c4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "babe06af-c5bd-408d-b577-3e9bf8d62ef9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 33, "id": "1ca074ad", "metadata": {}, "outputs": [], "source": ["pattern2 = \"KES22_WOT-R_\""]}, {"cell_type": "code", "execution_count": 34, "id": "1e9aefc9", "metadata": {}, "outputs": [{"data": {"text/plain": ["(451, 6)"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "NG25filtered_df2 = images_df[images_df['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "NG25filtered_df2.shape"]}, {"cell_type": "code", "execution_count": 35, "id": "3ad7fa26", "metadata": {}, "outputs": [], "source": ["NG25rightsorted = NG25filtered_df2.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 36, "id": "786e514b-1138-4c25-a57f-d0dd97b46ca2", "metadata": {}, "outputs": [], "source": ["NG25rightsorted.to_csv(\"NG25rightimagespos.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 37, "id": "5fd9fbb4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>621</th>\n", "      <td>622</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140021_M0702364.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>628</th>\n", "      <td>629</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140023_M0702365.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>632</th>\n", "      <td>633</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140025_M0702366.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>623</th>\n", "      <td>624</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140027_M0702367.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>619</th>\n", "      <td>620</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140031_M0702369.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "621  622   7008    4672  KES22_WOT-R_20220923B-140021_M0702364.jpg        1   \n", "628  629   7008    4672  KES22_WOT-R_20220923B-140023_M0702365.jpg        1   \n", "632  633   7008    4672  KES22_WOT-R_20220923B-140025_M0702366.jpg        1   \n", "623  624   7008    4672  KES22_WOT-R_20220923B-140027_M0702367.jpg        1   \n", "619  620   7008    4672  KES22_WOT-R_20220923B-140031_M0702369.jpg        1   \n", "\n", "    date_captured  \n", "621    2022-09-23  \n", "628    2022-09-23  \n", "632    2022-09-23  \n", "623    2022-09-23  \n", "619    2022-09-23  "]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25rightsorted.head()"]}, {"cell_type": "code", "execution_count": 45, "id": "d0822f6e-7bc5-4019-80db-51b4ebdbfbd5", "metadata": {}, "outputs": [{"data": {"text/plain": ["(451, 6)"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25rightsorted.shape"]}, {"cell_type": "code", "execution_count": 39, "id": "4a5e0113-6fbd-40f5-98ae-e7927a9dbdd9", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1219, 1)"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern for positive and negative images\n", "NG25imgright_df2 = ng25imgsdf[ng25imgsdf['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "NG25imgright_df2.shape"]}, {"cell_type": "code", "execution_count": 41, "id": "bccea1bf-8ba4-4731-b395-f9884d37d808", "metadata": {}, "outputs": [], "source": ["NG25imgrightsorted = NG25imgright_df2.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 42, "id": "557fa857-7da4-489d-9f5c-8db5342e9fac", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1533</th>\n", "      <td>KES22_WOT-R_20220923B-141314_M0702749.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1514</th>\n", "      <td>KES22_WOT-R_20220923B-141316_M0702750.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1521</th>\n", "      <td>KES22_WOT-R_20220923B-141318_M0702751.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1510</th>\n", "      <td>KES22_WOT-R_20220923B-141320_M0702752.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1513</th>\n", "      <td>KES22_WOT-R_20220923B-141322_M0702753.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      file_name\n", "1533  KES22_WOT-R_20220923B-141314_M0702749.jpg\n", "1514  KES22_WOT-R_20220923B-141316_M0702750.jpg\n", "1521  KES22_WOT-R_20220923B-141318_M0702751.jpg\n", "1510  KES22_WOT-R_20220923B-141320_M0702752.jpg\n", "1513  KES22_WOT-R_20220923B-141322_M0702753.jpg"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25imgrightsorted.head()"]}, {"cell_type": "code", "execution_count": 43, "id": "ca07d990-3f37-4f08-b294-485a4a12362d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1219, 1)"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25imgrightsorted.shape"]}, {"cell_type": "code", "execution_count": 47, "id": "b879b12b-d248-4bc7-baa7-f9119aee8f71", "metadata": {}, "outputs": [{"ename": "IndexError", "evalue": "index 1219 is out of bounds for axis 0 with size 1219", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn [47], line 15\u001b[0m\n\u001b[1;32m     12\u001b[0m new_list\u001b[38;5;241m.\u001b[39mappend(item)\n\u001b[1;32m     14\u001b[0m \u001b[38;5;66;03m#add the image after\u001b[39;00m\n\u001b[0;32m---> 15\u001b[0m new_list\u001b[38;5;241m.\u001b[39mappend(\u001b[43mNG25imgrightsorted\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfile_name\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalues\u001b[49m\u001b[43m[\u001b[49m\u001b[43mindex_in_pos\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m+\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m]\u001b[49m)\n", "\u001b[0;31mIndexError\u001b[0m: index 1219 is out of bounds for axis 0 with size 1219"]}], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in NG25rightsorted[\"file_name\"]:\n", "    if item in NG25imgrightsorted[\"file_name\"].values:\n", "        index_in_pos = NG25imgrightsorted[\"file_name\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(NG25imgrightsorted[\"file_name\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(NG25imgrightsorted[\"file_name\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 48, "id": "db6f4e5d-fbb6-46a9-9bee-8476dbe5cbbb", "metadata": {}, "outputs": [{"data": {"text/plain": ["1028"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 49, "id": "7badf276-6239-486d-b2ba-9cb2a2c3bcef", "metadata": {}, "outputs": [{"data": {"text/plain": ["587"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 50, "id": "82318eab-c30a-427e-96de-befaea605c12", "metadata": {}, "outputs": [], "source": ["cvatimg = pd.DataFrame(fin,columns=[\"cvatfilename\"])"]}, {"cell_type": "code", "execution_count": 51, "id": "efc73194-3fdb-436e-bcb9-09a25d0b2c1d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>KES22_WOT-R_20220923B-141320_M0702752.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>KES22_WOT-R_20220923B-141322_M0702753.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>419</th>\n", "      <td>KES22_WOT-R_20220923B-141324_M0702754.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>KES22_WOT-R_20220923B-141344_M0702764.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>452</th>\n", "      <td>KES22_WOT-R_20220923B-141346_M0702765.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  c<PERSON><PERSON><PERSON><PERSON>\n", "41   KES22_WOT-R_20220923B-141320_M0702752.jpg\n", "40   KES22_WOT-R_20220923B-141322_M0702753.jpg\n", "419  KES22_WOT-R_20220923B-141324_M0702754.jpg\n", "182  KES22_WOT-R_20220923B-141344_M0702764.jpg\n", "452  KES22_WOT-R_20220923B-141346_M0702765.jpg"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin = cvatimg.sort_values(by=[\"cvatfilename\"],ascending=True)\n", "cvatfin.head()"]}, {"cell_type": "code", "execution_count": 52, "id": "67455f80-c824-4529-b515-437b119701c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(587, 1)"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin.shape"]}, {"cell_type": "code", "execution_count": 53, "id": "dc41c311-5c01-4556-8eca-b38590211628", "metadata": {}, "outputs": [], "source": ["cvatfin.to_csv(\"NG25rightcvat.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "a9973fe2-38f9-4102-8131-4cc4d2db4c4e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b16ac600-595c-47fe-ba9a-b7c7d29f7111", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5259e8b4-d426-487d-91fd-92baeb3e1556", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "cc806d4c-c697-48e4-89e3-129958131725", "metadata": {}, "source": ["Annotations"]}, {"cell_type": "code", "execution_count": 54, "id": "98589797", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "annotationsdf = pd.DataFrame(coco_data['annotations'])\n"]}, {"cell_type": "code", "execution_count": 55, "id": "9ab9d2b2-f241-4195-a1b0-99e7e1ff6688", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>[]</td>\n", "      <td>1384.146154</td>\n", "      <td>[6360.960244683219, 625.1656233772903, 54.6520...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>2535.911877</td>\n", "      <td>[193.9164645864987, 397.2349395771913, 39.9585...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>1178.689536</td>\n", "      <td>[26.1632, 457.3888, -25.2288, 46.72]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>7721.217171</td>\n", "      <td>[594.6750637808044, 2266.2291017559783, 135.74...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>15387.282791</td>\n", "      <td>[771.7848110372613, 2336.038783156333, 127.984...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1            7           []   1384.146154   \n", "1   2         2           23           []   2535.911877   \n", "2   3         2           23           []   1178.689536   \n", "3   4         3           15           []   7721.217171   \n", "4   5         3           15           []  15387.282791   \n", "\n", "                                                bbox  iscrowd  \n", "0  [6360.960244683219, 625.1656233772903, 54.6520...        0  \n", "1  [193.9164645864987, 397.2349395771913, 39.9585...        0  \n", "2               [26.1632, 457.3888, -25.2288, 46.72]        0  \n", "3  [594.6750637808044, 2266.2291017559783, 135.74...        0  \n", "4  [771.7848110372613, 2336.038783156333, 127.984...        0  "]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 56, "id": "e4193fb0-f31a-4583-aee1-5466a6d9ff32", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>813</th>\n", "      <td>814</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140119_M0803345.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>817</th>\n", "      <td>818</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140128_M0803349.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>815</th>\n", "      <td>816</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140130_M0803350.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>821</th>\n", "      <td>822</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140132_M0803351.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>816</th>\n", "      <td>817</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140136_M0803353.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "813  814   7008    4672  KES22_WOT-L_20220923B-140119_M0803345.jpg        1   \n", "817  818   7008    4672  KES22_WOT-L_20220923B-140128_M0803349.jpg        1   \n", "815  816   7008    4672  KES22_WOT-L_20220923B-140130_M0803350.jpg        1   \n", "821  822   7008    4672  KES22_WOT-L_20220923B-140132_M0803351.jpg        1   \n", "816  817   7008    4672  KES22_WOT-L_20220923B-140136_M0803353.jpg        1   \n", "\n", "    date_captured  \n", "813    2022-09-23  \n", "817    2022-09-23  \n", "815    2022-09-23  \n", "821    2022-09-23  \n", "816    2022-09-23  "]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25leftsorted.head()"]}, {"cell_type": "code", "execution_count": 57, "id": "a8ad9f17-01f2-48aa-b76f-0f35d3009a2a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145132_M0804846.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145128_M0804844.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-145428_M0804934.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>891</th>\n", "      <td>892</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-141001_M0803605.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>892</th>\n", "      <td>893</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-141035_M0803622.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>893</th>\n", "      <td>894</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-141011_M0803610.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>894</th>\n", "      <td>895</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-141100_M0803634.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>895</th>\n", "      <td>896</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140921_M0803585.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>445 rows × 6 columns</p>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "0      1   7008    4672  KES22_WOT-L_20220923B-145132_M0804846.jpg        1   \n", "1      2   7008    4672  KES22_WOT-L_20220923B-145454_M0804947.jpg        1   \n", "2      3   7008    4672  KES22_WOT-L_20220923B-145256_M0804888.jpg        1   \n", "3      4   7008    4672  KES22_WOT-L_20220923B-145128_M0804844.jpg        1   \n", "4      5   7008    4672  KES22_WOT-L_20220923B-145428_M0804934.jpg        1   \n", "..   ...    ...     ...                                        ...      ...   \n", "891  892   7008    4672  KES22_WOT-L_20220923B-141001_M0803605.jpg        1   \n", "892  893   7008    4672  KES22_WOT-L_20220923B-141035_M0803622.jpg        1   \n", "893  894   7008    4672  KES22_WOT-L_20220923B-141011_M0803610.jpg        1   \n", "894  895   7008    4672  KES22_WOT-L_20220923B-141100_M0803634.jpg        1   \n", "895  896   7008    4672  KES22_WOT-L_20220923B-140921_M0803585.jpg        1   \n", "\n", "    date_captured  \n", "0      2022-09-23  \n", "1      2022-09-23  \n", "2      2022-09-23  \n", "3      2022-09-23  \n", "4      2022-09-23  \n", "..            ...  \n", "891    2022-09-23  \n", "892    2022-09-23  \n", "893    2022-09-23  \n", "894    2022-09-23  \n", "895    2022-09-23  \n", "\n", "[445 rows x 6 columns]"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["NG25leftsorted.sort_values(by=[\"id\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 58, "id": "fd7c2b81-3bad-49b8-98c7-9bd405abc281", "metadata": {}, "outputs": [], "source": ["# Compare image 'ID' columns and return annotations \n", "result_values = annotationsdf[annotationsdf['image_id'].isin(NG25leftsorted['id'])]"]}, {"cell_type": "code", "execution_count": 59, "id": "dc2d2883-f90f-4b14-8e97-23bed48462ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3075, 7)"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values.shape"]}, {"cell_type": "code", "execution_count": 60, "id": "a013ab74-3c01-47b8-8c87-83c9ddd2ac7c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7142, 7)"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.shape"]}, {"cell_type": "code", "execution_count": 61, "id": "c57c8809-7d45-470d-8e25-4f583132f5b7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>[]</td>\n", "      <td>1384.146154</td>\n", "      <td>[6360.960244683219, 625.1656233772903, 54.6520...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>2535.911877</td>\n", "      <td>[193.9164645864987, 397.2349395771913, 39.9585...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>1178.689536</td>\n", "      <td>[26.1632, 457.3888, -25.2288, 46.72]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>7721.217171</td>\n", "      <td>[594.6750637808044, 2266.2291017559783, 135.74...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>15387.282791</td>\n", "      <td>[771.7848110372613, 2336.038783156333, 127.984...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1            7           []   1384.146154   \n", "1   2         2           23           []   2535.911877   \n", "2   3         2           23           []   1178.689536   \n", "3   4         3           15           []   7721.217171   \n", "4   5         3           15           []  15387.282791   \n", "\n", "                                                bbox  iscrowd  \n", "0  [6360.960244683219, 625.1656233772903, 54.6520...        0  \n", "1  [193.9164645864987, 397.2349395771913, 39.9585...        0  \n", "2               [26.1632, 457.3888, -25.2288, 46.72]        0  \n", "3  [594.6750637808044, 2266.2291017559783, 135.74...        0  \n", "4  [771.7848110372613, 2336.038783156333, 127.984...        0  "]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values.head()"]}, {"cell_type": "code", "execution_count": 62, "id": "c8e87cea-2d9f-4ee4-b71f-1eaaced692da", "metadata": {}, "outputs": [], "source": ["result_values.to_csv(\"NG25leftannotations.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "431bd5e3-19b8-4473-b5f2-07460645bd12", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 63, "id": "2e2b725d-2c17-4191-87d6-b76cd869ca62", "metadata": {}, "outputs": [], "source": ["# Compare image 'ID' columns and return annotations \n", "result_valuesright = annotationsdf[annotationsdf['image_id'].isin(NG25rightsorted['id'])]"]}, {"cell_type": "code", "execution_count": 64, "id": "438e2ee3-45d1-4789-b428-c18564fd830f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4067, 7)"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["result_valuesright.shape"]}, {"cell_type": "code", "execution_count": 65, "id": "329c5336-6af2-4697-8242-d9471608b45f", "metadata": {}, "outputs": [], "source": ["result_valuesright.to_csv(\"NG25rightannotations.csv\",index=False)"]}, {"cell_type": "markdown", "id": "368d46a6-a4bf-443e-b5ee-4e21fd1d5a89", "metadata": {}, "source": ["### NG25 left COCO file"]}, {"cell_type": "code", "execution_count": 66, "id": "e844f260-cf39-4a71-9c92-22e761e93057", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>[]</td>\n", "      <td>1384.146154</td>\n", "      <td>[6360.960244683219, 625.1656233772903, 54.6520...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>2535.911877</td>\n", "      <td>[193.9164645864987, 397.2349395771913, 39.9585...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>1178.689536</td>\n", "      <td>[26.1632, 457.3888, -25.2288, 46.72]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>7721.217171</td>\n", "      <td>[594.6750637808044, 2266.2291017559783, 135.74...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>15387.282791</td>\n", "      <td>[771.7848110372613, 2336.038783156333, 127.984...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1            7           []   1384.146154   \n", "1   2         2           23           []   2535.911877   \n", "2   3         2           23           []   1178.689536   \n", "3   4         3           15           []   7721.217171   \n", "4   5         3           15           []  15387.282791   \n", "\n", "                                                bbox  iscrowd  \n", "0  [6360.960244683219, 625.1656233772903, 54.6520...        0  \n", "1  [193.9164645864987, 397.2349395771913, 39.9585...        0  \n", "2               [26.1632, 457.3888, -25.2288, 46.72]        0  \n", "3  [594.6750637808044, 2266.2291017559783, 135.74...        0  \n", "4  [771.7848110372613, 2336.038783156333, 127.984...        0  "]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25leftanno = pd.read_csv(\"NG25leftannotations.csv\")\n", "ng25leftanno.head()"]}, {"cell_type": "code", "execution_count": 67, "id": "c8281f42-a5ea-4e4f-beaf-0d88136baa5e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>814</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140119_M0803345.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>818</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140128_M0803349.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>816</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140130_M0803350.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>822</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140132_M0803351.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>817</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220923B-140136_M0803353.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0  814   7008    4672  KES22_WOT-L_20220923B-140119_M0803345.jpg        1   \n", "1  818   7008    4672  KES22_WOT-L_20220923B-140128_M0803349.jpg        1   \n", "2  816   7008    4672  KES22_WOT-L_20220923B-140130_M0803350.jpg        1   \n", "3  822   7008    4672  KES22_WOT-L_20220923B-140132_M0803351.jpg        1   \n", "4  817   7008    4672  KES22_WOT-L_20220923B-140136_M0803353.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25leftimages = pd.read_csv(\"NG25leftimagespos.csv\")\n", "ng25leftimages.head()"]}, {"cell_type": "code", "execution_count": 68, "id": "bc5b969d-9351-4fad-83cb-27aec08851b4", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = ng25leftimages.to_dict(orient='records')\n", "coco_dict['annotations'] = ng25leftanno.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'ng25leftannotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": 32, "id": "25d27e8d-9b16-49d6-80ad-d0457b4526a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'id': 1,\n", "  'name': 'KAZA Elephant Survey',\n", "  'url': 'https://www.kavangozambezi.org/kaza-elephant-survey/'}]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["coco_data[\"licenses\"]"]}, {"cell_type": "code", "execution_count": 69, "id": "36226de4-c23f-45dd-8d40-92c76b860f57", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>113</td>\n", "      <td>55</td>\n", "      <td>10</td>\n", "      <td>[]</td>\n", "      <td>21093.993868</td>\n", "      <td>[2373.520808744291, 533.7582679951277, 174.133...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>114</td>\n", "      <td>55</td>\n", "      <td>10</td>\n", "      <td>[]</td>\n", "      <td>13989.817605</td>\n", "      <td>[2742.608972783476, 490.22479223665977, 104.10...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>115</td>\n", "      <td>55</td>\n", "      <td>10</td>\n", "      <td>[]</td>\n", "      <td>42707.455996</td>\n", "      <td>[6270.713269034957, 2716.110335365277, 247.951...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>116</td>\n", "      <td>56</td>\n", "      <td>17</td>\n", "      <td>[]</td>\n", "      <td>3510.608568</td>\n", "      <td>[712.4494732228375, 2818.5126864823624, 39.817...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>117</td>\n", "      <td>56</td>\n", "      <td>17</td>\n", "      <td>[]</td>\n", "      <td>3549.031128</td>\n", "      <td>[851.8108472265061, 2259.645135630916, 63.9924...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  image_id  category_id segmentation          area  \\\n", "0  113        55           10           []  21093.993868   \n", "1  114        55           10           []  13989.817605   \n", "2  115        55           10           []  42707.455996   \n", "3  116        56           17           []   3510.608568   \n", "4  117        56           17           []   3549.031128   \n", "\n", "                                                bbox  iscrowd  \n", "0  [2373.520808744291, 533.7582679951277, 174.133...        0  \n", "1  [2742.608972783476, 490.22479223665977, 104.10...        0  \n", "2  [6270.713269034957, 2716.110335365277, 247.951...        0  \n", "3  [712.4494732228375, 2818.5126864823624, 39.817...        0  \n", "4  [851.8108472265061, 2259.645135630916, 63.9924...        0  "]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25rightanno = pd.read_csv(\"NG25rightannotations.csv\")\n", "ng25rightanno.head()"]}, {"cell_type": "code", "execution_count": 70, "id": "a55bfcb9-13ee-4468-bb3e-31c973044267", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>622</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140021_M0702364.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>629</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140023_M0702365.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>633</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140025_M0702366.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>624</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140027_M0702367.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>620</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-R_20220923B-140031_M0702369.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0  622   7008    4672  KES22_WOT-R_20220923B-140021_M0702364.jpg        1   \n", "1  629   7008    4672  KES22_WOT-R_20220923B-140023_M0702365.jpg        1   \n", "2  633   7008    4672  KES22_WOT-R_20220923B-140025_M0702366.jpg        1   \n", "3  624   7008    4672  KES22_WOT-R_20220923B-140027_M0702367.jpg        1   \n", "4  620   7008    4672  KES22_WOT-R_20220923B-140031_M0702369.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25rightimages = pd.read_csv(\"NG25rightimagespos.csv\")\n", "ng25rightimages.head()"]}, {"cell_type": "code", "execution_count": 71, "id": "6df4b37a-be3d-44d3-a10a-b92fe5a7ec73", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dictright = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dictright['images'] = ng25rightimages.to_dict(orient='records')\n", "coco_dictright['annotations'] = ng25rightanno.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'ng25rightannotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dictright, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "id": "254708b5-3db5-489a-bc35-918c449acee8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16dd420b-4f4d-42de-9aa8-1917a1d7a760", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "043cfaf2-580a-4a72-9871-0e8bbad31a7f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}