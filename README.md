# MWSLab-2023

## _datasets

This will be where CSV and COCO annotation data are lodged. 

These are being processed and organised from the `scoutexports` folder.

## imagereview

Scripts for rapidly reviewing bounding boxes from Scout output.

## labanalysis

Mostly jupyter notebooks, ongoing analysis of scout outputs (CSV and MongoDB queries).

## utils

Utility scripts

* scout2coco.py - converts an exported scout CSV to COCO export format.
* clean_up_images.py - [ToDo] check MongoDB list of files and delete unlisted files from .scout-hidden

## Training

The `index.html` in this folder can be opened and it will show a list of species, thumbnails for the selected species and full-size example images when thumbnails clicked.

To update:
* Add new images to the `images/` folder with the format NAME_SOURCE_INDEX.jpg where NAME is a dash-separated common name, SOURCE is *a* for aerial, *g* for ground image, and INDEX an integer (to make filenames unique).
* Run `python3 imagelistgen.py` to generate `image_list.txt`
* Replace the imagelist array in `scripts.js` with the contents of `image_list.txt`
* Reload web page. Check the console for errors. 
