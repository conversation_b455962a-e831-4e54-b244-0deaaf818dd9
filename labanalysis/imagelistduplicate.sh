#!/bin/bash

# Directory containing the files you want to list
directory="/nas/.scout-hidden/images/"

# CSV file to store the list of files
csv_file="imagesduplicates.csv"

# Check if the directory exists
if [ ! -d "$directory" ]; then
    echo "Directory does not exist: $directory"
    exit 1
fi

# Initialize the CSV file
echo "File Name" > "$csv_file"

# Iterate through the files in the directory and append them to the CSV file
for file in "$directory"/*; do
    # Check if it's a file (not a directory)
    if [ -f "$file" ]; then
        # Use basename to get just the file name without the full path
        echo "$(basename "$file")" >> "$csv_file"
    fi
done

echo "File list saved to $csv_file"
