{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7b85b79c", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "dbd877c9", "metadata": {}, "outputs": [], "source": ["# read csv files in directory\n", "import os\n", "filename = os"]}, {"cell_type": "markdown", "id": "26efa5cd", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 2, "id": "8b5e42e3", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KAZA_Left2</td>\n", "      <td>651d568bb5c3e0004614dbf8</td>\n", "      <td>KES22_IIM-L_20220831A-080606_M0307790.jpg</td>\n", "      <td>651d385d99f002004b039c41</td>\n", "      <td>276.653182</td>\n", "      <td>115.056697</td>\n", "      <td>107.300066</td>\n", "      <td>84.030172</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696491628045</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KAZA_Left2</td>\n", "      <td>651d568bb5c3e0004614dbf8</td>\n", "      <td>KES22_IIM-L_20220831A-080630_M0307802.jpg</td>\n", "      <td>651d38d599f002004b039c6d</td>\n", "      <td>4226.060549</td>\n", "      <td>2047.907374</td>\n", "      <td>149.644111</td>\n", "      <td>196.754294</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696493284665</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KAZA_Left2</td>\n", "      <td>651d568bb5c3e0004614dbf8</td>\n", "      <td>KES22_IIM-L_20220831A-080632_M0307803.jpg</td>\n", "      <td>651d38e099f002004b039c71</td>\n", "      <td>759.028787</td>\n", "      <td>1502.816031</td>\n", "      <td>140.222185</td>\n", "      <td>185.946811</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696493392772</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KAZA_Left2</td>\n", "      <td>651d568bb5c3e0004614dbf8</td>\n", "      <td>KES22_IIM-L_20220831A-080712_M0307823.jpg</td>\n", "      <td>651d39aa99f002004b039cbb</td>\n", "      <td>5731.112967</td>\n", "      <td>1122.364992</td>\n", "      <td>57.587314</td>\n", "      <td>96.370607</td>\n", "      <td>impala</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696497726892</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KAZA_Left2</td>\n", "      <td>651d568bb5c3e0004614dbf8</td>\n", "      <td>KES22_IIM-L_20220831A-080714_M0307824.jpg</td>\n", "      <td>651d3bdd99f002004b039d86</td>\n", "      <td>2277.305387</td>\n", "      <td>2033.174831</td>\n", "      <td>80.130380</td>\n", "      <td>85.472405</td>\n", "      <td>impala</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696497977464</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Task Name                   Task ID  \\\n", "0  KAZA_Left2  651d568bb5c3e0004614dbf8   \n", "1  KAZA_Left2  651d568bb5c3e0004614dbf8   \n", "2  KAZA_Left2  651d568bb5c3e0004614dbf8   \n", "3  KAZA_Left2  651d568bb5c3e0004614dbf8   \n", "4  KAZA_Left2  651d568bb5c3e0004614dbf8   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_IIM-L_20220831A-080606_M0307790.jpg  651d385d99f002004b039c41   \n", "1  KES22_IIM-L_20220831A-080630_M0307802.jpg  651d38d599f002004b039c6d   \n", "2  KES22_IIM-L_20220831A-080632_M0307803.jpg  651d38e099f002004b039c71   \n", "3  KES22_IIM-L_20220831A-080712_M0307823.jpg  651d39aa99f002004b039cbb   \n", "4  KES22_IIM-L_20220831A-080714_M0307824.jpg  651d3bdd99f002004b039d86   \n", "\n", "         Box X        Box Y       Box W       Box H     Label  \\\n", "0   276.653182   115.056697  107.300066   84.030172  elephant   \n", "1  4226.060549  2047.907374  149.644111  196.754294  elephant   \n", "2   759.028787  1502.816031  140.222185  185.946811  elephant   \n", "3  5731.112967  1122.364992   57.587314   96.370607    impala   \n", "4  2277.305387  2033.174831   80.130380   85.472405    impala   \n", "\n", "   Label Confidence    Assignee      Timestamp  Is Ground Truth  \\\n", "0               NaN  annotator2  1696491628045            False   \n", "1               NaN  annotator2  1696493284665            False   \n", "2               NaN  annotator2  1696493392772            False   \n", "3               NaN  annotator2  1696497726892            False   \n", "4               NaN  annotator2  1696497977464            False   \n", "\n", "   Excluded By Line  \n", "0               NaN  \n", "1               NaN  \n", "2               NaN  \n", "3               NaN  \n", "4               NaN  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["anno = pd.read_csv(\"Pregroundtruth/NGAM17_L_annotations1-454_annotator2.csv\")\n", "anno.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "6c898b93", "metadata": {}, "outputs": [{"data": {"text/plain": ["'KAZA_Left2'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["anno[\"Task Name\"][0]"]}, {"cell_type": "code", "execution_count": 3, "id": "6e71bffe", "metadata": {}, "outputs": [{"data": {"text/plain": ["73"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["anno[\"Label\"].count()"]}, {"cell_type": "code", "execution_count": 4, "id": "e2158d4a", "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["anno[\"Label\"].nunique()"]}, {"cell_type": "code", "execution_count": 5, "id": "fc18c5d4", "metadata": {}, "outputs": [{"data": {"text/plain": ["impala      40\n", "elephant    29\n", "zebra        3\n", "giraffe      1\n", "Name: Label, dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["anno[\"Label\"].value_counts()"]}, {"cell_type": "markdown", "id": "57388131", "metadata": {}, "source": ["## Pendo"]}, {"cell_type": "code", "execution_count": 6, "id": "3d8f38dd", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KAZA_Left</td>\n", "      <td>651d5336b5c3e0004614dbf6</td>\n", "      <td>KES22_IIM-L_20220831A-080606_M0307790.jpg</td>\n", "      <td>651d385d99f002004b039c41</td>\n", "      <td>290.942262</td>\n", "      <td>129.798217</td>\n", "      <td>69.755504</td>\n", "      <td>56.510788</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1696489888880</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KAZA_Left</td>\n", "      <td>651d5336b5c3e0004614dbf6</td>\n", "      <td>KES22_IIM-L_20220831A-080630_M0307802.jpg</td>\n", "      <td>651d38d599f002004b039c6d</td>\n", "      <td>4221.605948</td>\n", "      <td>2055.476129</td>\n", "      <td>201.188528</td>\n", "      <td>184.422817</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1696491307841</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KAZA_Left</td>\n", "      <td>651d5336b5c3e0004614dbf6</td>\n", "      <td>KES22_IIM-L_20220831A-080632_M0307803.jpg</td>\n", "      <td>651d38e099f002004b039c71</td>\n", "      <td>764.847680</td>\n", "      <td>1521.381797</td>\n", "      <td>182.898358</td>\n", "      <td>141.330549</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1696491536521</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KAZA_Left</td>\n", "      <td>651d5336b5c3e0004614dbf6</td>\n", "      <td>KES22_IIM-L_20220831A-080714_M0307824.jpg</td>\n", "      <td>651d3bdd99f002004b039d86</td>\n", "      <td>2282.972800</td>\n", "      <td>2037.926400</td>\n", "      <td>64.006400</td>\n", "      <td>70.547200</td>\n", "      <td>impala</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1696494246006</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KAZA_Left</td>\n", "      <td>651d5336b5c3e0004614dbf6</td>\n", "      <td>KES22_IIM-L_20220831A-080728_M0307831.jpg</td>\n", "      <td>651d3bb199f002004b039d76</td>\n", "      <td>3783.982869</td>\n", "      <td>2554.689507</td>\n", "      <td>89.421842</td>\n", "      <td>69.379015</td>\n", "      <td>impala</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1696497499979</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Task Name                   Task ID  \\\n", "0  KAZA_Left  651d5336b5c3e0004614dbf6   \n", "1  KAZA_Left  651d5336b5c3e0004614dbf6   \n", "2  KAZA_Left  651d5336b5c3e0004614dbf6   \n", "3  KAZA_Left  651d5336b5c3e0004614dbf6   \n", "4  KAZA_Left  651d5336b5c3e0004614dbf6   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_IIM-L_20220831A-080606_M0307790.jpg  651d385d99f002004b039c41   \n", "1  KES22_IIM-L_20220831A-080630_M0307802.jpg  651d38d599f002004b039c6d   \n", "2  KES22_IIM-L_20220831A-080632_M0307803.jpg  651d38e099f002004b039c71   \n", "3  KES22_IIM-L_20220831A-080714_M0307824.jpg  651d3bdd99f002004b039d86   \n", "4  KES22_IIM-L_20220831A-080728_M0307831.jpg  651d3bb199f002004b039d76   \n", "\n", "         Box X        Box Y       Box W       Box H     Label  \\\n", "0   290.942262   129.798217   69.755504   56.510788  elephant   \n", "1  4221.605948  2055.476129  201.188528  184.422817  elephant   \n", "2   764.847680  1521.381797  182.898358  141.330549  elephant   \n", "3  2282.972800  2037.926400   64.006400   70.547200    impala   \n", "4  3783.982869  2554.689507   89.421842   69.379015    impala   \n", "\n", "   Label Confidence    Assignee      Timestamp  Is Ground Truth  \\\n", "0               NaN  annotator1  1696489888880            False   \n", "1               NaN  annotator1  1696491307841            False   \n", "2               NaN  annotator1  1696491536521            False   \n", "3               NaN  annotator1  1696494246006            False   \n", "4               NaN  annotator1  1696497499979            False   \n", "\n", "   Excluded By Line  \n", "0               NaN  \n", "1               NaN  \n", "2               NaN  \n", "3               NaN  \n", "4               NaN  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["annop = pd.read_csv(\"Pregroundtruth/NGAM17_L_annotations1-454_annotator1.csv\")\n", "annop.head()"]}, {"cell_type": "code", "execution_count": 7, "id": "f400a864", "metadata": {}, "outputs": [{"data": {"text/plain": ["133"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["annop[\"Label\"].count()"]}, {"cell_type": "code", "execution_count": 8, "id": "61c9d551", "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["annop[\"Label\"].nunique()"]}, {"cell_type": "code", "execution_count": 9, "id": "6c3ca2b4", "metadata": {}, "outputs": [{"data": {"text/plain": ["impala      64\n", "elephant    58\n", "zebra        9\n", "duiker       1\n", "giraffe      1\n", "Name: Label, dtype: int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["annop[\"Label\"].value_counts()"]}, {"cell_type": "markdown", "id": "1b09aa29", "metadata": {}, "source": ["### NGAM17_R 1-100"]}, {"cell_type": "code", "execution_count": 10, "id": "7e25535b", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NGAM17-R_1-100</td>\n", "      <td>6523b6ef18f3b3004425b07a</td>\n", "      <td>KES22_IIM-R_20220831A-080451_KES22_IIM-R_20220...</td>\n", "      <td>651d467da368a6004cf098ec</td>\n", "      <td>6713</td>\n", "      <td>4272</td>\n", "      <td>33</td>\n", "      <td>41</td>\n", "      <td>hippo</td>\n", "      <td>0.7082</td>\n", "      <td>ML Config: V2 Classifier</td>\n", "      <td>1696839436847</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NGAM17-R_1-100</td>\n", "      <td>6523b6ef18f3b3004425b07a</td>\n", "      <td>KES22_IIM-R_20220831A-080451_KES22_IIM-R_20220...</td>\n", "      <td>651d467da368a6004cf098ec</td>\n", "      <td>6415</td>\n", "      <td>4372</td>\n", "      <td>30</td>\n", "      <td>50</td>\n", "      <td>hippo</td>\n", "      <td>0.6580</td>\n", "      <td>ML Config: V2 Classifier</td>\n", "      <td>1696839436847</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NGAM17-R_1-100</td>\n", "      <td>6523b6ef18f3b3004425b07a</td>\n", "      <td>KES22_IIM-R_20220831A-080451_KES22_IIM-R_20220...</td>\n", "      <td>651d467da368a6004cf098ec</td>\n", "      <td>5441</td>\n", "      <td>2228</td>\n", "      <td>30</td>\n", "      <td>60</td>\n", "      <td>hippo</td>\n", "      <td>0.6155</td>\n", "      <td>ML Config: V2 Classifier</td>\n", "      <td>1696839436847</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NGAM17-R_1-100</td>\n", "      <td>6523b6ef18f3b3004425b07a</td>\n", "      <td>KES22_IIM-R_20220831A-080451_KES22_IIM-R_20220...</td>\n", "      <td>651d467da368a6004cf098ec</td>\n", "      <td>3360</td>\n", "      <td>1885</td>\n", "      <td>17</td>\n", "      <td>34</td>\n", "      <td>goat</td>\n", "      <td>0.5051</td>\n", "      <td>ML Config: V2 Classifier</td>\n", "      <td>1696839436847</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NGAM17-R_1-100</td>\n", "      <td>6523b6ef18f3b3004425b07a</td>\n", "      <td>KES22_IIM-R_20220831A-080451_KES22_IIM-R_20220...</td>\n", "      <td>651d467da368a6004cf098ec</td>\n", "      <td>6648</td>\n", "      <td>630</td>\n", "      <td>33</td>\n", "      <td>36</td>\n", "      <td>goat</td>\n", "      <td>0.4731</td>\n", "      <td>ML Config: V2 Classifier</td>\n", "      <td>1696839436847</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        Task Name                   Task ID  \\\n", "0  NGAM17-R_1-100  6523b6ef18f3b3004425b07a   \n", "1  NGAM17-R_1-100  6523b6ef18f3b3004425b07a   \n", "2  NGAM17-R_1-100  6523b6ef18f3b3004425b07a   \n", "3  NGAM17-R_1-100  6523b6ef18f3b3004425b07a   \n", "4  NGAM17-R_1-100  6523b6ef18f3b3004425b07a   \n", "\n", "                                      Image Filename  \\\n", "0  KES22_IIM-R_20220831A-080451_KES22_IIM-R_20220...   \n", "1  KES22_IIM-R_20220831A-080451_KES22_IIM-R_20220...   \n", "2  KES22_IIM-R_20220831A-080451_KES22_IIM-R_20220...   \n", "3  KES22_IIM-R_20220831A-080451_KES22_IIM-R_20220...   \n", "4  KES22_IIM-R_20220831A-080451_KES22_IIM-R_20220...   \n", "\n", "                   Image ID  Box X  Box Y  Box W  Box H  Label  \\\n", "0  651d467da368a6004cf098ec   6713   4272     33     41  hippo   \n", "1  651d467da368a6004cf098ec   6415   4372     30     50  hippo   \n", "2  651d467da368a6004cf098ec   5441   2228     30     60  hippo   \n", "3  651d467da368a6004cf098ec   3360   1885     17     34   goat   \n", "4  651d467da368a6004cf098ec   6648    630     33     36   goat   \n", "\n", "   Label Confidence                  Assignee      Timestamp  Is Ground Truth  \\\n", "0            0.7082  ML Config: V2 Classifier  1696839436847            False   \n", "1            0.6580  ML Config: V2 Classifier  1696839436847            False   \n", "2            0.6155  ML Config: V2 Classifier  1696839436847            False   \n", "3            0.5051  ML Config: V2 Classifier  1696839436847            False   \n", "4            0.4731  ML Config: V2 Classifier  1696839436847            False   \n", "\n", "   Excluded By Line  \n", "0               NaN  \n", "1               NaN  \n", "2               NaN  \n", "3               NaN  \n", "4               NaN  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["annoMLV2 = pd.read_csv(\"Pregroundtruth/NGAM17_R_1-100_MLV2annotations.csv\")\n", "annoMLV2.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "bb5cfe53", "metadata": {}, "outputs": [{"data": {"text/plain": ["1208"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["annoMLV2[\"Label\"].count()"]}, {"cell_type": "code", "execution_count": 12, "id": "b15dbef8", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["hippo          557\n", "goat           223\n", "giraffe        103\n", "kob             98\n", "warthog         64\n", "cow             52\n", "roof_grass      31\n", "buffalo         27\n", "waterbuck       25\n", "canoe           16\n", "elephant         6\n", "roof_mabati      4\n", "hartebeest       1\n", "car              1\n", "Name: Label, dtype: int64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["annoMLV2[\"Label\"].value_counts()"]}, {"cell_type": "code", "execution_count": 8, "id": "23b98c9a", "metadata": {}, "outputs": [], "source": ["## MLV1\n", "annoV1 = pd.read_csv(\"Pregroundtruth/NG25-04-R_1440_2annotations.csv\")\n", "#annoV1.head()"]}, {"cell_type": "code", "execution_count": 9, "id": "a0a3fe67", "metadata": {}, "outputs": [{"data": {"text/plain": ["76"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["annoV1[\"Label\"].count()"]}, {"cell_type": "code", "execution_count": 10, "id": "30bcc0d6", "metadata": {}, "outputs": [{"data": {"text/plain": ["giraffe    48\n", "zebra      25\n", "warthog     3\n", "Name: Label, dtype: int64"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["annoV1[\"Label\"].value_counts()"]}, {"cell_type": "markdown", "id": "3712d666", "metadata": {}, "source": ["### NGAM17_R 176-275"]}, {"cell_type": "code", "execution_count": 5, "id": "b548fe21", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NGAM17_R_76-175_John</td>\n", "      <td>6523bc3818f3b3004425b088</td>\n", "      <td>KES22_IIM-R_20220831A-081006_KES22_IIM-R_20220...</td>\n", "      <td>651d4688a368a6004cf098f0</td>\n", "      <td>572.332603</td>\n", "      <td>754.547891</td>\n", "      <td>61.808710</td>\n", "      <td>59.400579</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696843573524</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NGAM17_R_76-175_John</td>\n", "      <td>6523bc3818f3b3004425b088</td>\n", "      <td>KES22_IIM-R_20220831A-081006_KES22_IIM-R_20220...</td>\n", "      <td>651d4688a368a6004cf098f0</td>\n", "      <td>817.159312</td>\n", "      <td>646.984681</td>\n", "      <td>-83.481894</td>\n", "      <td>77.060210</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696843573524</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NGAM17_R_76-175_John</td>\n", "      <td>6523bc3818f3b3004425b088</td>\n", "      <td>KES22_IIM-R_20220831A-081006_KES22_IIM-R_20220...</td>\n", "      <td>651d4688a368a6004cf098f0</td>\n", "      <td>921.511680</td>\n", "      <td>581.162418</td>\n", "      <td>65.822263</td>\n", "      <td>48.162631</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696843573524</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NGAM17_R_76-175_John</td>\n", "      <td>6523bc3818f3b3004425b088</td>\n", "      <td>KES22_IIM-R_20220831A-081006_KES22_IIM-R_20220...</td>\n", "      <td>651d4688a368a6004cf098f0</td>\n", "      <td>1102.121547</td>\n", "      <td>651.800944</td>\n", "      <td>49.768052</td>\n", "      <td>79.468342</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696843573524</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NGAM17_R_76-175_John</td>\n", "      <td>6523bc3818f3b3004425b088</td>\n", "      <td>KES22_IIM-R_20220831A-081006_KES22_IIM-R_20220...</td>\n", "      <td>651d4688a368a6004cf098f0</td>\n", "      <td>1294.772073</td>\n", "      <td>386.103761</td>\n", "      <td>55.387026</td>\n", "      <td>67.427684</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696843573524</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              Task Name                   Task ID  \\\n", "0  NGAM17_R_76-175_<PERSON>  6523bc3818f3b3004425b088   \n", "1  NGAM17_R_76-175_<PERSON>  6523bc3818f3b3004425b088   \n", "2  NGAM17_R_76-175_<PERSON>  6523bc3818f3b3004425b088   \n", "3  NGAM17_R_76-175_<PERSON>  6523bc3818f3b3004425b088   \n", "4  NGAM17_R_76-175_<PERSON>  6523bc3818f3b3004425b088   \n", "\n", "                                      Image Filename  \\\n", "0  KES22_IIM-R_20220831A-081006_KES22_IIM-R_20220...   \n", "1  KES22_IIM-R_20220831A-081006_KES22_IIM-R_20220...   \n", "2  KES22_IIM-R_20220831A-081006_KES22_IIM-R_20220...   \n", "3  KES22_IIM-R_20220831A-081006_KES22_IIM-R_20220...   \n", "4  KES22_IIM-R_20220831A-081006_KES22_IIM-R_20220...   \n", "\n", "                   Image ID        Box X       Box Y      Box W      Box H  \\\n", "0  651d4688a368a6004cf098f0   572.332603  754.547891  61.808710  59.400579   \n", "1  651d4688a368a6004cf098f0   817.159312  646.984681 -83.481894  77.060210   \n", "2  651d4688a368a6004cf098f0   921.511680  581.162418  65.822263  48.162631   \n", "3  651d4688a368a6004cf098f0  1102.121547  651.800944  49.768052  79.468342   \n", "4  651d4688a368a6004cf098f0  1294.772073  386.103761  55.387026  67.427684   \n", "\n", "      Label  Label Confidence    Assignee      Timestamp  Is Ground Truth  \\\n", "0  elephant               NaN  annotator2  1696843573524            False   \n", "1  elephant               NaN  annotator2  1696843573524            False   \n", "2  elephant               NaN  annotator2  1696843573524            False   \n", "3  elephant               NaN  annotator2  1696843573524            False   \n", "4  elephant               NaN  annotator2  1696843573524            False   \n", "\n", "   Excluded By Line  \n", "0               NaN  \n", "1               NaN  \n", "2               NaN  \n", "3               NaN  \n", "4               NaN  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["anno = pd.read_csv(\"Pregroundtruth/NGAM17_R_76-175annotations.csv\")\n", "anno.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "2fb10b4f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["24\n"]}], "source": ["tot= anno[\"Label\"].count()\n", "print(tot)"]}, {"cell_type": "code", "execution_count": 7, "id": "dd88c193", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["elephant    24\n", "Name: Label, dtype: int64\n"]}], "source": ["animals =anno[\"Label\"].value_counts()\n", "print(animals)"]}, {"cell_type": "code", "execution_count": 4, "id": "dc876322", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-03-L_109</td>\n", "      <td>652ce2a780b3720044fe07da</td>\n", "      <td>NG25-03_WOT-L_20220923B-141524_M0803766.jpg</td>\n", "      <td>652cdebc2bd4eb004b0f2614</td>\n", "      <td>5655.512086</td>\n", "      <td>4379.928897</td>\n", "      <td>58.304248</td>\n", "      <td>27.019042</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1697440748362</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-03-L_109</td>\n", "      <td>652ce2a780b3720044fe07da</td>\n", "      <td>NG25-03_WOT-L_20220923B-141643_M0803805.jpg</td>\n", "      <td>652cdf1a2bd4eb004b0f2636</td>\n", "      <td>6751.818722</td>\n", "      <td>660.491231</td>\n", "      <td>-61.113068</td>\n", "      <td>51.711057</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1697441058935</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-03-L_109</td>\n", "      <td>652ce2a780b3720044fe07da</td>\n", "      <td>NG25-03_WOT-L_20220923B-141643_M0803805.jpg</td>\n", "      <td>652cdf1a2bd4eb004b0f2636</td>\n", "      <td>6683.654146</td>\n", "      <td>562.945373</td>\n", "      <td>56.412062</td>\n", "      <td>51.711057</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1697441058935</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-03-L_109</td>\n", "      <td>652ce2a780b3720044fe07da</td>\n", "      <td>NG25-03_WOT-L_20220923B-141643_M0803805.jpg</td>\n", "      <td>652cdf1a2bd4eb004b0f2636</td>\n", "      <td>6594.335047</td>\n", "      <td>574.697886</td>\n", "      <td>-71.690329</td>\n", "      <td>-66.989324</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1697441058935</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-03-L_109</td>\n", "      <td>652ce2a780b3720044fe07da</td>\n", "      <td>NG25-03_WOT-L_20220923B-141643_M0803805.jpg</td>\n", "      <td>652cdf1a2bd4eb004b0f2636</td>\n", "      <td>6493.263435</td>\n", "      <td>511.234316</td>\n", "      <td>-34.082288</td>\n", "      <td>-38.783293</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1697441058935</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Task Name                   Task ID  \\\n", "0  NG25-03-L_109  652ce2a780b3720044fe07da   \n", "1  NG25-03-L_109  652ce2a780b3720044fe07da   \n", "2  NG25-03-L_109  652ce2a780b3720044fe07da   \n", "3  NG25-03-L_109  652ce2a780b3720044fe07da   \n", "4  NG25-03-L_109  652ce2a780b3720044fe07da   \n", "\n", "                                Image Filename                  Image ID  \\\n", "0  NG25-03_WOT-L_20220923B-141524_M0803766.jpg  652cdebc2bd4eb004b0f2614   \n", "1  NG25-03_WOT-L_20220923B-141643_M0803805.jpg  652cdf1a2bd4eb004b0f2636   \n", "2  NG25-03_WOT-L_20220923B-141643_M0803805.jpg  652cdf1a2bd4eb004b0f2636   \n", "3  NG25-03_WOT-L_20220923B-141643_M0803805.jpg  652cdf1a2bd4eb004b0f2636   \n", "4  NG25-03_WOT-L_20220923B-141643_M0803805.jpg  652cdf1a2bd4eb004b0f2636   \n", "\n", "         Box X        Box Y      Box W      Box H       Label  \\\n", "0  5655.512086  4379.928897  58.304248  27.019042        bird   \n", "1  6751.818722   660.491231 -61.113068  51.711057  red lechwe   \n", "2  6683.654146   562.945373  56.412062  51.711057  red lechwe   \n", "3  6594.335047   574.697886 -71.690329 -66.989324  red lechwe   \n", "4  6493.263435   511.234316 -34.082288 -38.783293  red lechwe   \n", "\n", "   Label Confidence    Assignee      Timestamp  Is Ground Truth  \\\n", "0               NaN  annotator1  1697440748362            False   \n", "1               NaN  annotator1  1697441058935            False   \n", "2               NaN  annotator1  1697441058935            False   \n", "3               NaN  annotator1  1697441058935            False   \n", "4               NaN  annotator1  1697441058935            False   \n", "\n", "   Excluded By Line  \n", "0               NaN  \n", "1               NaN  \n", "2               NaN  \n", "3               NaN  \n", "4               NaN  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["## John\n", "anno2 = pd.read_csv(\"Pregroundtruth/NG25-03-L_109annotations.csv\")\n", "anno2.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "b3b76b11", "metadata": {}, "outputs": [{"data": {"text/plain": ["75"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["anno2[\"Label\"].count()"]}, {"cell_type": "code", "execution_count": 6, "id": "5ddff41f", "metadata": {}, "outputs": [{"data": {"text/plain": ["red lechwe    61\n", "bird          13\n", "crocodile      1\n", "Name: Label, dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["anno2[\"Label\"].value_counts()"]}, {"cell_type": "code", "execution_count": 53, "id": "759e5040", "metadata": {}, "outputs": [], "source": ["annoML = pd.read_csv(\"Pregroundtruth/NG25-05-R_1-150_2annotations.csv\")"]}, {"cell_type": "code", "execution_count": 54, "id": "020023f2", "metadata": {}, "outputs": [{"data": {"text/plain": ["92"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["annoML[\"Label\"].count()"]}, {"cell_type": "code", "execution_count": 55, "id": "a894758a", "metadata": {}, "outputs": [{"data": {"text/plain": ["buffalo       38\n", "red lechwe    33\n", "elephant      16\n", "bird           5\n", "Name: Label, dtype: int64"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["annoML[\"Label\"].value_counts()"]}, {"cell_type": "code", "execution_count": 21, "id": "21f0946f", "metadata": {}, "outputs": [], "source": ["anno1 = pd.read_csv(\"Pregroundtruth/NG25-03-R_217annotations.csv\")"]}, {"cell_type": "code", "execution_count": 22, "id": "48fc9ac2", "metadata": {}, "outputs": [{"data": {"text/plain": ["38"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["anno1[\"Label\"].count()"]}, {"cell_type": "code", "execution_count": 23, "id": "1e12e2a6", "metadata": {}, "outputs": [{"data": {"text/plain": ["red lechwe    27\n", "bird           9\n", "sitatunga      2\n", "Name: Label, dtype: int64"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["anno1[\"Label\"].value_counts()"]}, {"cell_type": "code", "execution_count": 24, "id": "bc5dac04", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141919_M0702931.jpg</td>\n", "      <td>652ce05e2bd4eb004b0f26ac</td>\n", "      <td>1004.498425</td>\n", "      <td>606.023528</td>\n", "      <td>15.513263</td>\n", "      <td>19.391578</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619185752</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141921_M0702932.jpg</td>\n", "      <td>652cdeb22bd4eb004b0f2610</td>\n", "      <td>4348.420108</td>\n", "      <td>322.072296</td>\n", "      <td>-26.224491</td>\n", "      <td>20.396827</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619306021</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141931_M0702937.jpg</td>\n", "      <td>652cdb2d2bd4eb004b0f24c6</td>\n", "      <td>3718.169110</td>\n", "      <td>3767.576185</td>\n", "      <td>69.339478</td>\n", "      <td>42.308834</td>\n", "      <td>sitatunga</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619586561</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141931_M0702937.jpg</td>\n", "      <td>652cdb2d2bd4eb004b0f24c6</td>\n", "      <td>2259.796464</td>\n", "      <td>791.107065</td>\n", "      <td>10.577208</td>\n", "      <td>17.628681</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619586561</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141931_M0702937.jpg</td>\n", "      <td>652cdb2d2bd4eb004b0f24c6</td>\n", "      <td>3413.182979</td>\n", "      <td>306.104597</td>\n", "      <td>41.667861</td>\n", "      <td>26.710167</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619586561</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Task Name                   Task ID  \\\n", "0  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "1  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "2  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "3  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "4  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "\n", "                                Image Filename                  Image ID  \\\n", "0  NG25-03_WOT-R_20220923B-141919_M0702931.jpg  652ce05e2bd4eb004b0f26ac   \n", "1  NG25-03_WOT-R_20220923B-141921_M0702932.jpg  652cdeb22bd4eb004b0f2610   \n", "2  NG25-03_WOT-R_20220923B-141931_M0702937.jpg  652cdb2d2bd4eb004b0f24c6   \n", "3  NG25-03_WOT-R_20220923B-141931_M0702937.jpg  652cdb2d2bd4eb004b0f24c6   \n", "4  NG25-03_WOT-R_20220923B-141931_M0702937.jpg  652cdb2d2bd4eb004b0f24c6   \n", "\n", "         Box X        Box Y      Box W      Box H       Label  \\\n", "0  1004.498425   606.023528  15.513263  19.391578        bird   \n", "1  4348.420108   322.072296 -26.224491  20.396827        bird   \n", "2  3718.169110  3767.576185  69.339478  42.308834   sitatunga   \n", "3  2259.796464   791.107065  10.577208  17.628681        bird   \n", "4  3413.182979   306.104597  41.667861  26.710167  red lechwe   \n", "\n", "   Label Confidence  Assignee      Timestamp  Is Ground Truth  \\\n", "0               NaN  mshirima  1697619185752            False   \n", "1               NaN  mshirima  1697619306021            False   \n", "2               NaN  mshirima  1697619586561            False   \n", "3               NaN  mshirima  1697619586561            False   \n", "4               NaN  mshirima  1697619586561            False   \n", "\n", "   Excluded By Line  \n", "0               NaN  \n", "1               NaN  \n", "2               NaN  \n", "3               NaN  \n", "4               NaN  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["anno1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "bf38182a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "id": "6953cc13", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NGAM17_R_251-350</td>\n", "      <td>6523e92918f3b3004425b099</td>\n", "      <td>KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...</td>\n", "      <td>651d48cda368a6004cf099c5</td>\n", "      <td>2020.584759</td>\n", "      <td>117.128125</td>\n", "      <td>-61.305359</td>\n", "      <td>77.254721</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696855473073</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NGAM17_R_251-350</td>\n", "      <td>6523e92918f3b3004425b099</td>\n", "      <td>KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...</td>\n", "      <td>651d48cda368a6004cf099c5</td>\n", "      <td>2058.962910</td>\n", "      <td>101.677181</td>\n", "      <td>23.924043</td>\n", "      <td>35.387646</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696855473073</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NGAM17_R_251-350</td>\n", "      <td>6523e92918f3b3004425b099</td>\n", "      <td>KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...</td>\n", "      <td>651d48cda368a6004cf099c5</td>\n", "      <td>2668.029160</td>\n", "      <td>157.499947</td>\n", "      <td>63.299029</td>\n", "      <td>69.280040</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696855473073</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NGAM17_R_251-350</td>\n", "      <td>6523e92918f3b3004425b099</td>\n", "      <td>KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...</td>\n", "      <td>651d48cda368a6004cf099c5</td>\n", "      <td>2862.412005</td>\n", "      <td>223.789481</td>\n", "      <td>39.374987</td>\n", "      <td>64.295864</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696855473073</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NGAM17_R_251-350</td>\n", "      <td>6523e92918f3b3004425b099</td>\n", "      <td>KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...</td>\n", "      <td>651d48cda368a6004cf099c5</td>\n", "      <td>3049.817005</td>\n", "      <td>403.219800</td>\n", "      <td>-66.787952</td>\n", "      <td>94.699335</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696855473073</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          Task Name                   Task ID  \\\n", "0  NGAM17_R_251-350  6523e92918f3b3004425b099   \n", "1  NGAM17_R_251-350  6523e92918f3b3004425b099   \n", "2  NGAM17_R_251-350  6523e92918f3b3004425b099   \n", "3  NGAM17_R_251-350  6523e92918f3b3004425b099   \n", "4  NGAM17_R_251-350  6523e92918f3b3004425b099   \n", "\n", "                                      Image Filename  \\\n", "0  KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...   \n", "1  KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...   \n", "2  KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...   \n", "3  KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...   \n", "4  KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...   \n", "\n", "                   Image ID        Box X       Box Y      Box W      Box H  \\\n", "0  651d48cda368a6004cf099c5  2020.584759  117.128125 -61.305359  77.254721   \n", "1  651d48cda368a6004cf099c5  2058.962910  101.677181  23.924043  35.387646   \n", "2  651d48cda368a6004cf099c5  2668.029160  157.499947  63.299029  69.280040   \n", "3  651d48cda368a6004cf099c5  2862.412005  223.789481  39.374987  64.295864   \n", "4  651d48cda368a6004cf099c5  3049.817005  403.219800 -66.787952  94.699335   \n", "\n", "      Label  Label Confidence    Assignee      Timestamp  Is Ground Truth  \\\n", "0  elephant               NaN  annotator2  1696855473073            False   \n", "1  elephant               NaN  annotator2  1696855473073            False   \n", "2  elephant               NaN  annotator2  1696855473073            False   \n", "3  elephant               NaN  annotator2  1696855473073            False   \n", "4  elephant               NaN  annotator2  1696855473073            False   \n", "\n", "   Excluded By Line  \n", "0               NaN  \n", "1               NaN  \n", "2               NaN  \n", "3               NaN  \n", "4               NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["imrev = pd.read_csv(\"Pregroundtruth/NGAM17_R_251-350annotations.csv\")\n", "imrev.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "50030dbd", "metadata": {}, "outputs": [{"data": {"text/plain": ["8"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["imrev[\"Label\"].count()"]}, {"cell_type": "code", "execution_count": 5, "id": "c714e3ae", "metadata": {}, "outputs": [{"data": {"text/plain": ["0    KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...\n", "1    KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...\n", "2    KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...\n", "3    KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...\n", "4    KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...\n", "5    KES22_IIM-R_20220831A-081603_KES22_IIM-R_20220...\n", "6    KES22_IIM-R_20220831A-081605_KES22_IIM-R_20220...\n", "7    KES22_IIM-R_20220831A-081403_KES22_IIM-R_20220...\n", "Name: Image Filename, dtype: object"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["imrev[\"Image Filename\"]"]}, {"cell_type": "code", "execution_count": null, "id": "568b35a8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "f6887d47", "metadata": {}, "source": ["# get the EC images"]}, {"cell_type": "code", "execution_count": 2, "id": "e4796abd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KAZA_Left2</td>\n", "      <td>651d568bb5c3e0004614dbf8</td>\n", "      <td>KES22_IIM-L_20220831A-080606_M0307790.jpg</td>\n", "      <td>651d385d99f002004b039c41</td>\n", "      <td>276.653182</td>\n", "      <td>115.056697</td>\n", "      <td>107.300066</td>\n", "      <td>84.030172</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696491628045</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KAZA_Left2</td>\n", "      <td>651d568bb5c3e0004614dbf8</td>\n", "      <td>KES22_IIM-L_20220831A-080630_M0307802.jpg</td>\n", "      <td>651d38d599f002004b039c6d</td>\n", "      <td>4226.060549</td>\n", "      <td>2047.907374</td>\n", "      <td>149.644111</td>\n", "      <td>196.754294</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696493284665</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KAZA_Left2</td>\n", "      <td>651d568bb5c3e0004614dbf8</td>\n", "      <td>KES22_IIM-L_20220831A-080632_M0307803.jpg</td>\n", "      <td>651d38e099f002004b039c71</td>\n", "      <td>759.028787</td>\n", "      <td>1502.816031</td>\n", "      <td>140.222185</td>\n", "      <td>185.946811</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696493392772</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KAZA_Left2</td>\n", "      <td>651d568bb5c3e0004614dbf8</td>\n", "      <td>KES22_IIM-L_20220831A-080712_M0307823.jpg</td>\n", "      <td>651d39aa99f002004b039cbb</td>\n", "      <td>5731.112967</td>\n", "      <td>1122.364992</td>\n", "      <td>57.587314</td>\n", "      <td>96.370607</td>\n", "      <td>impala</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696497726892</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KAZA_Left2</td>\n", "      <td>651d568bb5c3e0004614dbf8</td>\n", "      <td>KES22_IIM-L_20220831A-080714_M0307824.jpg</td>\n", "      <td>651d3bdd99f002004b039d86</td>\n", "      <td>2277.305387</td>\n", "      <td>2033.174831</td>\n", "      <td>80.130380</td>\n", "      <td>85.472405</td>\n", "      <td>impala</td>\n", "      <td>NaN</td>\n", "      <td>annotator2</td>\n", "      <td>1696497977464</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Task Name                   Task ID  \\\n", "0  KAZA_Left2  651d568bb5c3e0004614dbf8   \n", "1  KAZA_Left2  651d568bb5c3e0004614dbf8   \n", "2  KAZA_Left2  651d568bb5c3e0004614dbf8   \n", "3  KAZA_Left2  651d568bb5c3e0004614dbf8   \n", "4  KAZA_Left2  651d568bb5c3e0004614dbf8   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_IIM-L_20220831A-080606_M0307790.jpg  651d385d99f002004b039c41   \n", "1  KES22_IIM-L_20220831A-080630_M0307802.jpg  651d38d599f002004b039c6d   \n", "2  KES22_IIM-L_20220831A-080632_M0307803.jpg  651d38e099f002004b039c71   \n", "3  KES22_IIM-L_20220831A-080712_M0307823.jpg  651d39aa99f002004b039cbb   \n", "4  KES22_IIM-L_20220831A-080714_M0307824.jpg  651d3bdd99f002004b039d86   \n", "\n", "         Box X        Box Y       Box W       Box H     Label  \\\n", "0   276.653182   115.056697  107.300066   84.030172  elephant   \n", "1  4226.060549  2047.907374  149.644111  196.754294  elephant   \n", "2   759.028787  1502.816031  140.222185  185.946811  elephant   \n", "3  5731.112967  1122.364992   57.587314   96.370607    impala   \n", "4  2277.305387  2033.174831   80.130380   85.472405    impala   \n", "\n", "   Label Confidence    Assignee      Timestamp  Is Ground Truth  \\\n", "0               NaN  annotator2  1696491628045            False   \n", "1               NaN  annotator2  1696493284665            False   \n", "2               NaN  annotator2  1696493392772            False   \n", "3               NaN  annotator2  1696497726892            False   \n", "4               NaN  annotator2  1696497977464            False   \n", "\n", "   Excluded By Line  \n", "0               NaN  \n", "1               NaN  \n", "2               NaN  \n", "3               NaN  \n", "4               NaN  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["ec = pd.read_csv(\"Pregroundtruth/NGAM17_L_annotations1-454_annotator2.csv\")\n", "ec.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "47dff888", "metadata": {}, "outputs": [{"data": {"text/plain": ["impala      40\n", "elephant    29\n", "zebra        3\n", "giraffe      1\n", "Name: Label, dtype: int64"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["ec[\"Label\"].value_counts()"]}, {"cell_type": "code", "execution_count": 9, "id": "8124d531", "metadata": {}, "outputs": [], "source": ["elephant = ec[ec[\"Label\"]=='elephant']\n", "ecfile = elephant[\"Image Filename\"]"]}, {"cell_type": "code", "execution_count": 10, "id": "91c20c07", "metadata": {}, "outputs": [], "source": ["ecfile.to_csv(\"NGAM17-Lelephants.csv\")"]}, {"cell_type": "markdown", "id": "0096b6ff", "metadata": {}, "source": ["### Analysis for 16/10/2023"]}, {"cell_type": "code", "execution_count": 3, "id": "32a88549", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2240, 14)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["anno16 = pd.read_csv(\"Pregroundtruth/39Tasks16OctStartannotations.csv\")\n", "anno16.shape"]}, {"cell_type": "code", "execution_count": 16, "id": "d76b1cbb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-03-L_55</td>\n", "      <td>652ce26280b3720044fe07d8</td>\n", "      <td>NG25-03_WOT-L_20220923B-141348_M0803718.jpg</td>\n", "      <td>652cd9e42bd4eb004b0f244e</td>\n", "      <td>2089.133209</td>\n", "      <td>4277.160299</td>\n", "      <td>-66.223580</td>\n", "      <td>58.276751</td>\n", "      <td>white_bones</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1697442363937</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-03-L_55</td>\n", "      <td>652ce26280b3720044fe07d8</td>\n", "      <td>NG25-03_WOT-L_20220923B-141348_M0803718.jpg</td>\n", "      <td>652cd9e42bd4eb004b0f244e</td>\n", "      <td>2131.516301</td>\n", "      <td>4356.628595</td>\n", "      <td>44.149053</td>\n", "      <td>25.606451</td>\n", "      <td>white_bones</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1697442363937</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-03-L_55</td>\n", "      <td>652ce26280b3720044fe07d8</td>\n", "      <td>NG25-03_WOT-L_20220923B-141348_M0803718.jpg</td>\n", "      <td>652cd9e42bd4eb004b0f244e</td>\n", "      <td>2293.984817</td>\n", "      <td>4591.501559</td>\n", "      <td>65.340599</td>\n", "      <td>29.138375</td>\n", "      <td>white_bones</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1697442363937</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-03-L_55</td>\n", "      <td>652ce26280b3720044fe07d8</td>\n", "      <td>NG25-03_WOT-L_20220923B-141348_M0803718.jpg</td>\n", "      <td>652cd9e42bd4eb004b0f244e</td>\n", "      <td>2141.229093</td>\n", "      <td>4564.129146</td>\n", "      <td>24.723470</td>\n", "      <td>56.510788</td>\n", "      <td>white_bones</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1697442363937</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-03-L_55</td>\n", "      <td>652ce26280b3720044fe07d8</td>\n", "      <td>NG25-03_WOT-L_20220923B-141348_M0803718.jpg</td>\n", "      <td>652cd9e42bd4eb004b0f244e</td>\n", "      <td>2234.825086</td>\n", "      <td>4634.767632</td>\n", "      <td>25.606451</td>\n", "      <td>17.659621</td>\n", "      <td>sheep</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1697442363937</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Task Name                   Task ID  \\\n", "0  NG25-03-L_55  652ce26280b3720044fe07d8   \n", "1  NG25-03-L_55  652ce26280b3720044fe07d8   \n", "2  NG25-03-L_55  652ce26280b3720044fe07d8   \n", "3  NG25-03-L_55  652ce26280b3720044fe07d8   \n", "4  NG25-03-L_55  652ce26280b3720044fe07d8   \n", "\n", "                                Image Filename                  Image ID  \\\n", "0  NG25-03_WOT-L_20220923B-141348_M0803718.jpg  652cd9e42bd4eb004b0f244e   \n", "1  NG25-03_WOT-L_20220923B-141348_M0803718.jpg  652cd9e42bd4eb004b0f244e   \n", "2  NG25-03_WOT-L_20220923B-141348_M0803718.jpg  652cd9e42bd4eb004b0f244e   \n", "3  NG25-03_WOT-L_20220923B-141348_M0803718.jpg  652cd9e42bd4eb004b0f244e   \n", "4  NG25-03_WOT-L_20220923B-141348_M0803718.jpg  652cd9e42bd4eb004b0f244e   \n", "\n", "         Box X        Box Y      Box W      Box H        Label  \\\n", "0  2089.133209  4277.160299 -66.223580  58.276751  white_bones   \n", "1  2131.516301  4356.628595  44.149053  25.606451  white_bones   \n", "2  2293.984817  4591.501559  65.340599  29.138375  white_bones   \n", "3  2141.229093  4564.129146  24.723470  56.510788  white_bones   \n", "4  2234.825086  4634.767632  25.606451  17.659621        sheep   \n", "\n", "   Label Confidence Assignee      Timestamp  Is Ground Truth  Excluded By Line  \n", "0               NaN  hgeorge  1697442363937            False               NaN  \n", "1               NaN  hgeorge  1697442363937            False               NaN  \n", "2               NaN  hgeorge  1697442363937            False               NaN  \n", "3               NaN  hgeorge  1697442363937            False               NaN  \n", "4               NaN  hgeorge  1697442363937            False               NaN  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["anno16.head()"]}, {"cell_type": "code", "execution_count": 17, "id": "a59268e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["37"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["anno16[\"Task Name\"].nunique()"]}, {"cell_type": "code", "execution_count": 18, "id": "f0263aa1", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['NG25-03-L_55', 'NG25-03-L_109', 'NG25-03-L_163', 'NG25-03-L_217',\n", "       'NG25-03-L_271', 'NG25-03-L_325', 'NG25-03-R_55', 'NG25-03-R_109',\n", "       'NG25-03-R_163', 'NG25-03-R_271', 'NG25-03-R_325',\n", "       'NG25-03-L_55_2', 'NG25-03-L_109_2', 'NG25-03-L_163_2',\n", "       'NG25-03-L_217_2', 'NG25-03-L_271_2', 'NG25-03-L_325_2',\n", "       'NG25-03-R_55_2', 'NG25-03-R_109_2', 'NG25-03-R_163_2',\n", "       'NG25-03-R_217_2', 'NG25-03-R_271_2', 'NG25-03-R_325_2',\n", "       'NG25-03-L_109ML', 'NG25-03-R_1426', 'NG25-03-R_1427',\n", "       'NG25-03-R_1428', 'NG25-03-R_1429', 'NG25-03-R_1430',\n", "       'NG25-03-R_1431', 'NG25-03-R_1432', 'NG25-03-R_1433',\n", "       'NG25-03-R_1434', 'NG25-01-R_141', 'NG25-01-R_140_90',\n", "       'NG25-01-R_140_180', 'NG25-01-R_140_290'], dtype=object)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["anno16[\"Task Name\"].unique()"]}, {"cell_type": "code", "execution_count": 20, "id": "48f17176", "metadata": {}, "outputs": [{"data": {"text/plain": ["166"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "len(anno16[anno16[\"Task Name\"]=='NG25-03-R_163'])"]}, {"cell_type": "code", "execution_count": 23, "id": "5af2b1fb", "metadata": {}, "outputs": [], "source": ["tsk = anno16[anno16[\"Task Name\"]=='NG25-03-R_163']"]}, {"cell_type": "code", "execution_count": 24, "id": "7e17ccc7", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["tsk[\"Assignee\"].nunique()"]}, {"cell_type": "code", "execution_count": 26, "id": "864267de", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['annotator1'], dtype=object)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["#annotator\n", "tsk[\"Assignee\"].unique()"]}, {"cell_type": "code", "execution_count": 27, "id": "16301011", "metadata": {}, "outputs": [{"data": {"text/plain": ["166"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["#no of BB\n", "tsk[\"Label\"].count()"]}, {"cell_type": "code", "execution_count": 28, "id": "66f13888", "metadata": {}, "outputs": [{"data": {"text/plain": ["red lechwe     123\n", "elephant        25\n", "bird            15\n", "lion             1\n", "bushback         1\n", "white_bones      1\n", "Name: Label, dtype: int64"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["#no of animals annotated\n", "tsk[\"Label\"].value_counts()"]}, {"cell_type": "code", "execution_count": 29, "id": "cc7edafd", "metadata": {}, "outputs": [], "source": ["tsk = anno16[anno16[\"Task Name\"]=='NG25-03-L_109']"]}, {"cell_type": "code", "execution_count": 30, "id": "3075d06a", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["tsk[\"Assignee\"].nunique()"]}, {"cell_type": "code", "execution_count": 31, "id": "7fba7925", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['annotator1'], dtype=object)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["#annotator\n", "tsk[\"Assignee\"].unique()"]}, {"cell_type": "code", "execution_count": 32, "id": "45621853", "metadata": {}, "outputs": [{"data": {"text/plain": ["75"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["#no of BB\n", "tsk[\"Label\"].count()"]}, {"cell_type": "code", "execution_count": 34, "id": "7725b49d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["red lechwe    61\n", "bird          13\n", "crocodile      1\n", "Name: Label, dtype: int64\n"]}], "source": ["tsk[\"Label\"].value_counts()"]}, {"cell_type": "code", "execution_count": 59, "id": "19f140b4", "metadata": {}, "outputs": [{"data": {"text/plain": ["'NG25-03-L_163'"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["tsk = anno16[anno16[\"Task Name\"]=='NG25-03-L_163']\n", "#tsk.head()\n", "tsk.iloc[0,0]"]}, {"cell_type": "code", "execution_count": 4, "id": "c9d7a450", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Task name:  NG25-01-R_140_290\n", "No of annotator/s:  1\n", "Annotator:  ['jmmbaga']\n", "No of bounding boxes:  186\n", "Animals found:  red lechwe     141\n", "bird            25\n", "cow             13\n", "crocodile        3\n", "hippo            1\n", "eland            1\n", "white_bones      1\n", "reedbuck         1\n", "Name: Label, dtype: int64\n"]}], "source": ["tsk = anno16[anno16[\"Task Name\"]=='NG25-01-R_140_290']\n", "print(\"Task name: \",tsk.iloc[0,0])\n", "print(\"No of annotator/s: \",tsk['Assignee'].nunique())#confirm its one annotator for the task\n", "print(\"Annotator: \",tsk[\"Assignee\"].unique()) #annotator\n", "print(\"No of bounding boxes: \",tsk[\"Label\"].count()) #no of BB\n", "print(\"Animals found: \",tsk[\"Label\"].value_counts()) #no of animals annotated"]}, {"cell_type": "markdown", "id": "cfe02a42", "metadata": {}, "source": ["### Analysis for 17/10 and 18/10"]}, {"cell_type": "code", "execution_count": 6, "id": "ad9afd5d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(972, 14)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["anno18 = pd.read_csv(\"Pregroundtruth/20Tasks18Octannotations.csv\")\n", "anno18.shape"]}, {"cell_type": "code", "execution_count": 7, "id": "9255ffdc", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>t_A</td>\n", "      <td>652e6e67df32760044f21ca3</td>\n", "      <td>KES22_WOT-R_20220923B-145909_M0704122.jpg</td>\n", "      <td>65250b2033c806004cc2d7bd</td>\n", "      <td>3687.286154</td>\n", "      <td>2034.116923</td>\n", "      <td>438.449231</td>\n", "      <td>503.138462</td>\n", "      <td>buffalo</td>\n", "      <td>NaN</td>\n", "      <td>simbamangu</td>\n", "      <td>1697541870551</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>t_A</td>\n", "      <td>652e6e67df32760044f21ca3</td>\n", "      <td>KES22_WOT-R_20220923B-145909_M0704122.jpg</td>\n", "      <td>65250b2033c806004cc2d7bd</td>\n", "      <td>4564.184615</td>\n", "      <td>2400.689231</td>\n", "      <td>546.264615</td>\n", "      <td>309.070769</td>\n", "      <td>buffalo</td>\n", "      <td>NaN</td>\n", "      <td>simbamangu</td>\n", "      <td>1697541870551</td>\n", "      <td>True</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>t_B</td>\n", "      <td>652e6e82df32760044f21ca5</td>\n", "      <td>KES22_WOT-R_20220923B-145909_M0704122.jpg</td>\n", "      <td>65250b2033c806004cc2d7bd</td>\n", "      <td>2724.135385</td>\n", "      <td>2012.553846</td>\n", "      <td>409.698462</td>\n", "      <td>280.320000</td>\n", "      <td>buffalo</td>\n", "      <td>NaN</td>\n", "      <td>simbamangu</td>\n", "      <td>1697541886693</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-01-R_140_290_2</td>\n", "      <td>652e744adf32760044f21cbf</td>\n", "      <td>NG25-01_WOT-R_20220923B-140749_M0702587.jpg</td>\n", "      <td>652d40b1f9f7d0004c6a82a7</td>\n", "      <td>5307.716101</td>\n", "      <td>1212.299695</td>\n", "      <td>-17.094507</td>\n", "      <td>-17.094507</td>\n", "      <td>white_bones</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1697544588116</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-01-R_140_290_2</td>\n", "      <td>652e744adf32760044f21cbf</td>\n", "      <td>NG25-01_WOT-R_20220923B-140749_M0702587.jpg</td>\n", "      <td>652d40b1f9f7d0004c6a82a7</td>\n", "      <td>6498.700836</td>\n", "      <td>2500.806369</td>\n", "      <td>7.770220</td>\n", "      <td>11.655330</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1697544588116</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             Task Name                   Task ID  \\\n", "0                  t_A  652e6e67df32760044f21ca3   \n", "1                  t_A  652e6e67df32760044f21ca3   \n", "2                  t_B  652e6e82df32760044f21ca5   \n", "3  NG25-01-R_140_290_2  652e744adf32760044f21cbf   \n", "4  NG25-01-R_140_290_2  652e744adf32760044f21cbf   \n", "\n", "                                Image Filename                  Image ID  \\\n", "0    KES22_WOT-R_20220923B-145909_M0704122.jpg  65250b2033c806004cc2d7bd   \n", "1    KES22_WOT-R_20220923B-145909_M0704122.jpg  65250b2033c806004cc2d7bd   \n", "2    KES22_WOT-R_20220923B-145909_M0704122.jpg  65250b2033c806004cc2d7bd   \n", "3  NG25-01_WOT-R_20220923B-140749_M0702587.jpg  652d40b1f9f7d0004c6a82a7   \n", "4  NG25-01_WOT-R_20220923B-140749_M0702587.jpg  652d40b1f9f7d0004c6a82a7   \n", "\n", "         Box X        Box Y       Box W       Box H        Label  \\\n", "0  3687.286154  2034.116923  438.449231  503.138462      buffalo   \n", "1  4564.184615  2400.689231  546.264615  309.070769      buffalo   \n", "2  2724.135385  2012.553846  409.698462  280.320000      buffalo   \n", "3  5307.716101  1212.299695  -17.094507  -17.094507  white_bones   \n", "4  6498.700836  2500.806369    7.770220   11.655330         bird   \n", "\n", "   Label Confidence    Assignee      Timestamp  Is Ground Truth  \\\n", "0               NaN  simbamangu  1697541870551             True   \n", "1               NaN  simbamangu  1697541870551             True   \n", "2               NaN  simbamangu  1697541886693            False   \n", "3               Na<PERSON>      zrahim  1697544588116            False   \n", "4               NaN      zrahim  1697544588116            False   \n", "\n", "   Excluded By Line  \n", "0               NaN  \n", "1               NaN  \n", "2               NaN  \n", "3               NaN  \n", "4               NaN  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["anno18.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "8f97a0aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["18"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["anno18[\"Task Name\"].nunique()"]}, {"cell_type": "code", "execution_count": 12, "id": "5a9cdaf5", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['t_A', 't_B', 'NG25-01-R_140_290_2', 'NG25-03-R_1429_2',\n", "       'NG25-03-R_1430_2', 'NG25-03-R_1431_2', 'NG25-03-R_1432_2',\n", "       'NG25-01-R_141_2', 'NG25-01-R_140_180_2', 'NG25-01-R_140_90_2',\n", "       'NG25-03-R_1426_2', 'NG25-03-L_1426', 'NG25-03-L_1427',\n", "       'NG25-03-L_1426_2', 'NG25-03-L_1429', 'NG25-03-L_1429_2',\n", "       'NG25-03-L_143_56_2', 'NG25-01-L_36'], dtype=object)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["anno18[\"Task Name\"].unique()"]}, {"cell_type": "code", "execution_count": 26, "id": "a961b25b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Task name:  NG25-03-R_1426_2\n", "No of annotator/s:  1\n", "Annotator:  ['annotator1']\n", "No of bounding boxes:  35\n", "Animals found:  red lechwe    23\n", "bird          12\n", "Name: Label, dtype: int64\n"]}], "source": ["tsk = anno18[anno18[\"Task Name\"]=='NG25-03-R_1426_2']\n", "print(\"Task name: \",tsk.iloc[0,0])\n", "print(\"No of annotator/s: \",tsk['Assignee'].nunique())#confirm its one annotator for the task\n", "print(\"Annotator: \",tsk[\"Assignee\"].unique()) #annotator\n", "print(\"No of bounding boxes: \",tsk[\"Label\"].count()) #no of BB\n", "print(\"Animals found: \",tsk[\"Label\"].value_counts()) #no of animals annotated"]}, {"cell_type": "code", "execution_count": null, "id": "40499458", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "01552f1d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Category  Count\n", "0        A      4\n", "1        B      2\n", "2        C      2\n"]}], "source": []}, {"cell_type": "code", "execution_count": 3, "id": "c2c37e3c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141919_M0702931.jpg</td>\n", "      <td>652ce05e2bd4eb004b0f26ac</td>\n", "      <td>1004.498425</td>\n", "      <td>606.023528</td>\n", "      <td>15.513263</td>\n", "      <td>19.391578</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619185752</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141921_M0702932.jpg</td>\n", "      <td>652cdeb22bd4eb004b0f2610</td>\n", "      <td>4348.420108</td>\n", "      <td>322.072296</td>\n", "      <td>-26.224491</td>\n", "      <td>20.396827</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619306021</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141931_M0702937.jpg</td>\n", "      <td>652cdb2d2bd4eb004b0f24c6</td>\n", "      <td>3718.169110</td>\n", "      <td>3767.576185</td>\n", "      <td>69.339478</td>\n", "      <td>42.308834</td>\n", "      <td>sitatunga</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619586561</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141931_M0702937.jpg</td>\n", "      <td>652cdb2d2bd4eb004b0f24c6</td>\n", "      <td>2259.796464</td>\n", "      <td>791.107065</td>\n", "      <td>10.577208</td>\n", "      <td>17.628681</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619586561</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141931_M0702937.jpg</td>\n", "      <td>652cdb2d2bd4eb004b0f24c6</td>\n", "      <td>3413.182979</td>\n", "      <td>306.104597</td>\n", "      <td>41.667861</td>\n", "      <td>26.710167</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619586561</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Task Name                   Task ID  \\\n", "0  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "1  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "2  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "3  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "4  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "\n", "                                Image Filename                  Image ID  \\\n", "0  NG25-03_WOT-R_20220923B-141919_M0702931.jpg  652ce05e2bd4eb004b0f26ac   \n", "1  NG25-03_WOT-R_20220923B-141921_M0702932.jpg  652cdeb22bd4eb004b0f2610   \n", "2  NG25-03_WOT-R_20220923B-141931_M0702937.jpg  652cdb2d2bd4eb004b0f24c6   \n", "3  NG25-03_WOT-R_20220923B-141931_M0702937.jpg  652cdb2d2bd4eb004b0f24c6   \n", "4  NG25-03_WOT-R_20220923B-141931_M0702937.jpg  652cdb2d2bd4eb004b0f24c6   \n", "\n", "         Box X        Box Y      Box W      Box H       Label  \\\n", "0  1004.498425   606.023528  15.513263  19.391578        bird   \n", "1  4348.420108   322.072296 -26.224491  20.396827        bird   \n", "2  3718.169110  3767.576185  69.339478  42.308834   sitatunga   \n", "3  2259.796464   791.107065  10.577208  17.628681        bird   \n", "4  3413.182979   306.104597  41.667861  26.710167  red lechwe   \n", "\n", "   Label Confidence  Assignee      Timestamp  Is Ground Truth  \\\n", "0               NaN  mshirima  1697619185752            False   \n", "1               NaN  mshirima  1697619306021            False   \n", "2               NaN  mshirima  1697619586561            False   \n", "3               NaN  mshirima  1697619586561            False   \n", "4               NaN  mshirima  1697619586561            False   \n", "\n", "   Excluded By Line  \n", "0               NaN  \n", "1               NaN  \n", "2               NaN  \n", "3               NaN  \n", "4               NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["annotest = pd.read_csv(\"Pregroundtruth/NG25-03-R_217annotations.csv\")\n", "annotest.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "5abf2b39", "metadata": {}, "outputs": [], "source": ["bbval= annotest[\"Label\"].value_counts()"]}, {"cell_type": "code", "execution_count": 17, "id": "b5c1c942", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          TaskID Annotator  BBcount\n", "0  NG25-03-<PERSON><PERSON>217  mshirima       38\n"]}], "source": ["annodf2 = pd.DataFrame({'LabelBBox':bbval.index, 'AnimalCount':bbval.values})\n", "print(annodf)"]}, {"cell_type": "code", "execution_count": 9, "id": "87027fb1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NG25-03-R_217\n"]}], "source": ["tskname = annotest.iloc[0,0]\n", "print(tskname)"]}, {"cell_type": "code", "execution_count": 11, "id": "74464c2e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['m<PERSON><PERSON>']\n"]}], "source": ["annotator= annotest[\"Assignee\"].unique()\n", "print(annotator)\n"]}, {"cell_type": "code", "execution_count": 12, "id": "6780870d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["38\n"]}], "source": ["bbcount = annotest[\"Label\"].count()\n", "print(bbcount)"]}, {"cell_type": "code", "execution_count": 20, "id": "59fbefed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          TaskID Annotator  BBcount\n", "0  NG25-03-<PERSON><PERSON>217  mshirima       38\n"]}], "source": ["annodf1 = pd.DataFrame({'TaskID':tskname, 'Annotator':annotator,'BBcount':bbcount})\n", "print(annodf1)"]}, {"cell_type": "code", "execution_count": 27, "id": "cd3bd1d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          TaskID Annotator BBcount   LabelBBox  AnimalCount\n", "0  NG25-03-R_217  mshirima      38         NaN          NaN\n", "1           None      None    None  red lechwe         27.0\n", "2           None      None    None        bird          9.0\n", "3           None      None    None   sitatunga          2.0\n"]}], "source": ["# Add missing columns with NaN values\n", "missing_columns = set(annodf1.columns) - set(annodf2.columns)\n", "for col in missing_columns:\n", "    annodf2[col] = None  # Add empty column with NaN values\n", "\n", "# Concatenate DataFrames vertically\n", "concatenated_df = pd.concat([annodf1, annodf2], axis=0)\n", "\n", "# Reindex the resulting DataFrame\n", "concatenated_df = concatenated_df.reset_index(drop=True)\n", "print(concatenated_df)"]}, {"cell_type": "code", "execution_count": 28, "id": "5f3a57fb", "metadata": {}, "outputs": [{"data": {"text/plain": ["38.0"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["concatenated_df['AnimalCount'].sum()"]}, {"cell_type": "code", "execution_count": 32, "id": "9e7c8faa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          TaskID Annotator BBcount   LabelBBox  AnimalCount\n", "0  NG25-03-<PERSON><PERSON>217  mshirima    38.0  red lechwe           27\n", "1            NaN       NaN     NaN        bird            9\n", "2            NaN       NaN     NaN   sitatunga            2\n"]}], "source": ["# Combine the two DataFrames, filling in missing values\n", "combined_df = annodf1.combine_first(annodf2)\n", "\n", "# Rearrange the columns in the order you want\n", "combined_df = combined_df[['TaskID', 'Annotator', 'BBcount', 'LabelBBox', 'AnimalCount']]\n", "print(combined_df)"]}, {"cell_type": "code", "execution_count": 2, "id": "b227424b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141919_M0702931.jpg</td>\n", "      <td>652ce05e2bd4eb004b0f26ac</td>\n", "      <td>1004.498425</td>\n", "      <td>606.023528</td>\n", "      <td>15.513263</td>\n", "      <td>19.391578</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619185752</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141921_M0702932.jpg</td>\n", "      <td>652cdeb22bd4eb004b0f2610</td>\n", "      <td>4348.420108</td>\n", "      <td>322.072296</td>\n", "      <td>-26.224491</td>\n", "      <td>20.396827</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619306021</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141931_M0702937.jpg</td>\n", "      <td>652cdb2d2bd4eb004b0f24c6</td>\n", "      <td>3718.169110</td>\n", "      <td>3767.576185</td>\n", "      <td>69.339478</td>\n", "      <td>42.308834</td>\n", "      <td>sitatunga</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619586561</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141931_M0702937.jpg</td>\n", "      <td>652cdb2d2bd4eb004b0f24c6</td>\n", "      <td>2259.796464</td>\n", "      <td>791.107065</td>\n", "      <td>10.577208</td>\n", "      <td>17.628681</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619586561</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>652ce56880b3720044fe07ee</td>\n", "      <td>NG25-03_WOT-R_20220923B-141931_M0702937.jpg</td>\n", "      <td>652cdb2d2bd4eb004b0f24c6</td>\n", "      <td>3413.182979</td>\n", "      <td>306.104597</td>\n", "      <td>41.667861</td>\n", "      <td>26.710167</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1697619586561</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Task Name                   Task ID  \\\n", "0  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "1  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "2  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "3  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "4  NG25-03-R_217  652ce56880b3720044fe07ee   \n", "\n", "                                Image Filename                  Image ID  \\\n", "0  NG25-03_WOT-R_20220923B-141919_M0702931.jpg  652ce05e2bd4eb004b0f26ac   \n", "1  NG25-03_WOT-R_20220923B-141921_M0702932.jpg  652cdeb22bd4eb004b0f2610   \n", "2  NG25-03_WOT-R_20220923B-141931_M0702937.jpg  652cdb2d2bd4eb004b0f24c6   \n", "3  NG25-03_WOT-R_20220923B-141931_M0702937.jpg  652cdb2d2bd4eb004b0f24c6   \n", "4  NG25-03_WOT-R_20220923B-141931_M0702937.jpg  652cdb2d2bd4eb004b0f24c6   \n", "\n", "         Box X        Box Y      Box W      Box H       Label  \\\n", "0  1004.498425   606.023528  15.513263  19.391578        bird   \n", "1  4348.420108   322.072296 -26.224491  20.396827        bird   \n", "2  3718.169110  3767.576185  69.339478  42.308834   sitatunga   \n", "3  2259.796464   791.107065  10.577208  17.628681        bird   \n", "4  3413.182979   306.104597  41.667861  26.710167  red lechwe   \n", "\n", "   Label Confidence  Assignee      Timestamp  Is Ground Truth  \\\n", "0               NaN  mshirima  1697619185752            False   \n", "1               NaN  mshirima  1697619306021            False   \n", "2               NaN  mshirima  1697619586561            False   \n", "3               NaN  mshirima  1697619586561            False   \n", "4               NaN  mshirima  1697619586561            False   \n", "\n", "   Excluded By Line  \n", "0               NaN  \n", "1               NaN  \n", "2               NaN  \n", "3               NaN  \n", "4               NaN  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["annotest = pd.read_csv(\"Pregroundtruth/NG25-03-R_217annotations.csv\")\n", "annotest.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "e0c62161", "metadata": {}, "outputs": [], "source": ["tskid =annotest.iloc[0,0]\n", "bb = annotest[\"Label\"].count()\n", "annotator = annotest[\"Assignee\"].unique()"]}, {"cell_type": "code", "execution_count": 4, "id": "17967f88", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["red lechwe    27\n", "bird           9\n", "sitatunga      2\n", "Name: Label, dtype: int64\n"]}], "source": ["anilabel = annotest[\"Label\"].value_counts()\n", "print(anilabel)"]}, {"cell_type": "code", "execution_count": 6, "id": "1a9908ab", "metadata": {}, "outputs": [], "source": ["animaldf = anilabel.to_frame().T\n", "animaldf_reset = animaldf.reset_index(drop=True)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "fa967b87", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>red lechwe</th>\n", "      <th>bird</th>\n", "      <th>sitatunga</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>27</td>\n", "      <td>9</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   red lechwe  bird  sitatunga\n", "0          27     9          2"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["animaldf_reset"]}, {"cell_type": "code", "execution_count": 8, "id": "63b1f0dd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskID</th>\n", "      <th>Annotator</th>\n", "      <th>BBcount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>38</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          TaskID Annotator  BBcount\n", "0  NG25-03-<PERSON><PERSON>217  mshirima       38"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["outdf = pd.DataFrame({'TaskID':tskid, 'Annotator':annotator,'BBcount':bb})\n", "outdf"]}, {"cell_type": "code", "execution_count": 9, "id": "2663931e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          TaskID Annotator  BBcount  red lechwe  bird  sitatunga\n", "0  NG25-03-<PERSON><PERSON>217  mshirima       38          27     9          2\n"]}], "source": ["combinedf = pd.concat([outdf, animaldf_reset], axis=1)\n", "print(combinedf)"]}, {"cell_type": "code", "execution_count": null, "id": "334c7ce5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "587ec92c", "metadata": {}, "source": ["#### Appending another task to the combinedf"]}, {"cell_type": "code", "execution_count": 10, "id": "fd7e3777", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-04-R_1440_2</td>\n", "      <td>65286fb70635dd00444c0430</td>\n", "      <td>KES22_WOT-R_20220923B-144033_M0703566.jpg</td>\n", "      <td>65269771da746c004c34e723</td>\n", "      <td>3939.091898</td>\n", "      <td>4226.346975</td>\n", "      <td>193.399458</td>\n", "      <td>383.954806</td>\n", "      <td>giraffe</td>\n", "      <td>NaN</td>\n", "      <td>jmmbaga</td>\n", "      <td>1697187750290</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-04-R_1440_2</td>\n", "      <td>65286fb70635dd00444c0430</td>\n", "      <td>KES22_WOT-R_20220923B-144043_M0703571.jpg</td>\n", "      <td>652695a9da746c004c34e679</td>\n", "      <td>5406.074892</td>\n", "      <td>2820.356779</td>\n", "      <td>57.877538</td>\n", "      <td>159.554294</td>\n", "      <td>zebra</td>\n", "      <td>NaN</td>\n", "      <td>jmmbaga</td>\n", "      <td>1697188028866</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-04-R_1440_2</td>\n", "      <td>65286fb70635dd00444c0430</td>\n", "      <td>KES22_WOT-R_20220923B-144043_M0703571.jpg</td>\n", "      <td>652695a9da746c004c34e679</td>\n", "      <td>5632.892270</td>\n", "      <td>2638.902877</td>\n", "      <td>53.184765</td>\n", "      <td>120.447849</td>\n", "      <td>zebra</td>\n", "      <td>NaN</td>\n", "      <td>jmmbaga</td>\n", "      <td>1697188028866</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-04-R_1440_2</td>\n", "      <td>65286fb70635dd00444c0430</td>\n", "      <td>KES22_WOT-R_20220923B-144043_M0703571.jpg</td>\n", "      <td>652695a9da746c004c34e679</td>\n", "      <td>5707.976643</td>\n", "      <td>2459.013232</td>\n", "      <td>51.620507</td>\n", "      <td>120.447849</td>\n", "      <td>zebra</td>\n", "      <td>NaN</td>\n", "      <td>jmmbaga</td>\n", "      <td>1697188028866</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-04-R_1440_2</td>\n", "      <td>65286fb70635dd00444c0430</td>\n", "      <td>KES22_WOT-R_20220923B-144043_M0703571.jpg</td>\n", "      <td>652695a9da746c004c34e679</td>\n", "      <td>5697.026839</td>\n", "      <td>2394.878663</td>\n", "      <td>53.184765</td>\n", "      <td>87.598436</td>\n", "      <td>zebra</td>\n", "      <td>NaN</td>\n", "      <td>jmmbaga</td>\n", "      <td>1697188028866</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            Task Name                   Task ID  \\\n", "0  NG25-04-R_1440_2    65286fb70635dd00444c0430   \n", "1  NG25-04-R_1440_2    65286fb70635dd00444c0430   \n", "2  NG25-04-R_1440_2    65286fb70635dd00444c0430   \n", "3  NG25-04-R_1440_2    65286fb70635dd00444c0430   \n", "4  NG25-04-R_1440_2    65286fb70635dd00444c0430   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_WOT-R_20220923B-144033_M0703566.jpg  65269771da746c004c34e723   \n", "1  KES22_WOT-R_20220923B-144043_M0703571.jpg  652695a9da746c004c34e679   \n", "2  KES22_WOT-R_20220923B-144043_M0703571.jpg  652695a9da746c004c34e679   \n", "3  KES22_WOT-R_20220923B-144043_M0703571.jpg  652695a9da746c004c34e679   \n", "4  KES22_WOT-R_20220923B-144043_M0703571.jpg  652695a9da746c004c34e679   \n", "\n", "         Box X        Box Y       Box W       Box H    Label  \\\n", "0  3939.091898  4226.346975  193.399458  383.954806  giraffe   \n", "1  5406.074892  2820.356779   57.877538  159.554294    zebra   \n", "2  5632.892270  2638.902877   53.184765  120.447849    zebra   \n", "3  5707.976643  2459.013232   51.620507  120.447849    zebra   \n", "4  5697.026839  2394.878663   53.184765   87.598436    zebra   \n", "\n", "   Label Confidence Assignee      Timestamp  Is Ground Truth  Excluded By Line  \n", "0               NaN  jmmbaga  1697187750290            False               NaN  \n", "1               NaN  jmmbaga  1697188028866            False               NaN  \n", "2               NaN  jmmbaga  1697188028866            False               NaN  \n", "3               NaN  jmmbaga  1697188028866            False               NaN  \n", "4               NaN  jmmbaga  1697188028866            False               NaN  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["annonew = pd.read_csv(\"Pregroundtruth/NG25-04-R_1440_2annotations.csv\")\n", "annonew.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "b0a887e0", "metadata": {}, "outputs": [], "source": ["tsknew =annonew.iloc[0,0]\n", "bbnew = annonew[\"Label\"].count()\n", "annotatornw = annonew[\"Assignee\"].unique()"]}, {"cell_type": "code", "execution_count": 12, "id": "4666a66c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["giraffe    48\n", "zebra      25\n", "warthog     3\n", "Name: Label, dtype: int64\n"]}], "source": ["anilabelnew = annonew[\"Label\"].value_counts()\n", "print(anilabelnew)"]}, {"cell_type": "code", "execution_count": 13, "id": "dd1e96bd", "metadata": {}, "outputs": [], "source": ["animaldfnw = anilabelnew.to_frame().T\n", "animaldfnw_reset = animaldfnw.reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 14, "id": "02b252fe", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>giraffe</th>\n", "      <th>zebra</th>\n", "      <th>warthog</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>48</td>\n", "      <td>25</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   giraffe  zebra  warthog\n", "0       48     25        3"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["animaldfnw_reset"]}, {"cell_type": "code", "execution_count": 15, "id": "2c039e74", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskID</th>\n", "      <th>Annotator</th>\n", "      <th>BBcount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-04-R_1440_2</td>\n", "      <td>jmmbaga</td>\n", "      <td>76</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               TaskID Annotator  BBcount\n", "0  NG25-04-R_1440_2     jmmbaga       76"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["outdfnw = pd.DataFrame({'TaskID':tsknew, 'Annotator':annotatornw,'BBcount':bbnew})\n", "outdfnw"]}, {"cell_type": "code", "execution_count": 16, "id": "074d5cf1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["               TaskID Annotator  BBcount  giraffe  zebra  warthog\n", "0  NG25-04-R_1440_2     jmmbaga       76       48     25        3\n"]}], "source": ["combinedfnw = pd.concat([outdfnw, animaldfnw_reset], axis=1)\n", "print(combinedfnw)"]}, {"cell_type": "code", "execution_count": 17, "id": "0bcd754a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          TaskID Annotator  BBcount  red lechwe  bird  sitatunga\n", "0  NG25-03-<PERSON><PERSON>217  mshirima       38          27     9          2\n", "               TaskID Annotator  BBcount  giraffe  zebra  warthog\n", "0  NG25-04-R_1440_2     jmmbaga       76       48     25        3\n"]}], "source": ["## append combinedfnw to combinedf\n", "print(combinedf)\n", "print(combinedfnw)\n"]}, {"cell_type": "code", "execution_count": 18, "id": "a6953699", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['TaskID', 'Annotator', 'BBcount', 'giraffe', 'zebra', 'warthog'], dtype='object')"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["combinedfnw.keys()"]}, {"cell_type": "code", "execution_count": 19, "id": "a21d8765", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["               TaskID Annotator  BBcount  red lechwe  bird  sitatunga giraffe  \\\n", "0       NG25-03-<PERSON><PERSON>217  mshirima       38        27.0   9.0        2.0    None   \n", "0  NG25-04-R_1440_2     jmmbaga       76         NaN   NaN        NaN      48   \n", "\n", "  zebra warthog  \n", "0  None    None  \n", "0    25       3  \n"]}], "source": ["# Check if the source row has columns not present in the target DataFrame\n", "missing_columns = [col for col in combinedfnw.keys() if col not in combinedf.columns]\n", "\n", "# Add missing columns to the target DataFrame\n", "for col in missing_columns:\n", "    combinedf[col] = None\n", "\n", "# Append the neW row to the existing DataFrame\n", "\n", "combinedf = pd.concat([combinedf,combinedfnw],axis=0)\n", "print(combinedf)\n"]}, {"cell_type": "code", "execution_count": 20, "id": "4b96b985", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>0</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>TaskID</th>\n", "      <td>NG25-03-R_217</td>\n", "      <td>NG25-04-R_1440_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Annotator</th>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>jmmbaga</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BBcount</th>\n", "      <td>38</td>\n", "      <td>76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>red lechwe</th>\n", "      <td>27.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>bird</th>\n", "      <td>9.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sitatunga</th>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>giraffe</th>\n", "      <td>None</td>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>zebra</th>\n", "      <td>None</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>warthog</th>\n", "      <td>None</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        0                   0\n", "TaskID      NG25-03-R_217  NG25-04-R_1440_2  \n", "Annotator        m<PERSON><PERSON>             j<PERSON>a\n", "BBcount                38                  76\n", "red lechwe           27.0                 NaN\n", "bird                  9.0                 NaN\n", "sitatunga             2.0                 NaN\n", "giraffe              None                  48\n", "zebra                None                  25\n", "warthog              None                   3"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["combinedf.T"]}, {"cell_type": "code", "execution_count": null, "id": "dd0db541", "metadata": {}, "outputs": [], "source": ["#images and tasks\n", "tasksdf = pd.read_csv(\"/home/<USER>/Dropbox/Conservation/MWSLab-2023/scoutexports/DANgtscout-export-images.csv\")\n", "tasksdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "42eb20e5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "96a82fee", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "261aace7", "metadata": {}, "source": ["##### "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}