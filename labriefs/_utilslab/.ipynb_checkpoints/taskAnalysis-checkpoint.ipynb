{"cells": [{"cell_type": "code", "execution_count": 1, "id": "802f7ac7", "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 3, "id": "dc435446", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>name</th>\n", "      <th>displayName</th>\n", "      <th>taskType</th>\n", "      <th>assignee</th>\n", "      <th>orientation</th>\n", "      <th>randomized</th>\n", "      <th>createdByUserId</th>\n", "      <th>assigneeDiplayName</th>\n", "      <th>createdAt</th>\n", "      <th>updatedAt</th>\n", "      <th>progressAnnotation</th>\n", "      <th>progressGroundTruth</th>\n", "      <th>progressLineDivision</th>\n", "      <th>sequencingComplete</th>\n", "      <th>imageCount</th>\n", "      <th>tagIds</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'$oid': '651e795d899c400045a59f73'}</td>\n", "      <td>kaza_ml</td>\n", "      <td>KAZA_ML</td>\n", "      <td>ml</td>\n", "      <td>ml-v1</td>\n", "      <td>left</td>\n", "      <td>False</td>\n", "      <td>64253370dead810044978bc5</td>\n", "      <td>ML Config: V1 Classifier</td>\n", "      <td>1.696496e+12</td>\n", "      <td>1.698992e+12</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>[65254d1f62e3240045e45673]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>{'$oid': '652e6e67df32760044f21ca3'}</td>\n", "      <td>t_a</td>\n", "      <td>t_A</td>\n", "      <td>ml</td>\n", "      <td>ml-v2</td>\n", "      <td>right</td>\n", "      <td>False</td>\n", "      <td>651eb6c6899c400045a59f80</td>\n", "      <td>ML Config: V2 Classifier</td>\n", "      <td>1.697542e+12</td>\n", "      <td>1.698224e+12</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>{'$oid': '652e6e82df32760044f21ca5'}</td>\n", "      <td>t_b</td>\n", "      <td>t_B</td>\n", "      <td>user</td>\n", "      <td>651eb6c6899c400045a59f80</td>\n", "      <td>right</td>\n", "      <td>False</td>\n", "      <td>651eb6c6899c400045a59f80</td>\n", "      <td>simbamangu</td>\n", "      <td>1.697542e+12</td>\n", "      <td>1.698224e+12</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>{'$oid': '6530dcab05667e0045cadc40'}</td>\n", "      <td>dan05-l_58</td>\n", "      <td>DAN05-L_58</td>\n", "      <td>user</td>\n", "      <td>6524f99d62e3240045e45638</td>\n", "      <td>left</td>\n", "      <td>True</td>\n", "      <td>6523b8b118f3b3004425b07f</td>\n", "      <td>jmmbaga</td>\n", "      <td>1.697701e+12</td>\n", "      <td>1.698141e+12</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>[6525024e62e3240045e4563f]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>{'$oid': '6530dd1905667e0045cadc43'}</td>\n", "      <td>dan05-l_58_2</td>\n", "      <td>DAN05-L_58_2</td>\n", "      <td>user</td>\n", "      <td>6524fa4562e3240045e45639</td>\n", "      <td>left</td>\n", "      <td>True</td>\n", "      <td>6523b8b118f3b3004425b07f</td>\n", "      <td>hgeorge</td>\n", "      <td>1.697701e+12</td>\n", "      <td>1.698141e+12</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>[6525029762e3240045e45642]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                    _id          name   displayName taskType  \\\n", "0  {'$oid': '651e795d899c400045a59f73'}       kaza_ml       KAZA_ML       ml   \n", "1  {'$oid': '652e6e67df32760044f21ca3'}           t_a           t_A       ml   \n", "2  {'$oid': '652e6e82df32760044f21ca5'}           t_b           t_B     user   \n", "3  {'$oid': '6530dcab05667e0045cadc40'}    dan05-l_58    DAN05-L_58     user   \n", "4  {'$oid': '6530dd1905667e0045cadc43'}  dan05-l_58_2  DAN05-L_58_2     user   \n", "\n", "                   assignee orientation  randomized           createdByUserId  \\\n", "0                     ml-v1        left       False  64253370dead810044978bc5   \n", "1                     ml-v2       right       False  651eb6c6899c400045a59f80   \n", "2  651eb6c6899c400045a59f80       right       False  651eb6c6899c400045a59f80   \n", "3  6524f99d62e3240045e45638        left        True  6523b8b118f3b3004425b07f   \n", "4  6524fa4562e3240045e45639        left        True  6523b8b118f3b3004425b07f   \n", "\n", "         assigneeDiplayName     createdAt     updatedAt  progressAnnotation  \\\n", "0  ML Config: V1 Classifier  1.696496e+12  1.698992e+12                 1.0   \n", "1  ML Config: V2 Classifier  1.697542e+12  1.698224e+12                 1.0   \n", "2                simbamangu  1.697542e+12  1.698224e+12                 1.0   \n", "3                   jmmbaga  1.697701e+12  1.698141e+12                 1.0   \n", "4                   hgeorge  1.697701e+12  1.698141e+12                 1.0   \n", "\n", "   progressGroundTruth  progressLineDivision  sequencingComplete  imageCount  \\\n", "0                  0.0                     0               False           0   \n", "1                  1.0                     0                True           0   \n", "2                  1.0                     0                True           0   \n", "3                  0.0                     0               False           0   \n", "4                  0.0                     0               False           0   \n", "\n", "                       tagIds  \n", "0  [65254d1f62e3240045e45673]  \n", "1                        None  \n", "2                        None  \n", "3  [6525024e62e3240045e4563f]  \n", "4  [6525029762e3240045e45642]  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["taskdf = pd.read_json(\"/home/<USER>/Dropbox/Conservation/MWSLab-2023/labriefs/_media/taskdbdata.json\")\n", "taskdf.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "fc42dfcd", "metadata": {}, "outputs": [{"data": {"text/plain": ["(368, 17)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["taskdf.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "9da6ccbe", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['_id', 'name', 'displayName', 'taskType', 'assignee', 'orientation',\n", "       'randomized', 'createdByUserId', 'assigneeDiplayName', 'createdAt',\n", "       'updatedAt', 'progressAnnotation', 'progressGroundTruth',\n", "       'progressLineDivision', 'sequencingComplete', 'imageCount', 'tagIds'],\n", "      dtype='object')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["taskdf.columns"]}, {"cell_type": "code", "execution_count": 6, "id": "507f3216", "metadata": {}, "outputs": [], "source": ["selectask = taskdf[['displayName','assigneeDiplayName','imageCount']]"]}, {"cell_type": "code", "execution_count": 7, "id": "ff216f4b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>displayName</th>\n", "      <th>assigneeDiplayName</th>\n", "      <th>imageCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KAZA_ML</td>\n", "      <td>ML Config: V1 Classifier</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>t_A</td>\n", "      <td>ML Config: V2 Classifier</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>t_B</td>\n", "      <td>simbamangu</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_58</td>\n", "      <td>jmmbaga</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_58_2</td>\n", "      <td>hgeorge</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    displayName        assigneeDiplayName  imageCount\n", "0       KAZA_ML  ML Config: V1 Classifier           0\n", "1           t_A  ML Config: V2 Classifier           0\n", "2           t_B                simbamangu           0\n", "3    DAN05-L_58                   jmmbaga           0\n", "4  DAN05-L_58_2                   hgeorge           0"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["selectask.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "f4471172", "metadata": {}, "outputs": [], "source": ["#define the wildcard pattern\n", "pattern = \"DAN\""]}, {"cell_type": "code", "execution_count": 9, "id": "cf80070e", "metadata": {}, "outputs": [], "source": ["# Filter the DataFrame based on the pattern\n", "filtered_df = selectask[selectask['displayName'].str.contains(pattern, case=False, na=False, regex=True)]"]}, {"cell_type": "code", "execution_count": 10, "id": "7954d0fc", "metadata": {}, "outputs": [{"data": {"text/plain": ["(151, 3)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_df.shape"]}, {"cell_type": "code", "execution_count": 11, "id": "dfff1f75", "metadata": {}, "outputs": [], "source": ["filtered_df.to_csv(\"/home/<USER>/Dropbox/Conservation/MWSLab-2023/labriefs/_media/DANannotators.csv\")"]}, {"cell_type": "code", "execution_count": 12, "id": "15b6c544", "metadata": {}, "outputs": [], "source": ["#define the wildcard pattern\n", "pattern2 = \"SH\""]}, {"cell_type": "code", "execution_count": 13, "id": "4e549e5e", "metadata": {}, "outputs": [], "source": ["# Filter the DataFrame based on the pattern\n", "filtered_df2 = selectask[selectask['displayName'].str.contains(pattern2, case=False, na=False, regex=True)]"]}, {"cell_type": "code", "execution_count": 14, "id": "58d06286", "metadata": {}, "outputs": [{"data": {"text/plain": ["(137, 3)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_df2.shape"]}, {"cell_type": "code", "execution_count": 14, "id": "38d09d22", "metadata": {}, "outputs": [], "source": ["filtered_df2.to_csv(\"/home/<USER>/Dropbox/Conservation/MWSLab-2023/labriefs/_media/SHannotators.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "cb6ec764", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}