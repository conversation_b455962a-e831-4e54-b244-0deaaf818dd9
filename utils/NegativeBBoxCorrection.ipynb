{"cells": [{"cell_type": "markdown", "id": "c12ccba4-20c1-467a-bba7-a8ff3298583f", "metadata": {}, "source": ["Negative bbox correction\n", "\n", "\n", "I'd write a function to deal with general condition where either w or h are negative:\n", "```\n", "x = x + w*(w<1)\n", "y = y + h*(y<1)\n", "w = abs(w)\n", "h = abs(h)\n", "```"]}, {"cell_type": "code", "execution_count": 1, "id": "ac49b2c9-bd38-4bf7-b4a3-ceccc42baa0c", "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 311, "id": "df82edef-10fc-4261-8e42-479d1510ef0f", "metadata": {}, "outputs": [], "source": ["coco_file_path = \"/Users/<USER>/Dropbox/Conservation/MWS_LabArusha/annotations/Pregroundtruth/dblock2024/cvatannotations_240525/R600_25coco.json\""]}, {"cell_type": "code", "execution_count": 312, "id": "b89099d8-9cbd-4139-a522-82e2f3dde075", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'annotations' section to a DataFrame\n", "annotationsdf = pd.DataFrame(coco_data['annotations'])"]}, {"cell_type": "code", "execution_count": 313, "id": "b50025bc-4960-4b2e-9a02-654a88996f7a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>[]</td>\n", "      <td>1864.641086</td>\n", "      <td>[5325.063642675307, 3378.8474890071748, -29.38...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>[]</td>\n", "      <td>2527.624583</td>\n", "      <td>[5377.949951198898, 3696.165340148718, -35.257...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>[]</td>\n", "      <td>3585.635748</td>\n", "      <td>[5453.166034432449, 3530.4549067748007, 51.711...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>[]</td>\n", "      <td>4022.099884</td>\n", "      <td>[5628.27847821056, 3502.2488755622194, 65.8140...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>[]</td>\n", "      <td>2689.226811</td>\n", "      <td>[5640.030991215802, 3323.610677882536, -38.783...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id image_id  category_id segmentation         area  \\\n", "0   1     None            2           []  1864.641086   \n", "1   2     None            2           []  2527.624583   \n", "2   3     None            2           []  3585.635748   \n", "3   4     None            2           []  4022.099884   \n", "4   5     None            2           []  2689.226811   \n", "\n", "                                                bbox  iscrowd  \n", "0  [5325.063642675307, 3378.8474890071748, -29.38...        0  \n", "1  [5377.949951198898, 3696.165340148718, -35.257...        0  \n", "2  [5453.166034432449, 3530.4549067748007, 51.711...        0  \n", "3  [5628.27847821056, 3502.2488755622194, 65.8140...        0  \n", "4  [5640.030991215802, 3323.610677882536, -38.783...        0  "]}, "execution_count": 313, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 314, "id": "36042298-170b-4cdc-96f7-7d2bcc11eed6", "metadata": {}, "outputs": [{"data": {"text/plain": ["(271, 7)"]}, "execution_count": 314, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.shape"]}, {"cell_type": "code", "execution_count": 6, "id": "eeb5f4c8-00a6-41c6-b610-ee5080635786", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 [3767.580056131111, 1831.8267558296664, -27.19573126, 38.85104466234712]\n", "1 [3739.4130487509096, 1828.9129274799905, -39.82232078, 52.448910294168606]\n", "2 [3782.149197879492, 1789.0906067010849, -42.73614913, -27.19573126]\n", "3 [3830.713003707425, 1884.275666123835, -47.59252971, -29.1382835]\n", "4 [3953.093794393819, 1756.0672187380897, -66.04677593, -31.08083573]\n", "5 [6418.122826357527, 7.974680843497742, 32.89555847942819, 21.431954766900184]\n", "6 [4108.740094493091, 3475.0125873415327, -257.3895978, -81.5876461]\n", "7 [4967.509178775447, 3187.1624384556103, 350.43792849530973, 274.58093787589814]\n", "8 [5297.647349217675, 3143.713747, -193.3819057, -307.7015958]\n", "9 [6374.958722666631, 3088.987556198918, 338.68543699089383, 153.85079787598963]\n", "10 [6751.394760808716, 3022.746240446756, 253.21277150423293, 209.4080304423192]\n", "11 [1399.9028086597043, 2093.039071374632, 40.406877678525326, 36.18526359270925]\n", "12 [1176.6569708405057, 4030.046136910776, -302.0685469, 186.48604822312865]\n", "13 [1564.198289804195, 3976.625654346859, -114.6112171, 150.54863268012988]\n"]}], "source": ["for idx, row in annotationsdf.iterrows():\n", "    print(idx, row[\"bbox\"])"]}, {"cell_type": "code", "execution_count": 8, "id": "581f9439-e90f-41b5-ada1-28df42bc458d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 [3794.775787391111, 1831.8267558296664, 27.19573126, 38.85104466234712]\n", "1 [3779.23536953091, 1828.9129274799905, 39.82232078, 52.448910294168606]\n", "2 [3824.885347009492, 1816.2863379610849, 42.73614913, 27.19573126]\n", "3 [3878.3055334174246, 1913.413949623835, 47.59252971, 29.1382835]\n", "4 [4019.140570323819, 1787.1480544680896, 66.04677593, 31.08083573]\n", "5 [6418.122826357527, 7.974680843497742, 32.89555847942819, 21.431954766900184]\n", "6 [4366.1296922930915, 3556.6002334415325, 257.3895978, 81.5876461]\n", "7 [4967.509178775447, 3187.1624384556103, 350.43792849530973, 274.58093787589814]\n", "8 [5491.029254917675, 3451.4153428, 193.3819057, 307.7015958]\n", "9 [6374.958722666631, 3088.987556198918, 338.68543699089383, 153.85079787598963]\n", "10 [6751.394760808716, 3022.746240446756, 253.21277150423293, 209.4080304423192]\n", "11 [1399.9028086597043, 2093.039071374632, 40.406877678525326, 36.18526359270925]\n", "12 [1478.7255177405057, 4030.046136910776, 302.0685469, 186.48604822312865]\n", "13 [1678.809506904195, 3976.625654346859, 114.6112171, 150.54863268012988]\n"]}], "source": ["for idx, row in annotationsdf.iterrows():\n", "    print(idx, row[\"bbox\"])"]}, {"cell_type": "code", "execution_count": 315, "id": "8834b20e-a520-478c-8d8c-88d88678d59b", "metadata": {}, "outputs": [], "source": ["for idx, row in annotationsdf.iterrows():\n", "    for i, item in enumerate(row[\"bbox\"]): \n", "       # print(i, item)\n", "        \n", "        if(i==0):\n", "            w = item\n", "        if(i==1):\n", "            h = item\n", "        if (i == 2 and item < 1):\n", "            x = abs(item)\n", "            w = w + x\n", "        elif (i==2):\n", "            x = item\n", "        if (i==3 and item <1):\n", "            y = abs(item)\n", "            h = h + y\n", "        elif (i==3):\n", "            y = item\n", "        \n", "    annotationsdf.at[idx, \"bbox\"] = [w, h, x, y]   "]}, {"cell_type": "code", "execution_count": 316, "id": "088ef846-4370-42df-a91e-2cae5243a0ba", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>[]</td>\n", "      <td>1864.641086</td>\n", "      <td>[5354.444925188413, 3378.8474890071748, 29.381...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>[]</td>\n", "      <td>2527.624583</td>\n", "      <td>[5413.207490214625, 3696.165340148718, 35.2575...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>[]</td>\n", "      <td>3585.635748</td>\n", "      <td>[5453.166034432449, 3530.4549067748007, 51.711...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>[]</td>\n", "      <td>4022.099884</td>\n", "      <td>[5628.27847821056, 3502.2488755622194, 65.8140...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>None</td>\n", "      <td>2</td>\n", "      <td>[]</td>\n", "      <td>2689.226811</td>\n", "      <td>[5678.814284133102, 3323.610677882536, 38.7832...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id image_id  category_id segmentation         area  \\\n", "0   1     None            2           []  1864.641086   \n", "1   2     None            2           []  2527.624583   \n", "2   3     None            2           []  3585.635748   \n", "3   4     None            2           []  4022.099884   \n", "4   5     None            2           []  2689.226811   \n", "\n", "                                                bbox  iscrowd  \n", "0  [5354.444925188413, 3378.8474890071748, 29.381...        0  \n", "1  [5413.207490214625, 3696.165340148718, 35.2575...        0  \n", "2  [5453.166034432449, 3530.4549067748007, 51.711...        0  \n", "3  [5628.27847821056, 3502.2488755622194, 65.8140...        0  \n", "4  [5678.814284133102, 3323.610677882536, 38.7832...        0  "]}, "execution_count": 316, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 317, "id": "ab059db3-f255-47b1-98c0-3017fdd87484", "metadata": {}, "outputs": [], "source": ["# Save the COCO formatted data to a JSON file\n", "output_json_path = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023/utils/CVATdata/CVAT_2024/cvatannotations_240525/R600_25coco.json\""]}, {"cell_type": "code", "execution_count": 318, "id": "81e9b233-12ef-4a02-91bb-33a091b01c7f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["COCO annotations updated successfully!\n"]}], "source": ["# Step 3: Update the `annotations` field in the COCO JSON\n", "# Create a dictionary from DataFrame for fast lookup based on `id`\n", "modified_annotations_dict = annotationsdf.set_index('id').to_dict(orient='index')\n", "\n", "# Go through the existing annotations and update them if they exist in the modified DataFrame\n", "for annotation in coco_data['annotations']:\n", "    annotation_id = annotation['id']\n", "    if annotation_id in modified_annotations_dict:\n", "        # Update the annotation fields\n", "        for key, value in modified_annotations_dict[annotation_id].items():\n", "            annotation[key] = value\n", "\n", "# Step 4: Save the updated COCO JSON file\n", "with open(output_json_path, 'w') as f:\n", "    json.dump(coco_data, f, indent=4)\n", "\n", "print(\"COCO annotations updated successfully!\")"]}, {"cell_type": "code", "execution_count": null, "id": "8fd472a2-dbb5-44cf-81b4-a3429bb1a2d3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 285, "id": "9b6a815c-d27c-43b2-ba69-bfbcd4892c6b", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(output_json_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'annotations' section to a DataFrame\n", "annotationsdf2 = pd.DataFrame(coco_data['annotations'])"]}, {"cell_type": "code", "execution_count": 286, "id": "9385a216-3ad1-4ded-8f68-7ee4f1345c1d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>1382.306174</td>\n", "      <td>[4730.416627123469, 1410.5343076777071, 49.881...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>2088.818219</td>\n", "      <td>[6528.91714909367, 3026.1364714814463, 44.3389...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>[]</td>\n", "      <td>820.442078</td>\n", "      <td>[702.8002777134923, 439.54398639606376, 38.783...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>[]</td>\n", "      <td>795.580197</td>\n", "      <td>[675.7694978014349, 445.4202428986849, 42.3090...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>[]</td>\n", "      <td>857.734900</td>\n", "      <td>[758.0370888381314, 458.3480072044515, 31.7317...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation         area  \\\n", "0   1         1            3           []  1382.306174   \n", "1   2         1            3           []  2088.818219   \n", "2   3         2            4           []   820.442078   \n", "3   4         2            4           []   795.580197   \n", "4   5         2            4           []   857.734900   \n", "\n", "                                                bbox  iscrowd  \n", "0  [4730.416627123469, 1410.5343076777071, 49.881...        0  \n", "1  [6528.91714909367, 3026.1364714814463, 44.3389...        0  \n", "2  [702.8002777134923, 439.54398639606376, 38.783...        0  \n", "3  [675.7694978014349, 445.4202428986849, 42.3090...        0  \n", "4  [758.0370888381314, 458.3480072044515, 31.7317...        0  "]}, "execution_count": 286, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf2.head()"]}, {"cell_type": "code", "execution_count": null, "id": "0efbfaeb-bd01-4034-9305-23d26436341d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}