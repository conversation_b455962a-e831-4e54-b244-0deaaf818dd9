{"cells": [{"cell_type": "markdown", "id": "ff9ed367", "metadata": {}, "source": ["Divide stratum to left and right"]}, {"cell_type": "code", "execution_count": 1, "id": "8daf5dd7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "b489dc6c-8845-4df1-9680-2fd060786fc4", "metadata": {}, "outputs": [], "source": ["coco_file_path = \"/Users/<USER>/Dropbox/Conservation/MWS_LabArusha/annotations/exports/MTleft.json\""]}, {"cell_type": "code", "execution_count": 25, "id": "23b685d4", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "images_df = pd.DataFrame(coco_data['images'])\n"]}, {"cell_type": "code", "execution_count": 4, "id": "4a1f8fd3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(296, 6)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "8e9230e2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-070510_M0801395.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053746_M0808781.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054013_M0808854.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054205_M0808910.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   1   7008    4672  KES22_WOT-L_20220909A-070510_M0801395.jpg        1   \n", "1   2   7008    4672  KES22_WOT-L_20220909A-053748_M0808782.jpg        1   \n", "2   3   7008    4672  KES22_WOT-L_20220909A-053746_M0808781.jpg        1   \n", "3   4   7008    4672  KES22_WOT-L_20220909A-054013_M0808854.jpg        1   \n", "4   5   7008    4672  KES22_WOT-L_20220909A-054205_M0808910.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-09  \n", "1    2022-09-09  \n", "2    2022-09-09  \n", "3    2022-09-09  \n", "4    2022-09-09  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "60719a18", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "id": "28706da0", "metadata": {}, "outputs": [], "source": ["pattern1 = \"KES22_WOT-L_\""]}, {"cell_type": "code", "execution_count": 7, "id": "4d9a85b9", "metadata": {}, "outputs": [], "source": ["pattern2 = \"KES22_LJB-L_\""]}, {"cell_type": "code", "execution_count": 8, "id": "4e4444a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(59, 6)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "filtered_df = images_df[images_df['file_name'].str.contains(pattern1, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "filtered_df.shape"]}, {"cell_type": "code", "execution_count": 9, "id": "5c0e262a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(83, 6)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_dfl = images_df[images_df['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "filtered_dfl.shape"]}, {"cell_type": "code", "execution_count": 11, "id": "cc84e412", "metadata": {}, "outputs": [], "source": ["MTleftposve = pd.concat([filtered_df,filtered_dfl],ignore_index=True)"]}, {"cell_type": "code", "execution_count": 12, "id": "71ae4baa", "metadata": {}, "outputs": [{"data": {"text/plain": ["(142, 6)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftposve.shape"]}, {"cell_type": "code", "execution_count": 14, "id": "6179df13-2124-4328-8245-d000ee2c1bf6", "metadata": {}, "outputs": [], "source": ["MTleftposve.to_csv(\"MTleftimages.csv\")"]}, {"cell_type": "code", "execution_count": 13, "id": "060a581a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-070510_M0801395.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053746_M0808781.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054013_M0808854.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054205_M0808910.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   1   7008    4672  KES22_WOT-L_20220909A-070510_M0801395.jpg        1   \n", "1   2   7008    4672  KES22_WOT-L_20220909A-053748_M0808782.jpg        1   \n", "2   3   7008    4672  KES22_WOT-L_20220909A-053746_M0808781.jpg        1   \n", "3   4   7008    4672  KES22_WOT-L_20220909A-054013_M0808854.jpg        1   \n", "4   5   7008    4672  KES22_WOT-L_20220909A-054205_M0808910.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-09  \n", "1    2022-09-09  \n", "2    2022-09-09  \n", "3    2022-09-09  \n", "4    2022-09-09  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftposve.head()"]}, {"cell_type": "code", "execution_count": 13, "id": "594ad7b9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>238</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-080514_M1007359.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>138</th>\n", "      <td>239</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-080404_M1007324.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>139</th>\n", "      <td>289</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-091512_M1009451.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>140</th>\n", "      <td>290</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-091434_M1009432.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>141</th>\n", "      <td>291</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-091442_M1009436.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "137  238   7008    4672  KES22_LJB-L_20220908A-080514_M1007359.jpg        1   \n", "138  239   7008    4672  KES22_LJB-L_20220908A-080404_M1007324.jpg        1   \n", "139  289   7008    4672  KES22_LJB-L_20220908A-091512_M1009451.jpg        1   \n", "140  290   7008    4672  KES22_LJB-L_20220908A-091434_M1009432.jpg        1   \n", "141  291   7008    4672  KES22_LJB-L_20220908A-091442_M1009436.jpg        1   \n", "\n", "    date_captured  \n", "137    2022-09-09  \n", "138    2022-09-09  \n", "139    2022-09-09  \n", "140    2022-09-09  \n", "141    2022-09-09  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftposve.tail()"]}, {"cell_type": "code", "execution_count": 15, "id": "164fc814", "metadata": {}, "outputs": [], "source": ["MTleftsorted = MTleftposve.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 16, "id": "932d181b-abf7-45cb-b26d-c73c7d9163f0", "metadata": {}, "outputs": [], "source": ["MTleftsorted.to_csv(\"MTLeftimages.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 45, "id": "7bc344e3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>147</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072836_M1006264.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>148</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072838_M1006265.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>149</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072840_M1006266.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>146</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072842_M1006267.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>150</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-073213_M1006372.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>155</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-073215_M1006373.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>154</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-073247_M1006389.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>151</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-073249_M1006390.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>153</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-073341_M1006416.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     id  width  height                                  file_name  license  \\\n", "60  147   7008    4672  KES22_LJB-L_20220908A-072836_M1006264.jpg        1   \n", "61  148   7008    4672  KES22_LJB-L_20220908A-072838_M1006265.jpg        1   \n", "62  149   7008    4672  KES22_LJB-L_20220908A-072840_M1006266.jpg        1   \n", "59  146   7008    4672  KES22_LJB-L_20220908A-072842_M1006267.jpg        1   \n", "63  150   7008    4672  KES22_LJB-L_20220908A-073213_M1006372.jpg        1   \n", "68  155   7008    4672  KES22_LJB-L_20220908A-073215_M1006373.jpg        1   \n", "67  154   7008    4672  KES22_LJB-L_20220908A-073247_M1006389.jpg        1   \n", "64  151   7008    4672  KES22_LJB-L_20220908A-073249_M1006390.jpg        1   \n", "66  153   7008    4672  KES22_LJB-L_20220908A-073341_M1006416.jpg        1   \n", "\n", "   date_captured  \n", "60    2022-09-09  \n", "61    2022-09-09  \n", "62    2022-09-09  \n", "59    2022-09-09  \n", "63    2022-09-09  \n", "68    2022-09-09  \n", "67    2022-09-09  \n", "64    2022-09-09  \n", "66    2022-09-09  "]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftsorted.head(9)"]}, {"cell_type": "code", "execution_count": 36, "id": "97e19225-b15b-427e-a15f-70d7d814cf48", "metadata": {}, "outputs": [], "source": ["MTleftsorted[\"file_name\"].to_csv(\"MTleftpositive.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 15, "id": "07d3b903", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>ImageFilename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070352_M0801356.jpg</td>\n", "      <td>654b3d440ff5c4004c9b6b56</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070504_M0801392.jpg</td>\n", "      <td>654b3d8e0ff5c4004c9b6b72</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070500_M0801390.jpg</td>\n", "      <td>654b3dab0ff5c4004c9b6b7d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070342_M0801351.jpg</td>\n", "      <td>654b3e2c0ff5c4004c9b6bae</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070354_M0801357.jpg</td>\n", "      <td>654b3e990ff5c4004c9b6bd7</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Task Name                   Task ID  \\\n", "0  MT02-L_46  654b524e47d0660045090326   \n", "1  MT02-L_46  654b524e47d0660045090326   \n", "2  MT02-L_46  654b524e47d0660045090326   \n", "3  MT02-L_46  654b524e47d0660045090326   \n", "4  MT02-L_46  654b524e47d0660045090326   \n", "\n", "                               ImageFilename                  Image ID  \\\n", "0  KES22_WOT-L_20220909A-070352_M0801356.jpg  654b3d440ff5c4004c9b6b56   \n", "1  KES22_WOT-L_20220909A-070504_M0801392.jpg  654b3d8e0ff5c4004c9b6b72   \n", "2  KES22_WOT-L_20220909A-070500_M0801390.jpg  654b3dab0ff5c4004c9b6b7d   \n", "3  KES22_WOT-L_20220909A-070342_M0801351.jpg  654b3e2c0ff5c4004c9b6bae   \n", "4  KES22_WOT-L_20220909A-070354_M0801357.jpg  654b3e990ff5c4004c9b6bd7   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                False   \n", "1          4672         7008             NaN                False   \n", "2          4672         7008             NaN                False   \n", "3          4672         7008             NaN                False   \n", "4          4672         7008             NaN                False   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["#imgpath = \"/home/<USER>/Dropbox/Conservation/MWS_LabArusha/annotations/exports/MTscout-export-images.csv\"\n", "imgpath = \"/Users/<USER>/Dropbox/Conservation/MWS_LabArusha/annotations/exports/MTscout-export-images.csv\"\n", "mtimgs = pd.read_csv(imgpath)\n", "mtimgs.head()"]}, {"cell_type": "code", "execution_count": 16, "id": "87ff32c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(9278, 11)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimgs.shape"]}, {"cell_type": "code", "execution_count": 17, "id": "fbff7df6", "metadata": {}, "outputs": [{"data": {"text/plain": ["8677"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimgs[\"ImageFilename\"].nunique()"]}, {"cell_type": "code", "execution_count": 18, "id": "6b578304", "metadata": {}, "outputs": [], "source": ["mtimgsunique = mtimgs[\"ImageFilename\"].unique()"]}, {"cell_type": "code", "execution_count": 19, "id": "7675cd62", "metadata": {}, "outputs": [{"data": {"text/plain": ["8677"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["len(mtimgsunique)"]}, {"cell_type": "code", "execution_count": 20, "id": "c1e7f0f8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_WOT-L_20220909A-070352_M0801356.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_WOT-L_20220909A-070504_M0801392.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_WOT-L_20220909A-070500_M0801390.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_WOT-L_20220909A-070342_M0801351.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_WOT-L_20220909A-070354_M0801357.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   file_name\n", "0  KES22_WOT-L_20220909A-070352_M0801356.jpg\n", "1  KES22_WOT-L_20220909A-070504_M0801392.jpg\n", "2  KES22_WOT-L_20220909A-070500_M0801390.jpg\n", "3  KES22_WOT-L_20220909A-070342_M0801351.jpg\n", "4  KES22_WOT-L_20220909A-070354_M0801357.jpg"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimgsunique = pd.DataFrame(mtimgsunique,columns=[\"file_name\"])\n", "mtimgsunique.head()"]}, {"cell_type": "code", "execution_count": 21, "id": "b6debf9d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2464, 1)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimgsuniqueleft = mtimgsunique[mtimgsunique['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "mtimgsuniqueleft.shape"]}, {"cell_type": "code", "execution_count": 22, "id": "67950dd6", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1912, 1)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimgsuniqueleft2 = mtimgsunique[mtimgsunique['file_name'].str.contains(pattern1, case=False, na=False, regex=True)]\n", "# Print the resulting filtered DataFrame\n", "mtimgsuniqueleft2.shape"]}, {"cell_type": "code", "execution_count": 28, "id": "0f086fec", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1907</th>\n", "      <td>KES22_WOT-L_20220909A-073206_M0802200.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1908</th>\n", "      <td>KES22_WOT-L_20220909A-073030_M0802152.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1909</th>\n", "      <td>KES22_WOT-L_20220909A-073106_M0802170.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1910</th>\n", "      <td>KES22_WOT-L_20220909A-073042_M0802158.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1911</th>\n", "      <td>KES22_WOT-L_20220909A-073110_M0802172.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      file_name\n", "1907  KES22_WOT-L_20220909A-073206_M0802200.jpg\n", "1908  KES22_WOT-L_20220909A-073030_M0802152.jpg\n", "1909  KES22_WOT-L_20220909A-073106_M0802170.jpg\n", "1910  KES22_WOT-L_20220909A-073042_M0802158.jpg\n", "1911  KES22_WOT-L_20220909A-073110_M0802172.jpg"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimgsuniqueleft2.tail()"]}, {"cell_type": "code", "execution_count": 23, "id": "6ff0e097", "metadata": {}, "outputs": [], "source": ["MTleftotal = pd.concat([mtimgsuniqueleft,mtimgsuniqueleft2],ignore_index=True)"]}, {"cell_type": "code", "execution_count": 24, "id": "68157f59-9f0d-49e1-ad22-c1fef15e29a9", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4376, 1)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftotal.shape"]}, {"cell_type": "code", "execution_count": 31, "id": "929403df-0066-490d-b8ef-5c1435665e37", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_LJB-L_20220908A-072752_M1006242.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_LJB-L_20220908A-072828_M1006260.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_LJB-L_20220908A-072754_M1006243.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_LJB-L_20220908A-072808_M1006250.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_LJB-L_20220908A-072832_M1006262.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   file_name\n", "0  KES22_LJB-L_20220908A-072752_M1006242.jpg\n", "1  KES22_LJB-L_20220908A-072828_M1006260.jpg\n", "2  KES22_LJB-L_20220908A-072754_M1006243.jpg\n", "3  KES22_LJB-L_20220908A-072808_M1006250.jpg\n", "4  KES22_LJB-L_20220908A-072832_M1006262.jpg"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftotal.head()"]}, {"cell_type": "code", "execution_count": 32, "id": "d6ef5859-5519-42a5-b3da-d836cb433c7e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4371</th>\n", "      <td>KES22_WOT-L_20220909A-073206_M0802200.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4372</th>\n", "      <td>KES22_WOT-L_20220909A-073030_M0802152.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4373</th>\n", "      <td>KES22_WOT-L_20220909A-073106_M0802170.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4374</th>\n", "      <td>KES22_WOT-L_20220909A-073042_M0802158.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4375</th>\n", "      <td>KES22_WOT-L_20220909A-073110_M0802172.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      file_name\n", "4371  KES22_WOT-L_20220909A-073206_M0802200.jpg\n", "4372  KES22_WOT-L_20220909A-073030_M0802152.jpg\n", "4373  KES22_WOT-L_20220909A-073106_M0802170.jpg\n", "4374  KES22_WOT-L_20220909A-073042_M0802158.jpg\n", "4375  KES22_WOT-L_20220909A-073110_M0802172.jpg"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftotal.tail()"]}, {"cell_type": "code", "execution_count": 25, "id": "247cec3a-dece-43d8-ab76-c7ff9b1a5244", "metadata": {}, "outputs": [], "source": ["MTleftotalsorted = MTleftotal.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 26, "id": "6cc04368-35fb-4d16-a052-218249487290", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>KES22_LJB-L_20220908A-072748_M1006240.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>KES22_LJB-L_20220908A-072750_M1006241.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_LJB-L_20220908A-072752_M1006242.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_LJB-L_20220908A-072754_M1006243.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>KES22_LJB-L_20220908A-072756_M1006244.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                    file_name\n", "18  KES22_LJB-L_20220908A-072748_M1006240.jpg\n", "14  KES22_LJB-L_20220908A-072750_M1006241.jpg\n", "0   KES22_LJB-L_20220908A-072752_M1006242.jpg\n", "2   KES22_LJB-L_20220908A-072754_M1006243.jpg\n", "7   KES22_LJB-L_20220908A-072756_M1006244.jpg"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftotalsorted.head()"]}, {"cell_type": "code", "execution_count": 27, "id": "2b99b790-5318-4b36-a53a-16d3756b4037", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in MTleftsorted[\"file_name\"]:\n", "    if item in MTleftotalsorted[\"file_name\"].values:\n", "        index_in_pos = MTleftotalsorted[\"file_name\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(MTleftotalsorted[\"file_name\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(MTleftotalsorted[\"file_name\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 28, "id": "2be9136b-e16e-42d9-b00a-93ec347ddb84", "metadata": {}, "outputs": [{"data": {"text/plain": ["426"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 29, "id": "d7696c45-bfd9-436c-afda-4239c5007d50", "metadata": {}, "outputs": [{"data": {"text/plain": ["317"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 30, "id": "008a9c33-b2e3-4b6f-875e-1a9b32e5f81b", "metadata": {}, "outputs": [], "source": ["cvatimg = pd.DataFrame(fin,columns=[\"cvatfilename\"])"]}, {"cell_type": "code", "execution_count": 31, "id": "8a77236e-0e42-482c-892a-0504f417334f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>246</th>\n", "      <td>KES22_LJB-L_20220908A-072834_M1006263.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>306</th>\n", "      <td>KES22_LJB-L_20220908A-072836_M1006264.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>156</th>\n", "      <td>KES22_LJB-L_20220908A-072838_M1006265.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>KES22_LJB-L_20220908A-072840_M1006266.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>KES22_LJB-L_20220908A-072842_M1006267.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  c<PERSON><PERSON><PERSON><PERSON>\n", "246  KES22_LJB-L_20220908A-072834_M1006263.jpg\n", "306  KES22_LJB-L_20220908A-072836_M1006264.jpg\n", "156  KES22_LJB-L_20220908A-072838_M1006265.jpg\n", "67   KES22_LJB-L_20220908A-072840_M1006266.jpg\n", "34   KES22_LJB-L_20220908A-072842_M1006267.jpg"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin = cvatimg.sort_values(by=[\"cvatfilename\"],ascending=True)\n", "cvatfin.head()"]}, {"cell_type": "code", "execution_count": 32, "id": "ffdef2e9-a764-40b6-9132-94f29afa7e82", "metadata": {}, "outputs": [{"data": {"text/plain": ["(317, 1)"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin.shape"]}, {"cell_type": "code", "execution_count": 33, "id": "afc94256-61ff-436c-a8a0-976acc8ab884", "metadata": {}, "outputs": [], "source": ["cvatfin.to_csv(\"MTleftcvat.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "c2d70828-12c5-45b1-a911-9dc64154f5dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c2687fbc-6339-428e-9d54-751e512b042f", "metadata": {}, "outputs": [], "source": ["#MT Right"]}, {"cell_type": "code", "execution_count": null, "id": "8ddae76b-c500-4d2d-958d-e20286f9ee1e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 17, "id": "1ca074ad", "metadata": {}, "outputs": [], "source": ["pattern3 = \"KES22_WOT-R_\""]}, {"cell_type": "code", "execution_count": 18, "id": "b6eae4a6", "metadata": {}, "outputs": [], "source": ["pattern4 = \"KES22_LJB-R_\""]}, {"cell_type": "code", "execution_count": 19, "id": "1e9aefc9", "metadata": {}, "outputs": [{"data": {"text/plain": ["(91, 6)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "filtered_df3 = images_df[images_df['file_name'].str.contains(pattern3, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "filtered_df3.shape"]}, {"cell_type": "code", "execution_count": 20, "id": "982446e4", "metadata": {}, "outputs": [{"data": {"text/plain": ["(63, 6)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_dfr = images_df[images_df['file_name'].str.contains(pattern4, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "filtered_dfr.shape"]}, {"cell_type": "code", "execution_count": 21, "id": "752270a3", "metadata": {}, "outputs": [], "source": ["MTrightposve = pd.concat([filtered_df3,filtered_dfr],ignore_index=True)"]}, {"cell_type": "code", "execution_count": 22, "id": "d84c0e4b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(154, 6)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["MTrightposve.shape"]}, {"cell_type": "code", "execution_count": 56, "id": "9824773b-5662-4fa8-b62f-2b9a3d5a2e81", "metadata": {}, "outputs": [], "source": ["MTrightposve.to_csv(\"MTrightimages.csv\")"]}, {"cell_type": "code", "execution_count": 23, "id": "3ad7fa26", "metadata": {}, "outputs": [], "source": ["MTrightsorted = MTrightposve.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 24, "id": "786e514b-1138-4c25-a57f-d0dd97b46ca2", "metadata": {}, "outputs": [], "source": ["MTrightsorted.to_csv(\"MTrightimagespos.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 41, "id": "5fd9fbb4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>223</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-072747_M0907060.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>229</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-073432_M0907262.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>225</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-073434_M0907263.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>224</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-073518_M0907285.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>228</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-073520_M0907286.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     id  width  height                                  file_name  license  \\\n", "91  223   7008    4672  KES22_LJB-R_20220908A-072747_M0907060.jpg        1   \n", "97  229   7008    4672  KES22_LJB-R_20220908A-073432_M0907262.jpg        1   \n", "93  225   7008    4672  KES22_LJB-R_20220908A-073434_M0907263.jpg        1   \n", "92  224   7008    4672  KES22_LJB-R_20220908A-073518_M0907285.jpg        1   \n", "96  228   7008    4672  KES22_LJB-R_20220908A-073520_M0907286.jpg        1   \n", "\n", "   date_captured  \n", "91    2022-09-09  \n", "97    2022-09-09  \n", "93    2022-09-09  \n", "92    2022-09-09  \n", "96    2022-09-09  "]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["MTrightsorted.head()"]}, {"cell_type": "code", "execution_count": 42, "id": "64d5e89c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2025, 1)"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimgsuniqueright = mtimgsunique[mtimgsunique['file_name'].str.contains(pattern3, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "mtimgsuniqueright.shape"]}, {"cell_type": "code", "execution_count": 43, "id": "68d8c374", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2276, 1)"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimgsuniqueright2 = mtimgsunique[mtimgsunique['file_name'].str.contains(pattern4, case=False, na=False, regex=True)]\n", "# Print the resulting filtered DataFrame\n", "mtimgsuniqueright2.shape"]}, {"cell_type": "code", "execution_count": 44, "id": "a9b64463", "metadata": {}, "outputs": [], "source": ["MTrightotal = pd.concat([mtimgsuniqueright,mtimgsuniqueright2],ignore_index=True)"]}, {"cell_type": "code", "execution_count": 47, "id": "bccea1bf-8ba4-4731-b395-f9884d37d808", "metadata": {}, "outputs": [], "source": ["MTrightotalsorted = MTrightotal.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 48, "id": "557fa857-7da4-489d-9f5c-8db5342e9fac", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2052</th>\n", "      <td>KES22_LJB-R_20220908A-072747_M0907060.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2068</th>\n", "      <td>KES22_LJB-R_20220908A-072749_M0907061.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025</th>\n", "      <td>KES22_LJB-R_20220908A-072751_M0907062.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2045</th>\n", "      <td>KES22_LJB-R_20220908A-072753_M0907063.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2065</th>\n", "      <td>KES22_LJB-R_20220908A-072755_M0907064.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      file_name\n", "2052  KES22_LJB-R_20220908A-072747_M0907060.jpg\n", "2068  KES22_LJB-R_20220908A-072749_M0907061.jpg\n", "2025  KES22_LJB-R_20220908A-072751_M0907062.jpg\n", "2045  KES22_LJB-R_20220908A-072753_M0907063.jpg\n", "2065  KES22_LJB-R_20220908A-072755_M0907064.jpg"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["MTrightotalsorted.head()"]}, {"cell_type": "code", "execution_count": 49, "id": "b879b12b-d248-4bc7-baa7-f9119aee8f71", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in MTrightsorted[\"file_name\"]:\n", "    if item in MTrightotalsorted[\"file_name\"].values:\n", "        index_in_pos = MTrightotalsorted[\"file_name\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(MTrightotalsorted[\"file_name\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(MTrightotalsorted[\"file_name\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 50, "id": "db6f4e5d-fbb6-46a9-9bee-8476dbe5cbbb", "metadata": {}, "outputs": [{"data": {"text/plain": ["462"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 51, "id": "7badf276-6239-486d-b2ba-9cb2a2c3bcef", "metadata": {}, "outputs": [{"data": {"text/plain": ["364"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 52, "id": "82318eab-c30a-427e-96de-befaea605c12", "metadata": {}, "outputs": [], "source": ["cvatimg = pd.DataFrame(fin,columns=[\"cvatfilename\"])"]}, {"cell_type": "code", "execution_count": 53, "id": "efc73194-3fdb-436e-bcb9-09a25d0b2c1d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>KES22_LJB-R_20220908A-072747_M0907060.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>KES22_LJB-R_20220908A-072749_M0907061.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>KES22_LJB-R_20220908A-073430_M0907261.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>KES22_LJB-R_20220908A-073432_M0907262.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>257</th>\n", "      <td>KES22_LJB-R_20220908A-073434_M0907263.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  c<PERSON><PERSON><PERSON><PERSON>\n", "69   KES22_LJB-R_20220908A-072747_M0907060.jpg\n", "108  KES22_LJB-R_20220908A-072749_M0907061.jpg\n", "212  KES22_LJB-R_20220908A-073430_M0907261.jpg\n", "21   KES22_LJB-R_20220908A-073432_M0907262.jpg\n", "257  KES22_LJB-R_20220908A-073434_M0907263.jpg"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin = cvatimg.sort_values(by=[\"cvatfilename\"],ascending=True)\n", "cvatfin.head()"]}, {"cell_type": "code", "execution_count": 54, "id": "67455f80-c824-4529-b515-437b119701c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(364, 1)"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin.shape"]}, {"cell_type": "code", "execution_count": 55, "id": "dc41c311-5c01-4556-8eca-b38590211628", "metadata": {}, "outputs": [], "source": ["cvatfin.to_csv(\"MTrightcvat.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "a9973fe2-38f9-4102-8131-4cc4d2db4c4e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b16ac600-595c-47fe-ba9a-b7c7d29f7111", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5259e8b4-d426-487d-91fd-92baeb3e1556", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "cc806d4c-c697-48e4-89e3-129958131725", "metadata": {}, "source": ["Annotations"]}, {"cell_type": "code", "execution_count": 57, "id": "98589797", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "annotationsdf = pd.DataFrame(coco_data['annotations'])\n"]}, {"cell_type": "code", "execution_count": 58, "id": "9ab9d2b2-f241-4195-a1b0-99e7e1ff6688", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>3531.029913</td>\n", "      <td>[3136.4668019044, 1525.14141578463, -60.966167...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>5315.129237</td>\n", "      <td>[3136.4668019044, 1576.96265824868, 67.0627843...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>12456.356031</td>\n", "      <td>[1456.64331726931, 1710.85830811493, 115.45772...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>20377.265781</td>\n", "      <td>[1346.86384096244, 1915.27526399669, 149.52721...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>4413.641252</td>\n", "      <td>[1519.10405378873, 1877.42027216673, 52.996988...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1           14           []   3531.029913   \n", "1   2         1           14           []   5315.129237   \n", "2   3         2            9           []  12456.356031   \n", "3   4         2            9           []  20377.265781   \n", "4   5         2            9           []   4413.641252   \n", "\n", "                                                bbox  iscrowd  \n", "0  [3136.4668019044, 1525.14141578463, -60.966167...        0  \n", "1  [3136.4668019044, 1576.96265824868, 67.0627843...        0  \n", "2  [1456.64331726931, 1710.85830811493, 115.45772...        0  \n", "3  [1346.86384096244, 1915.27526399669, 149.52721...        0  \n", "4  [1519.10405378873, 1877.42027216673, 52.996988...        0  "]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 59, "id": "e4193fb0-f31a-4583-aee1-5466a6d9ff32", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>147</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072836_M1006264.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>148</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072838_M1006265.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>149</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072840_M1006266.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>146</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072842_M1006267.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>150</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-073213_M1006372.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     id  width  height                                  file_name  license  \\\n", "60  147   7008    4672  KES22_LJB-L_20220908A-072836_M1006264.jpg        1   \n", "61  148   7008    4672  KES22_LJB-L_20220908A-072838_M1006265.jpg        1   \n", "62  149   7008    4672  KES22_LJB-L_20220908A-072840_M1006266.jpg        1   \n", "59  146   7008    4672  KES22_LJB-L_20220908A-072842_M1006267.jpg        1   \n", "63  150   7008    4672  KES22_LJB-L_20220908A-073213_M1006372.jpg        1   \n", "\n", "   date_captured  \n", "60    2022-09-09  \n", "61    2022-09-09  \n", "62    2022-09-09  \n", "59    2022-09-09  \n", "63    2022-09-09  "]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftsorted.head()"]}, {"cell_type": "code", "execution_count": 75, "id": "a8ad9f17-01f2-48aa-b76f-0f35d3009a2a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-070510_M0801395.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053746_M0808781.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054013_M0808854.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054205_M0808910.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>238</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-080514_M1007359.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>138</th>\n", "      <td>239</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-080404_M1007324.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>139</th>\n", "      <td>289</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-091512_M1009451.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>140</th>\n", "      <td>290</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-091434_M1009432.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>141</th>\n", "      <td>291</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-091442_M1009436.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>142 rows × 6 columns</p>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "0      1   7008    4672  KES22_WOT-L_20220909A-070510_M0801395.jpg        1   \n", "1      2   7008    4672  KES22_WOT-L_20220909A-053748_M0808782.jpg        1   \n", "2      3   7008    4672  KES22_WOT-L_20220909A-053746_M0808781.jpg        1   \n", "3      4   7008    4672  KES22_WOT-L_20220909A-054013_M0808854.jpg        1   \n", "4      5   7008    4672  KES22_WOT-L_20220909A-054205_M0808910.jpg        1   \n", "..   ...    ...     ...                                        ...      ...   \n", "137  238   7008    4672  KES22_LJB-L_20220908A-080514_M1007359.jpg        1   \n", "138  239   7008    4672  KES22_LJB-L_20220908A-080404_M1007324.jpg        1   \n", "139  289   7008    4672  KES22_LJB-L_20220908A-091512_M1009451.jpg        1   \n", "140  290   7008    4672  KES22_LJB-L_20220908A-091434_M1009432.jpg        1   \n", "141  291   7008    4672  KES22_LJB-L_20220908A-091442_M1009436.jpg        1   \n", "\n", "    date_captured  \n", "0      2022-09-09  \n", "1      2022-09-09  \n", "2      2022-09-09  \n", "3      2022-09-09  \n", "4      2022-09-09  \n", "..            ...  \n", "137    2022-09-09  \n", "138    2022-09-09  \n", "139    2022-09-09  \n", "140    2022-09-09  \n", "141    2022-09-09  \n", "\n", "[142 rows x 6 columns]"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["MTleftsorted.sort_values(by=[\"id\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 76, "id": "fd7c2b81-3bad-49b8-98c7-9bd405abc281", "metadata": {}, "outputs": [], "source": ["# Compare image 'ID' columns and return annotations \n", "result_values = annotationsdf[annotationsdf['image_id'].isin(MTleftsorted['id'])]"]}, {"cell_type": "code", "execution_count": 71, "id": "dc2d2883-f90f-4b14-8e97-23bed48462ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1477, 7)"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values.shape"]}, {"cell_type": "code", "execution_count": 78, "id": "a013ab74-3c01-47b8-8c87-83c9ddd2ac7c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2414, 7)"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.shape"]}, {"cell_type": "code", "execution_count": 72, "id": "c57c8809-7d45-470d-8e25-4f583132f5b7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>3531.029913</td>\n", "      <td>[3136.4668019044, 1525.14141578463, -60.966167...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>5315.129237</td>\n", "      <td>[3136.4668019044, 1576.96265824868, 67.0627843...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>12456.356031</td>\n", "      <td>[1456.64331726931, 1710.85830811493, 115.45772...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>20377.265781</td>\n", "      <td>[1346.86384096244, 1915.27526399669, 149.52721...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>4413.641252</td>\n", "      <td>[1519.10405378873, 1877.42027216673, 52.996988...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1           14           []   3531.029913   \n", "1   2         1           14           []   5315.129237   \n", "2   3         2            9           []  12456.356031   \n", "3   4         2            9           []  20377.265781   \n", "4   5         2            9           []   4413.641252   \n", "\n", "                                                bbox  iscrowd  \n", "0  [3136.4668019044, 1525.14141578463, -60.966167...        0  \n", "1  [3136.4668019044, 1576.96265824868, 67.0627843...        0  \n", "2  [1456.64331726931, 1710.85830811493, 115.45772...        0  \n", "3  [1346.86384096244, 1915.27526399669, 149.52721...        0  \n", "4  [1519.10405378873, 1877.42027216673, 52.996988...        0  "]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values.head()"]}, {"cell_type": "code", "execution_count": 82, "id": "c8e87cea-2d9f-4ee4-b71f-1eaaced692da", "metadata": {}, "outputs": [], "source": ["result_values.to_csv(\"MTleftannotations.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 77, "id": "fd2ab339-e6e6-456a-a121-214cb260ac04", "metadata": {}, "outputs": [{"data": {"text/plain": ["0    [3136.4668019044, 1525.14141578463, -60.966167...\n", "1    [3136.4668019044, 1576.96265824868, 67.0627843...\n", "2    [1456.64331726931, 1710.85830811493, 115.45772...\n", "3    [1346.86384096244, 1915.27526399669, 149.52721...\n", "4    [1519.10405378873, 1877.42027216673, 52.996988...\n", "Name: bbox, dtype: object"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values['bbox'].head() #change -ve to +ve"]}, {"cell_type": "code", "execution_count": null, "id": "431bd5e3-19b8-4473-b5f2-07460645bd12", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 79, "id": "2e2b725d-2c17-4191-87d6-b76cd869ca62", "metadata": {}, "outputs": [], "source": ["# Compare image 'ID' columns and return annotations \n", "result_valuesright = annotationsdf[annotationsdf['image_id'].isin(MTrightsorted['id'])]"]}, {"cell_type": "code", "execution_count": 80, "id": "438e2ee3-45d1-4789-b428-c18564fd830f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(937, 7)"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["result_valuesright.shape"]}, {"cell_type": "code", "execution_count": 81, "id": "329c5336-6af2-4697-8242-d9471608b45f", "metadata": {}, "outputs": [], "source": ["result_valuesright.to_csv(\"MTrightannotations.csv\",index=False)"]}, {"cell_type": "markdown", "id": "368d46a6-a4bf-443e-b5ee-4e21fd1d5a89", "metadata": {}, "source": ["### MT left COCO file"]}, {"cell_type": "code", "execution_count": 26, "id": "ac1a6bcd-0ce9-4b87-8c4a-679c4619136d", "metadata": {}, "outputs": [], "source": ["datapath = \"/Users/<USER>/Dropbox/Conservation/MWS_LabArusha/annotations/exports\""]}, {"cell_type": "code", "execution_count": 27, "id": "e844f260-cf39-4a71-9c92-22e761e93057", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>3531.029913</td>\n", "      <td>[3136.4668019044, 1525.14141578463, -60.966167...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>5315.129237</td>\n", "      <td>[3136.4668019044, 1576.96265824868, 67.0627843...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>12456.356031</td>\n", "      <td>[1456.64331726931, 1710.85830811493, 115.45772...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>20377.265781</td>\n", "      <td>[1346.86384096244, 1915.27526399669, 149.52721...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>4413.641252</td>\n", "      <td>[1519.10405378873, 1877.42027216673, 52.996988...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1           14           []   3531.029913   \n", "1   2         1           14           []   5315.129237   \n", "2   3         2            9           []  12456.356031   \n", "3   4         2            9           []  20377.265781   \n", "4   5         2            9           []   4413.641252   \n", "\n", "                                                bbox  iscrowd  \n", "0  [3136.4668019044, 1525.14141578463, -60.966167...        0  \n", "1  [3136.4668019044, 1576.96265824868, 67.0627843...        0  \n", "2  [1456.64331726931, 1710.85830811493, 115.45772...        0  \n", "3  [1346.86384096244, 1915.27526399669, 149.52721...        0  \n", "4  [1519.10405378873, 1877.42027216673, 52.996988...        0  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["mtleftanno = pd.read_csv(datapath+\"/MTleftannotations.csv\")\n", "mtleftanno.head()"]}, {"cell_type": "code", "execution_count": 28, "id": "c8281f42-a5ea-4e4f-beaf-0d88136baa5e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>147</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072836_M1006264.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>148</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072838_M1006265.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>149</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072840_M1006266.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>146</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072842_M1006267.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>150</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-073213_M1006372.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0  147   7008    4672  KES22_LJB-L_20220908A-072836_M1006264.jpg        1   \n", "1  148   7008    4672  KES22_LJB-L_20220908A-072838_M1006265.jpg        1   \n", "2  149   7008    4672  KES22_LJB-L_20220908A-072840_M1006266.jpg        1   \n", "3  146   7008    4672  KES22_LJB-L_20220908A-072842_M1006267.jpg        1   \n", "4  150   7008    4672  KES22_LJB-L_20220908A-073213_M1006372.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-09  \n", "1    2022-09-09  \n", "2    2022-09-09  \n", "3    2022-09-09  \n", "4    2022-09-09  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["mtleftimages = pd.read_csv(datapath+\"/MTleftimagespos.csv\")\n", "mtleftimages.head()"]}, {"cell_type": "code", "execution_count": 34, "id": "bc5b969d-9351-4fad-83cb-27aec08851b4", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = mtleftimages.to_dict(orient='records')\n", "coco_dict['annotations'] = mtleftanno.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'mtleftannotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": 32, "id": "25d27e8d-9b16-49d6-80ad-d0457b4526a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'id': 1,\n", "  'name': 'KAZA Elephant Survey',\n", "  'url': 'https://www.kavangozambezi.org/kaza-elephant-survey/'}]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["coco_data[\"licenses\"]"]}, {"cell_type": "code", "execution_count": 35, "id": "36226de4-c23f-45dd-8d40-92c76b860f57", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>484</td>\n", "      <td>60</td>\n", "      <td>18</td>\n", "      <td>[]</td>\n", "      <td>2619.726101</td>\n", "      <td>[6305.47752808989, 2412.17850990197, 54.488741...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>485</td>\n", "      <td>61</td>\n", "      <td>26</td>\n", "      <td>[]</td>\n", "      <td>2843.458701</td>\n", "      <td>[6193.65061344198, 1428.3539729611, 56.6255545...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>486</td>\n", "      <td>61</td>\n", "      <td>26</td>\n", "      <td>[]</td>\n", "      <td>3871.943763</td>\n", "      <td>[6258.82342151602, 1434.76441309953, 56.625554...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>487</td>\n", "      <td>62</td>\n", "      <td>19</td>\n", "      <td>[]</td>\n", "      <td>3725.801057</td>\n", "      <td>[5034.88627559047, 1701.07912114624, 75.709983...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>488</td>\n", "      <td>62</td>\n", "      <td>19</td>\n", "      <td>[]</td>\n", "      <td>1766.173001</td>\n", "      <td>[5214.69748678276, 1981.2060606879, 32.1767430...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  image_id  category_id segmentation         area  \\\n", "0  484        60           18           []  2619.726101   \n", "1  485        61           26           []  2843.458701   \n", "2  486        61           26           []  3871.943763   \n", "3  487        62           19           []  3725.801057   \n", "4  488        62           19           []  1766.173001   \n", "\n", "                                                bbox  iscrowd  \n", "0  [6305.47752808989, 2412.17850990197, 54.488741...        0  \n", "1  [6193.65061344198, 1428.3539729611, 56.6255545...        0  \n", "2  [6258.82342151602, 1434.76441309953, 56.625554...        0  \n", "3  [5034.88627559047, 1701.07912114624, 75.709983...        0  \n", "4  [5214.69748678276, 1981.2060606879, 32.1767430...        0  "]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["mtrightanno = pd.read_csv(datapath+\"/MTrightannotations.csv\")\n", "mtrightanno.head()"]}, {"cell_type": "code", "execution_count": 36, "id": "a55bfcb9-13ee-4468-bb3e-31c973044267", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>223</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-072747_M0907060.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>229</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-073432_M0907262.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>225</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-073434_M0907263.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>224</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-073518_M0907285.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>228</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-073520_M0907286.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0  223   7008    4672  KES22_LJB-R_20220908A-072747_M0907060.jpg        1   \n", "1  229   7008    4672  KES22_LJB-R_20220908A-073432_M0907262.jpg        1   \n", "2  225   7008    4672  KES22_LJB-R_20220908A-073434_M0907263.jpg        1   \n", "3  224   7008    4672  KES22_LJB-R_20220908A-073518_M0907285.jpg        1   \n", "4  228   7008    4672  KES22_LJB-R_20220908A-073520_M0907286.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-09  \n", "1    2022-09-09  \n", "2    2022-09-09  \n", "3    2022-09-09  \n", "4    2022-09-09  "]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["mtrightimages = pd.read_csv(datapath+\"/MTrightimagespos.csv\")\n", "mtrightimages.head()"]}, {"cell_type": "code", "execution_count": 39, "id": "6df4b37a-be3d-44d3-a10a-b92fe5a7ec73", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dictright = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dictright['images'] = mtrightimages.to_dict(orient='records')\n", "coco_dictright['annotations'] = mtrightanno.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'mtrightannotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dictright, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "id": "254708b5-3db5-489a-bc35-918c449acee8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16dd420b-4f4d-42de-9aa8-1917a1d7a760", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "043cfaf2-580a-4a72-9871-0e8bbad31a7f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}