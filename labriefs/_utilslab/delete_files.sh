#!/bin/bash

# Check if a CSV file is provided as a command-line argument
if [ "$#" -ne 2 ]; then
    echo "Usage: $0 /path/to/directory /path/to/csvfile.csv"
    exit 1
fi

# Assign command line arguments to variables
directory="$1"
csvfile="$2"

# Check if the specified directory exists
if [ ! -d "$directory" ]; then
    echo "Directory does not exist: $directory"
    exit 1
fi

# Check if the CSV file exists
if [ ! -f "$csvfile" ]; then
    echo "CSV file does not exist: $csvfile"
    exit 1
fi

# Change to the directory to ensure relative paths work correctly
cd "$directory" || exit 1

# Read the CSV file line by line and delete the listed files
while IFS= read -r filename; do
    file_to_delete="$directory/$filename"
    if [ -e "$file_to_delete" ]; then
        sudo rm -r "$file_to_delete" # recurssively delete files on directory
        echo "Deleted: $file_to_delete"
    else
        echo "File not found: $file_to_delete"
    fi
done < "$csvfile"