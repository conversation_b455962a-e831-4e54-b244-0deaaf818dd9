{"cells": [{"cell_type": "code", "execution_count": 1, "id": "802f7ac7", "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 19, "id": "dc435446", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>name</th>\n", "      <th>displayName</th>\n", "      <th>taskType</th>\n", "      <th>assignee</th>\n", "      <th>orientation</th>\n", "      <th>randomized</th>\n", "      <th>createdByUserId</th>\n", "      <th>assigneeDiplayName</th>\n", "      <th>createdAt</th>\n", "      <th>updatedAt</th>\n", "      <th>progressAnnotation</th>\n", "      <th>progressGroundTruth</th>\n", "      <th>progressLineDivision</th>\n", "      <th>sequencingComplete</th>\n", "      <th>imageCount</th>\n", "      <th>tagIds</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'$oid': '651e795d899c400045a59f73'}</td>\n", "      <td>kaza_ml</td>\n", "      <td>KAZA_ML</td>\n", "      <td>ml</td>\n", "      <td>ml-v1</td>\n", "      <td>left</td>\n", "      <td>False</td>\n", "      <td>64253370dead810044978bc5</td>\n", "      <td>ML Config: V1 Classifier</td>\n", "      <td>1.696496e+12</td>\n", "      <td>1.698992e+12</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>[65254d1f62e3240045e45673]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>{'$oid': '652e6e67df32760044f21ca3'}</td>\n", "      <td>t_a</td>\n", "      <td>t_A</td>\n", "      <td>ml</td>\n", "      <td>ml-v2</td>\n", "      <td>right</td>\n", "      <td>False</td>\n", "      <td>651eb6c6899c400045a59f80</td>\n", "      <td>ML Config: V2 Classifier</td>\n", "      <td>1.697542e+12</td>\n", "      <td>1.698224e+12</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>{'$oid': '652e6e82df32760044f21ca5'}</td>\n", "      <td>t_b</td>\n", "      <td>t_B</td>\n", "      <td>user</td>\n", "      <td>651eb6c6899c400045a59f80</td>\n", "      <td>right</td>\n", "      <td>False</td>\n", "      <td>651eb6c6899c400045a59f80</td>\n", "      <td>simbamangu</td>\n", "      <td>1.697542e+12</td>\n", "      <td>1.698224e+12</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>{'$oid': '6530dcab05667e0045cadc40'}</td>\n", "      <td>dan05-l_58</td>\n", "      <td>DAN05-L_58</td>\n", "      <td>user</td>\n", "      <td>6524f99d62e3240045e45638</td>\n", "      <td>left</td>\n", "      <td>True</td>\n", "      <td>6523b8b118f3b3004425b07f</td>\n", "      <td>jmmbaga</td>\n", "      <td>1.697701e+12</td>\n", "      <td>1.698141e+12</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>[6525024e62e3240045e4563f]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>{'$oid': '6530dd1905667e0045cadc43'}</td>\n", "      <td>dan05-l_58_2</td>\n", "      <td>DAN05-L_58_2</td>\n", "      <td>user</td>\n", "      <td>6524fa4562e3240045e45639</td>\n", "      <td>left</td>\n", "      <td>True</td>\n", "      <td>6523b8b118f3b3004425b07f</td>\n", "      <td>hgeorge</td>\n", "      <td>1.697701e+12</td>\n", "      <td>1.698141e+12</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "      <td>[6525029762e3240045e45642]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                    _id          name   displayName taskType  \\\n", "0  {'$oid': '651e795d899c400045a59f73'}       kaza_ml       KAZA_ML       ml   \n", "1  {'$oid': '652e6e67df32760044f21ca3'}           t_a           t_A       ml   \n", "2  {'$oid': '652e6e82df32760044f21ca5'}           t_b           t_B     user   \n", "3  {'$oid': '6530dcab05667e0045cadc40'}    dan05-l_58    DAN05-L_58     user   \n", "4  {'$oid': '6530dd1905667e0045cadc43'}  dan05-l_58_2  DAN05-L_58_2     user   \n", "\n", "                   assignee orientation  randomized           createdByUserId  \\\n", "0                     ml-v1        left       False  64253370dead810044978bc5   \n", "1                     ml-v2       right       False  651eb6c6899c400045a59f80   \n", "2  651eb6c6899c400045a59f80       right       False  651eb6c6899c400045a59f80   \n", "3  6524f99d62e3240045e45638        left        True  6523b8b118f3b3004425b07f   \n", "4  6524fa4562e3240045e45639        left        True  6523b8b118f3b3004425b07f   \n", "\n", "         assigneeDiplayName     createdAt     updatedAt  progressAnnotation  \\\n", "0  ML Config: V1 Classifier  1.696496e+12  1.698992e+12                 1.0   \n", "1  ML Config: V2 Classifier  1.697542e+12  1.698224e+12                 1.0   \n", "2                simbamangu  1.697542e+12  1.698224e+12                 1.0   \n", "3                   jmmbaga  1.697701e+12  1.698141e+12                 1.0   \n", "4                   hgeorge  1.697701e+12  1.698141e+12                 1.0   \n", "\n", "   progressGroundTruth  progressLineDivision  sequencingComplete  imageCount  \\\n", "0                  0.0                     0               False           0   \n", "1                  1.0                     0                True           0   \n", "2                  1.0                     0                True           0   \n", "3                  0.0                     0               False           0   \n", "4                  0.0                     0               False           0   \n", "\n", "                       tagIds  \n", "0  [65254d1f62e3240045e45673]  \n", "1                        None  \n", "2                        None  \n", "3  [6525024e62e3240045e4563f]  \n", "4  [6525029762e3240045e45642]  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["taskdf = pd.read_json(\"/home/<USER>/Dropbox/Conservation/MWSLab-2023/labriefs/_media/taskdbdata.json\")\n", "taskdf.head()"]}, {"cell_type": "code", "execution_count": 20, "id": "fc42dfcd", "metadata": {}, "outputs": [{"data": {"text/plain": ["(368, 17)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["taskdf.shape"]}, {"cell_type": "code", "execution_count": 21, "id": "9da6ccbe", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['_id', 'name', 'displayName', 'taskType', 'assignee', 'orientation',\n", "       'randomized', 'createdByUserId', 'assigneeDiplayName', 'createdAt',\n", "       'updatedAt', 'progressAnnotation', 'progressGroundTruth',\n", "       'progressLineDivision', 'sequencingComplete', 'imageCount', 'tagIds'],\n", "      dtype='object')"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["taskdf.columns"]}, {"cell_type": "code", "execution_count": 22, "id": "507f3216", "metadata": {}, "outputs": [], "source": ["selectask = taskdf[['displayName','assigneeDiplayName','imageCount']]"]}, {"cell_type": "code", "execution_count": 7, "id": "ff216f4b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>displayName</th>\n", "      <th>assigneeDiplayName</th>\n", "      <th>imageCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KAZA_ML</td>\n", "      <td>ML Config: V1 Classifier</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>t_A</td>\n", "      <td>ML Config: V2 Classifier</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>t_B</td>\n", "      <td>simbamangu</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_58</td>\n", "      <td>jmmbaga</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_58_2</td>\n", "      <td>hgeorge</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    displayName        assigneeDiplayName  imageCount\n", "0       KAZA_ML  ML Config: V1 Classifier           0\n", "1           t_A  ML Config: V2 Classifier           0\n", "2           t_B                simbamangu           0\n", "3    DAN05-L_58                   jmmbaga           0\n", "4  DAN05-L_58_2                   hgeorge           0"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["selectask.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "f4471172", "metadata": {}, "outputs": [], "source": ["#define the wildcard pattern\n", "pattern = \"DAN\""]}, {"cell_type": "code", "execution_count": 9, "id": "cf80070e", "metadata": {}, "outputs": [], "source": ["# Filter the DataFrame based on the pattern\n", "filtered_df = selectask[selectask['displayName'].str.contains(pattern, case=False, na=False, regex=True)]"]}, {"cell_type": "code", "execution_count": 10, "id": "7954d0fc", "metadata": {}, "outputs": [{"data": {"text/plain": ["(151, 3)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_df.shape"]}, {"cell_type": "code", "execution_count": 11, "id": "dfff1f75", "metadata": {}, "outputs": [], "source": ["filtered_df.to_csv(\"/home/<USER>/Dropbox/Conservation/MWSLab-2023/labriefs/_media/DANannotators.csv\")"]}, {"cell_type": "code", "execution_count": 12, "id": "15b6c544", "metadata": {}, "outputs": [], "source": ["#define the wildcard pattern\n", "pattern2 = \"SH\""]}, {"cell_type": "code", "execution_count": 13, "id": "4e549e5e", "metadata": {}, "outputs": [], "source": ["# Filter the DataFrame based on the pattern\n", "filtered_df2 = selectask[selectask['displayName'].str.contains(pattern2, case=False, na=False, regex=True)]"]}, {"cell_type": "code", "execution_count": 14, "id": "58d06286", "metadata": {}, "outputs": [{"data": {"text/plain": ["(137, 3)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_df2.shape"]}, {"cell_type": "code", "execution_count": 14, "id": "38d09d22", "metadata": {}, "outputs": [], "source": ["filtered_df2.to_csv(\"/home/<USER>/Dropbox/Conservation/MWSLab-2023/labriefs/_media/SHannotators.csv\")"]}, {"cell_type": "code", "execution_count": 23, "id": "3d986749", "metadata": {}, "outputs": [], "source": ["pattern3 = \"NG26\""]}, {"cell_type": "code", "execution_count": 25, "id": "c9b782b8", "metadata": {}, "outputs": [], "source": ["filtered_df3= selectask[selectask['displayName'].str.contains(pattern3, case=False, na=False, regex=True)]"]}, {"cell_type": "code", "execution_count": 26, "id": "bbceab26", "metadata": {}, "outputs": [{"data": {"text/plain": ["(77, 3)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["filtered_df3.shape"]}, {"cell_type": "code", "execution_count": 28, "id": "3cba8520", "metadata": {}, "outputs": [], "source": ["filtered_df3.to_csv(homedir+ \"/labriefs/_media/NG26annotators.csv\")"]}, {"cell_type": "markdown", "id": "3de82e0d", "metadata": {}, "source": ["### NG26 tasks and image count"]}, {"cell_type": "code", "execution_count": 3, "id": "cb6ec764", "metadata": {}, "outputs": [], "source": ["homedir = \"/home/<USER>/Dropbox/Conservation/MWSLab-2023\""]}, {"cell_type": "code", "execution_count": 4, "id": "3cee960d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050240_M0301361.jpg</td>\n", "      <td>6540dfe8e1ace1004c5535db</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050302_M0301372.jpg</td>\n", "      <td>6540dfebe1ace1004c5535dc</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050248_M0301365.jpg</td>\n", "      <td>6540dfede1ace1004c5535dd</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050338_M0301390.jpg</td>\n", "      <td>6540dff0e1ace1004c5535de</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050318_M0301380.jpg</td>\n", "      <td>6540dff3e1ace1004c5535df</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Task Name                   Task ID  \\\n", "0  NG26-01-L_34  6540f9228a297200456fceee   \n", "1  NG26-01-L_34  6540f9228a297200456fceee   \n", "2  NG26-01-L_34  6540f9228a297200456fceee   \n", "3  NG26-01-L_34  6540f9228a297200456fceee   \n", "4  NG26-01-L_34  6540f9228a297200456fceee   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_IIM-L_20220924A-050240_M0301361.jpg  6540dfe8e1ace1004c5535db   \n", "1  KES22_IIM-L_20220924A-050302_M0301372.jpg  6540dfebe1ace1004c5535dc   \n", "2  KES22_IIM-L_20220924A-050248_M0301365.jpg  6540dfede1ace1004c5535dd   \n", "3  KES22_IIM-L_20220924A-050338_M0301390.jpg  6540dff0e1ace1004c5535de   \n", "4  KES22_IIM-L_20220924A-050318_M0301380.jpg  6540dff3e1ace1004c5535df   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                 True   \n", "1          4672         7008             NaN                 True   \n", "2          4672         7008             NaN                 True   \n", "3          4672         7008             NaN                 True   \n", "4          4672         7008             NaN                 True   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26df = pd.read_csv(homedir+\"/scoutexports/NG26scout-export-images.csv\")\n", "ng26df.head()"]}, {"cell_type": "code", "execution_count": 7, "id": "75122804", "metadata": {}, "outputs": [{"data": {"text/plain": ["77"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26df[\"Task Name\"].nunique()"]}, {"cell_type": "code", "execution_count": 16, "id": "e88a0c6e", "metadata": {}, "outputs": [], "source": ["ng26tasks = ng26df[\"Task Name\"].value_counts()"]}, {"cell_type": "code", "execution_count": 17, "id": "2e2b8c52", "metadata": {}, "outputs": [{"data": {"text/plain": ["NG26-07-L_1022    174\n", "NG26-07-R_1020    174\n", "NG26-07-L_692     174\n", "NG26-07-R_690     174\n", "NG26-07-L_854     173\n", "Name: Task Name, dtype: int64"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26tasks.head()"]}, {"cell_type": "code", "execution_count": 18, "id": "1bf7258d", "metadata": {}, "outputs": [], "source": ["ng26tasks.to_csv(homedir+\"/scoutexports/ng26tasks.csv\")"]}, {"cell_type": "markdown", "id": "3de9b628-c0d1-4e30-92aa-fa1e444d521e", "metadata": {}, "source": ["## MT Tasks"]}, {"cell_type": "code", "execution_count": 4, "id": "d8799400-7e26-462a-9b6e-0c9999386d98", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>name</th>\n", "      <th>displayName</th>\n", "      <th>taskType</th>\n", "      <th>assignee</th>\n", "      <th>orientation</th>\n", "      <th>tagIds</th>\n", "      <th>randomized</th>\n", "      <th>createdByUserId</th>\n", "      <th>assigneeDiplayName</th>\n", "      <th>createdAt</th>\n", "      <th>updatedAt</th>\n", "      <th>progressAnnotation</th>\n", "      <th>progressGroundTruth</th>\n", "      <th>progressLineDivision</th>\n", "      <th>sequencingComplete</th>\n", "      <th>imageCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'$oid': '654b524e47d0660045090326'}</td>\n", "      <td>mt02-l_46</td>\n", "      <td>MT02-L_46</td>\n", "      <td>user</td>\n", "      <td>6524fa4562e3240045e45639</td>\n", "      <td>left</td>\n", "      <td>[6525029762e3240045e45642]</td>\n", "      <td>True</td>\n", "      <td>6523b8b118f3b3004425b07f</td>\n", "      <td>hgeorge</td>\n", "      <td>1.699435e+12</td>\n", "      <td>1.699450e+12</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>46</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>{'$oid': '654b52ac47d0660045090329'}</td>\n", "      <td>mt03-l_140</td>\n", "      <td>MT03-L_140</td>\n", "      <td>user</td>\n", "      <td>652d988242b4d50045313dec</td>\n", "      <td>left</td>\n", "      <td>[652f8d2bb7daaa0045118f3b]</td>\n", "      <td>True</td>\n", "      <td>6523b8b118f3b3004425b07f</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1.699435e+12</td>\n", "      <td>1.699596e+12</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>94</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>{'$oid': '654b531e47d066004509032b'}</td>\n", "      <td>mt03-l_241</td>\n", "      <td>MT03-L_241</td>\n", "      <td>user</td>\n", "      <td>65279287b2decd004564a4c4</td>\n", "      <td>left</td>\n", "      <td>[6527a53eb2decd004564a4f0]</td>\n", "      <td>True</td>\n", "      <td>6523b8b118f3b3004425b07f</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1.699435e+12</td>\n", "      <td>1.699872e+12</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>{'$oid': '654b53d447d066004509032d'}</td>\n", "      <td>mt04-l_247</td>\n", "      <td>MT04-L_247</td>\n", "      <td>user</td>\n", "      <td>651d51b4b5c3e0004614dbf5</td>\n", "      <td>left</td>\n", "      <td>[651e7a1f899c400045a59f77]</td>\n", "      <td>True</td>\n", "      <td>6523b8b118f3b3004425b07f</td>\n", "      <td>annotator2</td>\n", "      <td>1.699435e+12</td>\n", "      <td>1.699874e+12</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>{'$oid': '654b547447d0660045090331'}</td>\n", "      <td>mt05-l_326</td>\n", "      <td>MT05-L_326</td>\n", "      <td>user</td>\n", "      <td>651d51b4b5c3e0004614dbf5</td>\n", "      <td>left</td>\n", "      <td>[651e7a1f899c400045a59f77]</td>\n", "      <td>True</td>\n", "      <td>6523b8b118f3b3004425b07f</td>\n", "      <td>annotator2</td>\n", "      <td>1.699436e+12</td>\n", "      <td>1.699942e+12</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>79</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                    _id        name displayName taskType  \\\n", "0  {'$oid': '654b524e47d0660045090326'}   mt02-l_46   MT02-L_46     user   \n", "1  {'$oid': '654b52ac47d0660045090329'}  mt03-l_140  MT03-L_140     user   \n", "2  {'$oid': '654b531e47d066004509032b'}  mt03-l_241  MT03-L_241     user   \n", "3  {'$oid': '654b53d447d066004509032d'}  mt04-l_247  MT04-L_247     user   \n", "4  {'$oid': '654b547447d0660045090331'}  mt05-l_326  MT05-L_326     user   \n", "\n", "                   assignee orientation                      tagIds  \\\n", "0  6524fa4562e3240045e45639        left  [6525029762e3240045e45642]   \n", "1  652d988242b4d50045313dec        left  [652f8d2bb7daaa0045118f3b]   \n", "2  65279287b2decd004564a4c4        left  [6527a53eb2decd004564a4f0]   \n", "3  651d51b4b5c3e0004614dbf5        left  [651e7a1f899c400045a59f77]   \n", "4  651d51b4b5c3e0004614dbf5        left  [651e7a1f899c400045a59f77]   \n", "\n", "   randomized           createdByUserId assigneeDiplayName     createdAt  \\\n", "0        True  6523b8b118f3b3004425b07f            hgeorge  1.699435e+12   \n", "1        True  6523b8b118f3b3004425b07f             z<PERSON><PERSON>  1.699435e+12   \n", "2        True  6523b8b118f3b3004425b07f           mshirima  1.699435e+12   \n", "3        True  6523b8b118f3b3004425b07f         annotator2  1.699435e+12   \n", "4        True  6523b8b118f3b3004425b07f         annotator2  1.699436e+12   \n", "\n", "      updatedAt  progressAnnotation  progressGroundTruth  \\\n", "0  1.699450e+12                 1.0                  0.0   \n", "1  1.699596e+12                 1.0                  0.0   \n", "2  1.699872e+12                 1.0                  0.0   \n", "3  1.699874e+12                 1.0                  0.0   \n", "4  1.699942e+12                 1.0                  0.0   \n", "\n", "   progressLineDivision  sequencingComplete  imageCount  \n", "0                     0               False          46  \n", "1                     0               False          94  \n", "2                     0               False         107  \n", "3                     0               False           6  \n", "4                     0               False          79  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["taskmtdf = pd.read_json(\"/Users/<USER>/Dropbox/Conservation/MWSLab-2023/labriefs/_media/taskdbdataMT.json\")\n", "taskmtdf.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "c404a60a-fe77-4746-9962-63a113771c03", "metadata": {}, "outputs": [{"data": {"text/plain": ["(101, 17)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["taskmtdf.shape"]}, {"cell_type": "code", "execution_count": 6, "id": "d587a669-e2fd-410c-b51e-58c4cd1f26a1", "metadata": {}, "outputs": [], "source": ["selectaskmt = taskmtdf[['displayName','assigneeDiplayName','imageCount']]"]}, {"cell_type": "code", "execution_count": 9, "id": "eca10a64-e195-4e17-ac3d-fbf6a0e5a0f3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>displayName</th>\n", "      <th>assigneeDiplayName</th>\n", "      <th>imageCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MT02-L_46</td>\n", "      <td>hgeorge</td>\n", "      <td>46</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MT03-L_140</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>94</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MT03-L_241</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>MT04-L_247</td>\n", "      <td>annotator2</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MT05-L_326</td>\n", "      <td>annotator2</td>\n", "      <td>79</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  displayName assigneeDiplayName  imageCount\n", "0   MT02-L_46            hgeorge          46\n", "1  MT03-<PERSON>_140             <PERSON><PERSON><PERSON>          94\n", "2  MT03-<PERSON>_241           mshirima         107\n", "3  MT04-L_247         annotator2           6\n", "4  MT05-L_326         annotator2          79"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["selectaskmt.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "9890ab5d-2db2-47a6-b45a-d8e8f45315ee", "metadata": {}, "outputs": [], "source": ["selectaskmt.to_csv(\"MTtasks.csv\")"]}, {"cell_type": "markdown", "id": "30203a76", "metadata": {}, "source": ["## Zambia A2 block"]}, {"cell_type": "code", "execution_count": 2, "id": "c6930cb2", "metadata": {}, "outputs": [], "source": ["homedir = \"/home/<USER>/Dropbox/Conservation/MWSLab-2023\""]}, {"cell_type": "code", "execution_count": 3, "id": "f47a54af", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>name</th>\n", "      <th>displayName</th>\n", "      <th>taskType</th>\n", "      <th>assignee</th>\n", "      <th>orientation</th>\n", "      <th>randomized</th>\n", "      <th>createdByUserId</th>\n", "      <th>assigneeDiplayName</th>\n", "      <th>createdAt</th>\n", "      <th>updatedAt</th>\n", "      <th>progressAnnotation</th>\n", "      <th>progressGroundTruth</th>\n", "      <th>progressLineDivision</th>\n", "      <th>sequencingComplete</th>\n", "      <th>imageCount</th>\n", "      <th>tagIds</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'$oid': '652e6e67df32760044f21ca3'}</td>\n", "      <td>t_a</td>\n", "      <td>t_A</td>\n", "      <td>ml</td>\n", "      <td>ml-v2</td>\n", "      <td>right</td>\n", "      <td>False</td>\n", "      <td>651eb6c6899c400045a59f80</td>\n", "      <td>ML Config: V2 Classifier</td>\n", "      <td>1.697542e+12</td>\n", "      <td>1.698224e+12</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>{'$oid': '652e6e82df32760044f21ca5'}</td>\n", "      <td>t_b</td>\n", "      <td>t_B</td>\n", "      <td>user</td>\n", "      <td>651eb6c6899c400045a59f80</td>\n", "      <td>right</td>\n", "      <td>False</td>\n", "      <td>651eb6c6899c400045a59f80</td>\n", "      <td>simbamangu</td>\n", "      <td>1.697542e+12</td>\n", "      <td>1.698224e+12</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>{'$oid': '653a60f36f30500045ef6b69'}</td>\n", "      <td>sh01-l_85</td>\n", "      <td>SH01-L_85</td>\n", "      <td>user</td>\n", "      <td>6524f99d62e3240045e45638</td>\n", "      <td>left</td>\n", "      <td>True</td>\n", "      <td>6523b8b118f3b3004425b07f</td>\n", "      <td>jmmbaga</td>\n", "      <td>1.698325e+12</td>\n", "      <td>1.698392e+12</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>85</td>\n", "      <td>[6525024e62e3240045e4563f]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>{'$oid': '653a61286f30500045ef6b6b'}</td>\n", "      <td>sh01-l_159</td>\n", "      <td>SH01-L_159</td>\n", "      <td>user</td>\n", "      <td>6524fa4562e3240045e45639</td>\n", "      <td>left</td>\n", "      <td>True</td>\n", "      <td>6523b8b118f3b3004425b07f</td>\n", "      <td>hgeorge</td>\n", "      <td>1.698325e+12</td>\n", "      <td>1.698396e+12</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>85</td>\n", "      <td>[6525029762e3240045e45642]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>{'$oid': '653a62096f30500045ef6b6d'}</td>\n", "      <td>sh02-l_100</td>\n", "      <td>SH02-L_100</td>\n", "      <td>user</td>\n", "      <td>65279287b2decd004564a4c4</td>\n", "      <td>left</td>\n", "      <td>True</td>\n", "      <td>6523b8b118f3b3004425b07f</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1.698325e+12</td>\n", "      <td>1.698391e+12</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>False</td>\n", "      <td>100</td>\n", "      <td>[6527a53eb2decd004564a4f0]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                    _id        name displayName taskType  \\\n", "0  {'$oid': '652e6e67df32760044f21ca3'}         t_a         t_A       ml   \n", "1  {'$oid': '652e6e82df32760044f21ca5'}         t_b         t_B     user   \n", "2  {'$oid': '653a60f36f30500045ef6b69'}   sh01-l_85   SH01-L_85     user   \n", "3  {'$oid': '653a61286f30500045ef6b6b'}  sh01-l_159  SH01-L_159     user   \n", "4  {'$oid': '653a62096f30500045ef6b6d'}  sh02-l_100  SH02-L_100     user   \n", "\n", "                   assignee orientation  randomized           createdByUserId  \\\n", "0                     ml-v2       right       False  651eb6c6899c400045a59f80   \n", "1  651eb6c6899c400045a59f80       right       False  651eb6c6899c400045a59f80   \n", "2  6524f99d62e3240045e45638        left        True  6523b8b118f3b3004425b07f   \n", "3  6524fa4562e3240045e45639        left        True  6523b8b118f3b3004425b07f   \n", "4  65279287b2decd004564a4c4        left        True  6523b8b118f3b3004425b07f   \n", "\n", "         assigneeDiplayName     createdAt     updatedAt  progressAnnotation  \\\n", "0  ML Config: V2 Classifier  1.697542e+12  1.698224e+12                 1.0   \n", "1                simbamangu  1.697542e+12  1.698224e+12                 1.0   \n", "2                   jmmbaga  1.698325e+12  1.698392e+12                 1.0   \n", "3                   hgeorge  1.698325e+12  1.698396e+12                 1.0   \n", "4                  mshirima  1.698325e+12  1.698391e+12                 1.0   \n", "\n", "   progressGroundTruth  progressLineDivision  sequencingComplete  imageCount  \\\n", "0                  1.0                     0                True           0   \n", "1                  1.0                     0                True           0   \n", "2                  0.0                     0               False          85   \n", "3                  0.0                     0               False          85   \n", "4                  0.0                     0               False         100   \n", "\n", "                       tagIds  \n", "0                        None  \n", "1                        None  \n", "2  [6525024e62e3240045e4563f]  \n", "3  [6525029762e3240045e45642]  \n", "4  [6527a53eb2decd004564a4f0]  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["taskzm = pd.read_json(homedir+\"/labriefs/_media/taskdbdata24Nov.json\")\n", "taskzm.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "acd192b9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>displayName</th>\n", "      <th>assigneeDiplayName</th>\n", "      <th>imageCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>t_A</td>\n", "      <td>ML Config: V2 Classifier</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>t_B</td>\n", "      <td>simbamangu</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SH01-L_85</td>\n", "      <td>jmmbaga</td>\n", "      <td>85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SH01-L_159</td>\n", "      <td>hgeorge</td>\n", "      <td>85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SH02-L_100</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>100</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  displayName        assigneeDiplayName  imageCount\n", "0         t_A  ML Config: V2 Classifier           0\n", "1         t_B                simbamangu           0\n", "2   SH01-L_85                   jmmbaga          85\n", "3  SH01-L_159                   hgeorge          85\n", "4  SH02-L_100                  mshirima         100"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["filterzm = taskzm[[\"displayName\",\"assigneeDiplayName\",\"imageCount\"]]\n", "filterzm.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "1625a8ab", "metadata": {}, "outputs": [], "source": ["#define the wildcard pattern\n", "pattern2 = \"A2\""]}, {"cell_type": "code", "execution_count": 8, "id": "9eba0136", "metadata": {}, "outputs": [], "source": ["# Filter the DataFrame based on the pattern\n", "filter_df = filterzm[filterzm['displayName'].str.contains(pattern2, case=False, na=False, regex=True)]"]}, {"cell_type": "code", "execution_count": 9, "id": "bf17153c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>displayName</th>\n", "      <th>assigneeDiplayName</th>\n", "      <th>imageCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>252</th>\n", "      <td>A201-L</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>192</td>\n", "    </tr>\n", "    <tr>\n", "      <th>253</th>\n", "      <td>A201-L_10</td>\n", "      <td>hgeorge</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>254</th>\n", "      <td>A201-L_184</td>\n", "      <td>hgeorge</td>\n", "      <td>184</td>\n", "    </tr>\n", "    <tr>\n", "      <th>255</th>\n", "      <td>A202-L_362</td>\n", "      <td>wmagesa</td>\n", "      <td>178</td>\n", "    </tr>\n", "    <tr>\n", "      <th>256</th>\n", "      <td>A202-L_531</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>179</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    displayName assigneeDiplayName  imageCount\n", "252      A201-<PERSON>         192\n", "253   A201-L_10            hgeorge          10\n", "254  A201-L_184            hgeorge         184\n", "255  A202-L_362            wmagesa         178\n", "256  A202-L_531           mshirima         179"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["filter_df.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "50cdfb70", "metadata": {}, "outputs": [{"data": {"text/plain": ["(68, 3)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["filter_df.shape"]}, {"cell_type": "code", "execution_count": 11, "id": "2a0197ee", "metadata": {}, "outputs": [], "source": ["filter_df.to_csv(\"A2tasks.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "6f041e44", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}