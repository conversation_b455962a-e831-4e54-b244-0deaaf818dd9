"""
GUI application for reviewing and labeling images with bounding boxes.
Provides interface for loading CSV files with bbox data and image directories.
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QLabel, QFileDialog, QGraphicsView, QGraphicsScene, QSplitter, QWidget, QMessageBox
from PyQt5.QtGui import QPixmap
from PyQt5.QtCore import Qt

class App(QMainWindow):
    """Main application window for the bounding box reviewer."""
    
    def __init__(self):
        super().__init__()
        self.title = 'Bounding Box Reviewer'
        self.init_ui()
        # Set reasonable default window size
        self.setGeometry(100, 100, 800, 600)

    def init_ui(self):
        """Initialize the user interface components."""
        self.setWindowTitle(self.title)

        # Create main layout
        layout = QVBoxLayout()

        # Add CSV file chooser button
        self.csv_button = QPushButton('Choose CSV File', self)
        self.csv_button.clicked.connect(self.load_csv)
        layout.addWidget(self.csv_button)

        # Add directory chooser button
        self.dir_button = QPushButton('Choose Image Directory', self)
        self.dir_button.clicked.connect(self.load_directory)
        layout.addWidget(self.dir_button)

        # Image display pane
        self.image_display = QGraphicsView(self)
        self.scene = QGraphicsScene()
        self.image_display.setScene(self.scene)

        # Splitter to separate controls and image display
        splitter = QSplitter(Qt.Horizontal)
        control_widget = QWidget()
        control_widget.setLayout(layout)
        splitter.addWidget(control_widget)
        splitter.addWidget(self.image_display)

        self.setCentralWidget(splitter)
        self.show()

    def load_csv(self):
        """Load and process CSV file containing bounding box data."""
        try:
            options = QFileDialog.Options()
            filePath, _ = QFileDialog.getOpenFileName(self, "Open CSV File", "", 
                                                    "CSV Files (*.csv);;All Files (*)", 
                                                    options=options)
            if filePath:
                # Load CSV data
                # TODO: Integrate CSV loading logic here
                pass
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load CSV: {str(e)}")

    def load_directory(self):
        """Load directory containing images to be processed."""
        try:
            options = QFileDialog.Options()
            dirPath = QFileDialog.getExistingDirectory(self, "Open Image Directory", 
                                                     "", options=options)
            if dirPath:
                # Load images from directory
                # TODO: Integrate image loading logic here
                pass
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load directory: {str(e)}")

    def display_image(self, image_path):
        """
        Display an image in the graphics view.
        
        Args:
            image_path (str): Path to the image file to display
        """
        try:
            pixmap = QPixmap(image_path)
            if pixmap.isNull():
                raise ValueError("Failed to load image")
            self.scene.clear()
            self.scene.addPixmap(pixmap)
            self.image_display.setScene(self.scene)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to display image: {str(e)}")

# Run the app
app = QApplication(sys.argv)
ex = App()
sys.exit(app.exec_())
