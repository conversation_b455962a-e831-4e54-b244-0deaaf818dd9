{"cells": [{"cell_type": "markdown", "id": "52a621fb", "metadata": {}, "source": ["### DAN transects 01 to 25 (L &R)\n", "#### Session 20220906A\n", "#### Bounding box analysis for each species"]}, {"cell_type": "code", "execution_count": 1, "id": "24d16aba", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 3, "id": "adea48c8", "metadata": {}, "outputs": [], "source": ["homedir = \"/home/<USER>/Dropbox/Conservation/MWSLab-2023\""]}, {"cell_type": "code", "execution_count": 4, "id": "a0022119", "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '/scoutexports/DANscout-export-annotations.csv'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m dandf \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m/scoutexports/DANscout-export-annotations.csv\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m      2\u001b[0m dandf\u001b[38;5;241m.\u001b[39mhead()\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/util/_decorators.py:211\u001b[0m, in \u001b[0;36mdeprecate_kwarg.<locals>._deprecate_kwarg.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    209\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    210\u001b[0m         kwargs[new_arg_name] \u001b[38;5;241m=\u001b[39m new_arg_value\n\u001b[0;32m--> 211\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/util/_decorators.py:331\u001b[0m, in \u001b[0;36mdeprecate_nonkeyword_arguments.<locals>.decorate.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    325\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(args) \u001b[38;5;241m>\u001b[39m num_allow_args:\n\u001b[1;32m    326\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[1;32m    327\u001b[0m         msg\u001b[38;5;241m.\u001b[39mformat(arguments\u001b[38;5;241m=\u001b[39m_format_argument_list(allow_args)),\n\u001b[1;32m    328\u001b[0m         \u001b[38;5;167;01mFutureWarning\u001b[39;00m,\n\u001b[1;32m    329\u001b[0m         stacklevel\u001b[38;5;241m=\u001b[39mfind_stack_level(),\n\u001b[1;32m    330\u001b[0m     )\n\u001b[0;32m--> 331\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/io/parsers/readers.py:950\u001b[0m, in \u001b[0;36mread_csv\u001b[0;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, squeeze, prefix, mangle_dupe_cols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, error_bad_lines, warn_bad_lines, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options)\u001b[0m\n\u001b[1;32m    935\u001b[0m kwds_defaults \u001b[38;5;241m=\u001b[39m _refine_defaults_read(\n\u001b[1;32m    936\u001b[0m     dialect,\n\u001b[1;32m    937\u001b[0m     delimiter,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    946\u001b[0m     defaults\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdelimiter\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m,\u001b[39m\u001b[38;5;124m\"\u001b[39m},\n\u001b[1;32m    947\u001b[0m )\n\u001b[1;32m    948\u001b[0m kwds\u001b[38;5;241m.\u001b[39mupdate(kwds_defaults)\n\u001b[0;32m--> 950\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/io/parsers/readers.py:605\u001b[0m, in \u001b[0;36m_read\u001b[0;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[1;32m    602\u001b[0m _validate_names(kwds\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnames\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[1;32m    604\u001b[0m \u001b[38;5;66;03m# Create the parser.\u001b[39;00m\n\u001b[0;32m--> 605\u001b[0m parser \u001b[38;5;241m=\u001b[39m \u001b[43mTextFileReader\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    607\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mor\u001b[39;00m iterator:\n\u001b[1;32m    608\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m parser\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/io/parsers/readers.py:1442\u001b[0m, in \u001b[0;36mTextFileReader.__init__\u001b[0;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[1;32m   1439\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39moptions[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m kwds[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhas_index_names\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m   1441\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles: IOHandles \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m-> 1442\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_engine\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mengine\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/io/parsers/readers.py:1735\u001b[0m, in \u001b[0;36mTextFileReader._make_engine\u001b[0;34m(self, f, engine)\u001b[0m\n\u001b[1;32m   1733\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode:\n\u001b[1;32m   1734\u001b[0m         mode \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m-> 1735\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;241m=\u001b[39m \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1736\u001b[0m \u001b[43m    \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1737\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1738\u001b[0m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1739\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcompression\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1740\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmemory_map\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmemory_map\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1741\u001b[0m \u001b[43m    \u001b[49m\u001b[43mis_text\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mis_text\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1742\u001b[0m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mencoding_errors\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstrict\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1743\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstorage_options\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1744\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1745\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1746\u001b[0m f \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhandles\u001b[38;5;241m.\u001b[39mhandle\n", "File \u001b[0;32m~/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/io/common.py:856\u001b[0m, in \u001b[0;36mget_handle\u001b[0;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[1;32m    851\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[1;32m    852\u001b[0m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[1;32m    853\u001b[0m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[1;32m    854\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mencoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mb\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs\u001b[38;5;241m.\u001b[39mmode:\n\u001b[1;32m    855\u001b[0m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[0;32m--> 856\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m    857\u001b[0m \u001b[43m            \u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    858\u001b[0m \u001b[43m            \u001b[49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    859\u001b[0m \u001b[43m            \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    860\u001b[0m \u001b[43m            \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    861\u001b[0m \u001b[43m            \u001b[49m\u001b[43mnewline\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    862\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    863\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    864\u001b[0m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[1;32m    865\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(handle, ioargs\u001b[38;5;241m.\u001b[39mmode)\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '/scoutexports/DANscout-export-annotations.csv'"]}], "source": ["dandf = pd.read_csv(homedir+\"/scoutexports/DANscout-export-annotations.csv\")\n", "dandf.head()"]}, {"cell_type": "code", "execution_count": 31, "id": "ceb82f1b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1371, 14)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["dandf.shape"]}, {"cell_type": "code", "execution_count": 11, "id": "7b81a520", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['Task Name', 'Task ID', 'Image Filename', 'Image ID', 'Box X', 'Box Y',\n", "       'Box W', 'Box H', 'Label', 'Label Confidence', 'Assignee', 'Timestamp',\n", "       'Is Ground Truth', 'Excluded By Line'],\n", "      dtype='object')"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["annoscoutdf.columns"]}, {"cell_type": "code", "execution_count": 32, "id": "53b9717b", "metadata": {}, "outputs": [], "source": ["dandf.columns=['TaskName', 'TaskID', 'ImageFilename', 'ImageID', 'BoxX', 'BoxY',\n", "       'BoxW', 'BoxH', 'Label', 'LabelConfidence', 'Assignee', 'Timestamp',\n", "       'IsGroundTruth', 'ExcludedByLine']"]}, {"cell_type": "code", "execution_count": 33, "id": "be1eea4c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6482.702368</td>\n", "      <td>1211.366282</td>\n", "      <td>41.640716</td>\n", "      <td>35.962436</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6102.257645</td>\n", "      <td>1084.551374</td>\n", "      <td>43.533476</td>\n", "      <td>28.391397</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6230.965313</td>\n", "      <td>1126.192090</td>\n", "      <td>39.747956</td>\n", "      <td>26.498637</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6405.099216</td>\n", "      <td>1175.403845</td>\n", "      <td>-39.747956</td>\n", "      <td>30.284157</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_201_2</td>\n", "      <td>6530df2905667e0045cadc59</td>\n", "      <td>KES22_IIM-L_20220906A-062738_M0309381.jpg</td>\n", "      <td>6530d4680e8be9004ca31544</td>\n", "      <td>6616.536906</td>\n", "      <td>42.693538</td>\n", "      <td>31.230503</td>\n", "      <td>14.574235</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1697703725045</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        TaskName                    TaskID  \\\n", "0  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "1  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "2  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "3  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "4  DAN05-L_201_2  6530df2905667e0045cadc59   \n", "\n", "                               ImageFilename                   ImageID  \\\n", "0  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "1  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "2  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "3  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "4  KES22_IIM-L_20220906A-062738_M0309381.jpg  6530d4680e8be9004ca31544   \n", "\n", "          BoxX         BoxY       BoxW       BoxH          Label  \\\n", "0  6482.702368  1211.366282  41.640716  35.962436  vervet monkey   \n", "1  6102.257645  1084.551374  43.533476  28.391397  vervet monkey   \n", "2  6230.965313  1126.192090  39.747956  26.498637  vervet monkey   \n", "3  6405.099216  1175.403845 -39.747956  30.284157  vervet monkey   \n", "4  6616.536906    42.693538  31.230503  14.574235           bird   \n", "\n", "   LabelConfidence Assignee      Timestamp  IsGroundTruth  ExcludedByLine  \n", "0              NaN  wmagesa  1697717018183          False             NaN  \n", "1              NaN  wmagesa  1697717018183          False             NaN  \n", "2              NaN  wmagesa  1697717018183          False             NaN  \n", "3              NaN  wmagesa  1697717018183          False             NaN  \n", "4              NaN   zrahim  1697703725045          False             NaN  "]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["dandf.head()"]}, {"cell_type": "code", "execution_count": 37, "id": "5b4e470b", "metadata": {}, "outputs": [], "source": ["dantasks=dandf['TaskName'].unique()"]}, {"cell_type": "code", "execution_count": 38, "id": "9433d4a7", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["array(['DAN05-L_174_2', 'DAN05-L_201_2', 'DAN05-R_58', 'DAN05-R_58_2',\n", "       'DAN05-R_116', 'DAN05-R_116_2', 'DAN05-R_174', 'DAN05-R_174_2',\n", "       'DAN05-R_201', 'DAN05-R_201_2', 'DAN05-L_100', 'DAN05-L_200',\n", "       'DAN05-L_600', 'DAN05-L_700', 'DAN05-L_900', 'DAN05-L_100_2',\n", "       'DAN05-L_200_2', 'DAN05-L_600_2', 'DAN05-L_700_2', 'DAN05-L_900_2',\n", "       'DAN15-L_128', 'DAN16-L_256', 'DAN17-L_120', 'DAN17-L_241',\n", "       'DAN18-L_228', 'DAN19-L_123', 'DAN19-L_246', 'DAN16-L_256_2',\n", "       'DAN17-L_120_2', 'DAN17-L_241_2', 'DAN18-L_114_2', 'DAN15-L_128_L',\n", "       'DAN19-L_123_2', 'DAN19-L_246_2', 'DAN20-L', 'DAN20-L_2',\n", "       'DAN21-L_091_2', 'DAN10-L_070', 'DAN10-L_071_140',\n", "       'DAN10-L_071_280', 'DAN11-L_072_124', 'DAN11-L_072_248',\n", "       'DAN12-L_073_101', 'DAN12-L_073_221', 'DAN12-L_074_149',\n", "       'DAN13-L_074_240', 'DAN13-L_075_227', 'DAN01-L', 'DAN03-L_060_120',\n", "       'DAN03-L_061_72', 'DAN06-L_115', 'DAN06-L_230', 'DAN07-L_128',\n", "       'DAN09-L_174', 'DAN11-L_072_124_2', 'DAN12-L_073_101_2',\n", "       'DAN12-L_073_221_2', 'DAN12-L_074_149_2', 'DAN06-L_115_2',\n", "       'DAN10-L_070_2', 'DAN10-L_071_140_2', 'DAN10-L_071_280_2',\n", "       'DAN03-R_061_72', 'DAN04-R', 'DAN06-R_115', 'DAN06-R_230',\n", "       'DAN07-R_128', 'DAN07-R_255', 'DAN08-R', 'DAN09-R_148',\n", "       'DAN09-R_174', 'DAN10-R_070', 'DAN10-R_071_140', 'DAN10-R_071_280',\n", "       'DAN11-R_072_124', 'DAN12-R_073_172', 'DAN12-R_074_149',\n", "       'DAN13-R_074_240', 'DAN14-R_253', 'DAN05-R_100', 'DAN01-R',\n", "       'DAN02-R', 'DAN16-R_133', 'DAN17-R_126', 'DAN17-R_242',\n", "       'DAN18-R_085_50', 'DAN18-R_085_90', 'DAN19-R_085_173',\n", "       'DAN20-R_09_71', 'DAN20-R_09_121', 'DAN25-R_09_441', 'DAN08-L',\n", "       'DAN09-L_148'], dtype=object)"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["dantasks"]}, {"cell_type": "code", "execution_count": 19, "id": "aff51fb5", "metadata": {}, "outputs": [], "source": ["dandf =pd.DataFrame(dantasks)"]}, {"cell_type": "code", "execution_count": 21, "id": "dc03c6fc", "metadata": {}, "outputs": [], "source": ["dandf.to_csv(\"DANTasks.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "fbbdb6e0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 42, "id": "1363fb08", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_100_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_200_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_600_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_700_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_900_2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        TaskName\n", "0  DAN05-L_100_2\n", "1  DAN05-L_200_2\n", "2  DAN05-L_600_2\n", "3  DAN05-L_700_2\n", "4  DAN05-L_900_2"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["#the identified repeated tasks\n", "deletetasksdf = pd.read_csv(\"scoutexports/deleterepeattasks.txt\")\n", "deletetasksdf.head()"]}, {"cell_type": "code", "execution_count": 45, "id": "634a8d09", "metadata": {}, "outputs": [{"data": {"text/plain": ["(24, 1)"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["deletetasksdf.shape"]}, {"cell_type": "code", "execution_count": null, "id": "497df9c8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 53, "id": "a2ca8b86", "metadata": {}, "outputs": [], "source": ["#filtering the repeated tasks from all DAN tasks\n", "\n", "# Create a new DataFrame without the records meeting the condition\n", "filteredtasks_df = dandf[~dandf['TaskName'].isin(deletetasksdf['TaskName'])]\n"]}, {"cell_type": "code", "execution_count": 54, "id": "f2b9dd81", "metadata": {}, "outputs": [{"data": {"text/plain": ["(999, 14)"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["filteredtasks_df.shape  #deleted 1371-999=372 records"]}, {"cell_type": "code", "execution_count": 57, "id": "e4bd0889", "metadata": {}, "outputs": [], "source": ["filteredtasks_df.to_csv(\"uniqueDANtasks.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 58, "id": "5243f37a", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6482.702368</td>\n", "      <td>1211.366282</td>\n", "      <td>41.640716</td>\n", "      <td>35.962436</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6102.257645</td>\n", "      <td>1084.551374</td>\n", "      <td>43.533476</td>\n", "      <td>28.391397</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6230.965313</td>\n", "      <td>1126.192090</td>\n", "      <td>39.747956</td>\n", "      <td>26.498637</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6405.099216</td>\n", "      <td>1175.403845</td>\n", "      <td>-39.747956</td>\n", "      <td>30.284157</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_201_2</td>\n", "      <td>6530df2905667e0045cadc59</td>\n", "      <td>KES22_IIM-L_20220906A-062738_M0309381.jpg</td>\n", "      <td>6530d4680e8be9004ca31544</td>\n", "      <td>6616.536906</td>\n", "      <td>42.693538</td>\n", "      <td>31.230503</td>\n", "      <td>14.574235</td>\n", "      <td>bird</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1697703725045</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        TaskName                    TaskID  \\\n", "0  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "1  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "2  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "3  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "4  DAN05-L_201_2  6530df2905667e0045cadc59   \n", "\n", "                               ImageFilename                   ImageID  \\\n", "0  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "1  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "2  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "3  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "4  KES22_IIM-L_20220906A-062738_M0309381.jpg  6530d4680e8be9004ca31544   \n", "\n", "          BoxX         BoxY       BoxW       BoxH          Label  \\\n", "0  6482.702368  1211.366282  41.640716  35.962436  vervet monkey   \n", "1  6102.257645  1084.551374  43.533476  28.391397  vervet monkey   \n", "2  6230.965313  1126.192090  39.747956  26.498637  vervet monkey   \n", "3  6405.099216  1175.403845 -39.747956  30.284157  vervet monkey   \n", "4  6616.536906    42.693538  31.230503  14.574235           bird   \n", "\n", "   LabelConfidence Assignee      Timestamp  IsGroundTruth  ExcludedByLine  \n", "0              NaN  wmagesa  1697717018183          False             NaN  \n", "1              NaN  wmagesa  1697717018183          False             NaN  \n", "2              NaN  wmagesa  1697717018183          False             NaN  \n", "3              NaN  wmagesa  1697717018183          False             NaN  \n", "4              NaN   zrahim  1697703725045          False             NaN  "]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["bboxdf = pd.read_csv(\"uniqueDANtasks.csv\")\n", "bboxdf.head()"]}, {"cell_type": "code", "execution_count": 59, "id": "1c5ba84b", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["elephant            847\n", "white_bones          59\n", "zebra                15\n", "giraffe              14\n", "unknown mammal       14\n", "bird                 11\n", "impala               10\n", "vervet monkey         7\n", "kudu                  6\n", "buffalo               5\n", "unknown antelope      4\n", "gazelle_grants        3\n", "duiker                2\n", "gazelle_thomsons      2\n", "Name: Label, dtype: int64"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["bboxdf['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 78, "id": "c8569baa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>251</th>\n", "      <td>DAN17-L_120</td>\n", "      <td>65360a7b5aee9b00457dae28</td>\n", "      <td>KES22_IIM-L_20220906A-083326_M0303143.jpg</td>\n", "      <td>653232db8cfd62004c69aae5</td>\n", "      <td>1894.420736</td>\n", "      <td>1492.546185</td>\n", "      <td>122.839614</td>\n", "      <td>131.167724</td>\n", "      <td>gazelle_grants</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1698055384082</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>531</th>\n", "      <td>DAN09-L_174</td>\n", "      <td>653780b65aee9b00457daefb</td>\n", "      <td>KES22_IIM-L_20220906A-070101_M0300380.jpg</td>\n", "      <td>65323b998cfd62004c69ae2a</td>\n", "      <td>5181.429686</td>\n", "      <td>3874.437318</td>\n", "      <td>78.859085</td>\n", "      <td>125.398872</td>\n", "      <td>gazelle_grants</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1698235551907</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>739</th>\n", "      <td>DAN07-R_255</td>\n", "      <td>6537b7d65aee9b00457daf4b</td>\n", "      <td>KES22_IIM-R_20220906A-064951_M0400427.jpg</td>\n", "      <td>6537a77726599d004cfc8cd1</td>\n", "      <td>804.104108</td>\n", "      <td>4478.161785</td>\n", "      <td>-122.813328</td>\n", "      <td>85.322944</td>\n", "      <td>gazelle_grants</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1698226691580</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        TaskName                    TaskID  \\\n", "251  DAN17-L_120  65360a7b5aee9b00457dae28   \n", "531  DAN09-L_174  653780b65aee9b00457daefb   \n", "739  DAN07-R_255  6537b7d65aee9b00457daf4b   \n", "\n", "                                 ImageFilename                   ImageID  \\\n", "251  KES22_IIM-L_20220906A-083326_M0303143.jpg  653232db8cfd62004c69aae5   \n", "531  KES22_IIM-L_20220906A-070101_M0300380.jpg  65323b998cfd62004c69ae2a   \n", "739  KES22_IIM-R_20220906A-064951_M0400427.jpg  6537a77726599d004cfc8cd1   \n", "\n", "            BoxX         BoxY        BoxW        BoxH           Label  \\\n", "251  1894.420736  1492.546185  122.839614  131.167724  gazelle_grants   \n", "531  5181.429686  3874.437318   78.859085  125.398872  gazelle_grants   \n", "739   804.104108  4478.161785 -122.813328   85.322944  gazelle_grants   \n", "\n", "     LabelConfidence Assignee      Timestamp  IsGroundTruth  ExcludedByLine  \n", "251              NaN  wmagesa  1698055384082          False             NaN  \n", "531              NaN  wmagesa  1698235551907          False             NaN  \n", "739              NaN  wmagesa  1698226691580          False             NaN  "]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["bboxdf[bboxdf['Label']=='gazelle_grants']"]}, {"cell_type": "code", "execution_count": 79, "id": "1a51c6ec", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>253</th>\n", "      <td>DAN17-L_120</td>\n", "      <td>65360a7b5aee9b00457dae28</td>\n", "      <td>KES22_IIM-L_20220906A-083326_M0303143.jpg</td>\n", "      <td>653232db8cfd62004c69aae5</td>\n", "      <td>3774.954195</td>\n", "      <td>1815.723113</td>\n", "      <td>102.019341</td>\n", "      <td>135.331779</td>\n", "      <td>gazelle_thomsons</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1698055384082</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>727</th>\n", "      <td>DAN07-R_255</td>\n", "      <td>6537b7d65aee9b00457daf4b</td>\n", "      <td>KES22_IIM-R_20220906A-064646_M0400335.jpg</td>\n", "      <td>6537a4df26599d004cfc8bdf</td>\n", "      <td>6469.030476</td>\n", "      <td>226.235079</td>\n", "      <td>51.710875</td>\n", "      <td>42.661472</td>\n", "      <td>gazelle_thomsons</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1698223586777</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        TaskName                    TaskID  \\\n", "253  DAN17-L_120  65360a7b5aee9b00457dae28   \n", "727  DAN07-R_255  6537b7d65aee9b00457daf4b   \n", "\n", "                                 ImageFilename                   ImageID  \\\n", "253  KES22_IIM-L_20220906A-083326_M0303143.jpg  653232db8cfd62004c69aae5   \n", "727  KES22_IIM-R_20220906A-064646_M0400335.jpg  6537a4df26599d004cfc8bdf   \n", "\n", "            BoxX         BoxY        BoxW        BoxH             Label  \\\n", "253  3774.954195  1815.723113  102.019341  135.331779  gazelle_thomsons   \n", "727  6469.030476   226.235079   51.710875   42.661472  gazelle_thomsons   \n", "\n", "     LabelConfidence Assignee      Timestamp  IsGroundTruth  ExcludedByLine  \n", "253              NaN  wmagesa  1698055384082          False             NaN  \n", "727              NaN  wmagesa  1698223586777          False             NaN  "]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["bboxdf[bboxdf['Label']=='gazelle_thomsons']"]}, {"cell_type": "markdown", "id": "53fa0b74", "metadata": {}, "source": ["### NG25 transects 01 to 05 (L &R)\n", "#### Session 20220923B\n", "#### Bounding box analysis for each species"]}, {"cell_type": "code", "execution_count": 61, "id": "e3d5c15c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145132_M0804846.jpg</td>\n", "      <td>6525067833c806004cc2d6b7</td>\n", "      <td>6360.960245</td>\n", "      <td>625.165623</td>\n", "      <td>54.652005</td>\n", "      <td>25.326539</td>\n", "      <td>crocodile</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696929902940</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>652506a333c806004cc2d6c7</td>\n", "      <td>193.916465</td>\n", "      <td>397.234940</td>\n", "      <td>39.958544</td>\n", "      <td>63.463570</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696932493527</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>652506a333c806004cc2d6c7</td>\n", "      <td>26.163200</td>\n", "      <td>457.388800</td>\n", "      <td>-25.228800</td>\n", "      <td>46.720000</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696932493527</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>6525065633c806004cc2d6aa</td>\n", "      <td>594.675064</td>\n", "      <td>2266.229102</td>\n", "      <td>135.741047</td>\n", "      <td>56.881963</td>\n", "      <td>hippo</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696938797992</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>6525065633c806004cc2d6aa</td>\n", "      <td>771.784811</td>\n", "      <td>2336.038783</td>\n", "      <td>127.984416</td>\n", "      <td>120.227785</td>\n", "      <td>hippo</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696938797992</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Task Name                   Task ID  \\\n", "0  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "1  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "2  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "3  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "4  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_WOT-L_20220923B-145132_M0804846.jpg  6525067833c806004cc2d6b7   \n", "1  KES22_WOT-L_20220923B-145454_M0804947.jpg  652506a333c806004cc2d6c7   \n", "2  KES22_WOT-L_20220923B-145454_M0804947.jpg  652506a333c806004cc2d6c7   \n", "3  KES22_WOT-L_20220923B-145256_M0804888.jpg  6525065633c806004cc2d6aa   \n", "4  KES22_WOT-L_20220923B-145256_M0804888.jpg  6525065633c806004cc2d6aa   \n", "\n", "         Box X        Box Y       Box W       Box H       Label  \\\n", "0  6360.960245   625.165623   54.652005   25.326539   crocodile   \n", "1   193.916465   397.234940   39.958544   63.463570  red lechwe   \n", "2    26.163200   457.388800  -25.228800   46.720000  red lechwe   \n", "3   594.675064  2266.229102  135.741047   56.881963       hippo   \n", "4   771.784811  2336.038783  127.984416  120.227785       hippo   \n", "\n", "   Label Confidence Assignee      Timestamp  Is Ground Truth  Excluded By Line  \n", "0               NaN  wmagesa  1696929902940            False               NaN  \n", "1               NaN  wmagesa  1696932493527            False               NaN  \n", "2               NaN  wmagesa  1696932493527            False               NaN  \n", "3               NaN  wmagesa  1696938797992            False               NaN  \n", "4               NaN  wmagesa  1696938797992            False               NaN  "]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25df = pd.read_csv(\"scoutexports/NG25Tasksannotations.csv\")\n", "ng25df.head()"]}, {"cell_type": "code", "execution_count": 62, "id": "22b51e0b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7142, 14)"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25df.shape"]}, {"cell_type": "code", "execution_count": 64, "id": "4ea01d3d", "metadata": {}, "outputs": [], "source": ["ng25df.columns=['TaskName', 'TaskID', 'ImageFilename', 'ImageID', 'BoxX', 'BoxY',\n", "       'BoxW', 'BoxH', 'Label', 'LabelConfidence', 'Assignee', 'Timestamp',\n", "       'IsGroundTruth', 'ExcludedByLine']"]}, {"cell_type": "code", "execution_count": 65, "id": "c1d61928", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145132_M0804846.jpg</td>\n", "      <td>6525067833c806004cc2d6b7</td>\n", "      <td>6360.960245</td>\n", "      <td>625.165623</td>\n", "      <td>54.652005</td>\n", "      <td>25.326539</td>\n", "      <td>crocodile</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696929902940</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>652506a333c806004cc2d6c7</td>\n", "      <td>193.916465</td>\n", "      <td>397.234940</td>\n", "      <td>39.958544</td>\n", "      <td>63.463570</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696932493527</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>652506a333c806004cc2d6c7</td>\n", "      <td>26.163200</td>\n", "      <td>457.388800</td>\n", "      <td>-25.228800</td>\n", "      <td>46.720000</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696932493527</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>6525065633c806004cc2d6aa</td>\n", "      <td>594.675064</td>\n", "      <td>2266.229102</td>\n", "      <td>135.741047</td>\n", "      <td>56.881963</td>\n", "      <td>hippo</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696938797992</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>6525065633c806004cc2d6aa</td>\n", "      <td>771.784811</td>\n", "      <td>2336.038783</td>\n", "      <td>127.984416</td>\n", "      <td>120.227785</td>\n", "      <td>hippo</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696938797992</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          TaskName                    TaskID  \\\n", "0  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "1  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "2  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "3  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "4  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "\n", "                               ImageFilename                   ImageID  \\\n", "0  KES22_WOT-L_20220923B-145132_M0804846.jpg  6525067833c806004cc2d6b7   \n", "1  KES22_WOT-L_20220923B-145454_M0804947.jpg  652506a333c806004cc2d6c7   \n", "2  KES22_WOT-L_20220923B-145454_M0804947.jpg  652506a333c806004cc2d6c7   \n", "3  KES22_WOT-L_20220923B-145256_M0804888.jpg  6525065633c806004cc2d6aa   \n", "4  KES22_WOT-L_20220923B-145256_M0804888.jpg  6525065633c806004cc2d6aa   \n", "\n", "          BoxX         BoxY        BoxW        BoxH       Label  \\\n", "0  6360.960245   625.165623   54.652005   25.326539   crocodile   \n", "1   193.916465   397.234940   39.958544   63.463570  red lechwe   \n", "2    26.163200   457.388800  -25.228800   46.720000  red lechwe   \n", "3   594.675064  2266.229102  135.741047   56.881963       hippo   \n", "4   771.784811  2336.038783  127.984416  120.227785       hippo   \n", "\n", "   LabelConfidence Assignee      Timestamp  IsGroundTruth  ExcludedByLine  \n", "0              NaN  wmagesa  1696929902940          False             NaN  \n", "1              NaN  wmagesa  1696932493527          False             NaN  \n", "2              NaN  wmagesa  1696932493527          False             NaN  \n", "3              NaN  wmagesa  1696938797992          False             NaN  \n", "4              NaN  wmagesa  1696938797992          False             NaN  "]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25df.head()"]}, {"cell_type": "code", "execution_count": 66, "id": "4aa71527", "metadata": {}, "outputs": [], "source": ["ng25tasks=ng25df['TaskName'].unique()"]}, {"cell_type": "code", "execution_count": 67, "id": "f1e2e70b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(122, 1)"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25tasksdf = pd.DataFrame(ng25tasks)\n", "ng25tasksdf.shape"]}, {"cell_type": "code", "execution_count": 70, "id": "84f7b1b0", "metadata": {}, "outputs": [], "source": ["ng25tasksdf.to_csv(\"NG25Tasks.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "7a6632a4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 71, "id": "9905e765", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-03-L_109ML</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-05-L_1-150_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-05-L_116-265_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-05-R_1-150_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-05-R_116-195_2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              TaskName\n", "0      NG25-03-L_109ML\n", "1    NG25-05-L_1-150_2\n", "2  NG25-05-L_116-265_2\n", "3    NG25-05-R_1-150_2\n", "4  NG25-05-R_116-195_2"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["#the identified repeated tasks\n", "deleteNG25tasksdf = pd.read_csv(\"scoutexports/deleteNG25repeattasks.txt\")\n", "deleteNG25tasksdf.head()"]}, {"cell_type": "code", "execution_count": 72, "id": "34a98542", "metadata": {}, "outputs": [{"data": {"text/plain": ["(23, 1)"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["deleteNG25tasksdf.shape"]}, {"cell_type": "code", "execution_count": 73, "id": "abd1bec7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(5713, 14)"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["#filtering the repeated tasks from all DAN tasks\n", "\n", "# Create a new DataFrame without the records meeting the condition\n", "filteredng25tasks_df = ng25df[~ng25df['TaskName'].isin(deleteNG25tasksdf['TaskName'])]\n", "filteredng25tasks_df.shape  # deleted 7142-5713 = 1,429 records"]}, {"cell_type": "code", "execution_count": 74, "id": "5b9c430d", "metadata": {}, "outputs": [], "source": ["filteredng25tasks_df.to_csv(\"uniqueNG25tasks.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 76, "id": "ea0d0f9f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145132_M0804846.jpg</td>\n", "      <td>6525067833c806004cc2d6b7</td>\n", "      <td>6360.960245</td>\n", "      <td>625.165623</td>\n", "      <td>54.652005</td>\n", "      <td>25.326539</td>\n", "      <td>crocodile</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696929902940</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>652506a333c806004cc2d6c7</td>\n", "      <td>193.916465</td>\n", "      <td>397.234940</td>\n", "      <td>39.958544</td>\n", "      <td>63.463570</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696932493527</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>652506a333c806004cc2d6c7</td>\n", "      <td>26.163200</td>\n", "      <td>457.388800</td>\n", "      <td>-25.228800</td>\n", "      <td>46.720000</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696932493527</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>6525065633c806004cc2d6aa</td>\n", "      <td>594.675064</td>\n", "      <td>2266.229102</td>\n", "      <td>135.741047</td>\n", "      <td>56.881963</td>\n", "      <td>hippo</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696938797992</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>6525065633c806004cc2d6aa</td>\n", "      <td>771.784811</td>\n", "      <td>2336.038783</td>\n", "      <td>127.984416</td>\n", "      <td>120.227785</td>\n", "      <td>hippo</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696938797992</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          TaskName                    TaskID  \\\n", "0  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "1  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "2  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "3  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "4  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "\n", "                               ImageFilename                   ImageID  \\\n", "0  KES22_WOT-L_20220923B-145132_M0804846.jpg  6525067833c806004cc2d6b7   \n", "1  KES22_WOT-L_20220923B-145454_M0804947.jpg  652506a333c806004cc2d6c7   \n", "2  KES22_WOT-L_20220923B-145454_M0804947.jpg  652506a333c806004cc2d6c7   \n", "3  KES22_WOT-L_20220923B-145256_M0804888.jpg  6525065633c806004cc2d6aa   \n", "4  KES22_WOT-L_20220923B-145256_M0804888.jpg  6525065633c806004cc2d6aa   \n", "\n", "          BoxX         BoxY        BoxW        BoxH       Label  \\\n", "0  6360.960245   625.165623   54.652005   25.326539   crocodile   \n", "1   193.916465   397.234940   39.958544   63.463570  red lechwe   \n", "2    26.163200   457.388800  -25.228800   46.720000  red lechwe   \n", "3   594.675064  2266.229102  135.741047   56.881963       hippo   \n", "4   771.784811  2336.038783  127.984416  120.227785       hippo   \n", "\n", "   LabelConfidence Assignee      Timestamp  IsGroundTruth  ExcludedByLine  \n", "0              NaN  wmagesa  1696929902940          False             NaN  \n", "1              NaN  wmagesa  1696932493527          False             NaN  \n", "2              NaN  wmagesa  1696932493527          False             NaN  \n", "3              NaN  wmagesa  1696938797992          False             NaN  \n", "4              NaN  wmagesa  1696938797992          False             NaN  "]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["bboxng25df = pd.read_csv(\"uniqueNG25tasks.csv\")\n", "bboxng25df.head()"]}, {"cell_type": "code", "execution_count": 77, "id": "83b5f2e0", "metadata": {"scrolled": false}, "outputs": [{"data": {"text/plain": ["red lechwe          2980\n", "bird                1413\n", "zebra                247\n", "elephant             211\n", "buffalo              196\n", "impala               101\n", "white_bones           73\n", "cow                   70\n", "warthog               56\n", "hippo                 51\n", "giraffe               51\n", "crocodile             42\n", "sitatunga             41\n", "ostrich               36\n", "elephant bull         21\n", "unknown animal        20\n", "donkey                19\n", "reedbuck              19\n", "roof_mabati           17\n", "human                  9\n", "vervet monkey          7\n", "roan                   7\n", "canoe                  4\n", "unknown mammal         4\n", "eland                  2\n", "waterbuck              2\n", "bushpig                2\n", "puku                   2\n", "wildebeest             2\n", "lion                   2\n", "gazelle_thomsons       1\n", "bushback               1\n", "sheep                  1\n", "oribi                  1\n", "roof_grass             1\n", "goat                   1\n", "Name: Label, dtype: int64"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["bboxng25df['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "95b71284", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "6bf69c47", "metadata": {}, "source": ["### SH Block \n", "#### Session 20220904A (SH) and 20220905A (SH-B)\n", "#### Bounding box analysis for each species"]}, {"cell_type": "code", "execution_count": 2, "id": "9f3b89ad", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SH01-L_159</th>\n", "      <th>653a61286f30500045ef6b6b</th>\n", "      <th>KES22_IIM-L_20220904A-060827_M0309504.jpg</th>\n", "      <th>653a4e074a2456004cd8a5ce</th>\n", "      <th>2992.2844077128157</th>\n", "      <th>3582.150410156599</th>\n", "      <th>83.2813419134031</th>\n", "      <th>108.26574448742355</th>\n", "      <th>elephant</th>\n", "      <th>Unnamed: 9</th>\n", "      <th>hgeorge</th>\n", "      <th>1698389707201</th>\n", "      <th>false</th>\n", "      <th>Unnamed: 13</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>3119.288454</td>\n", "      <td>3278.173512</td>\n", "      <td>110.347778</td>\n", "      <td>127.004046</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>3221.308098</td>\n", "      <td>2886.751205</td>\n", "      <td>112.429812</td>\n", "      <td>189.465053</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>3106.796253</td>\n", "      <td>2818.044098</td>\n", "      <td>81.199308</td>\n", "      <td>106.183711</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>312.139451</td>\n", "      <td>3096.658157</td>\n", "      <td>77.035241</td>\n", "      <td>74.953208</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>133.084565</td>\n", "      <td>3080.001889</td>\n", "      <td>-70.789141</td>\n", "      <td>116.593879</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   SH01-L_159  653a61286f30500045ef6b6b  \\\n", "0  SH01-L_159  653a61286f30500045ef6b6b   \n", "1  SH01-L_159  653a61286f30500045ef6b6b   \n", "2  SH01-L_159  653a61286f30500045ef6b6b   \n", "3  SH01-L_159  653a61286f30500045ef6b6b   \n", "4  SH01-L_159  653a61286f30500045ef6b6b   \n", "\n", "   KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce  \\\n", "0  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "1  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "2  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "3  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "4  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "\n", "   2992.2844077128157  3582.150410156599  83.2813419134031  \\\n", "0         3119.288454        3278.173512        110.347778   \n", "1         3221.308098        2886.751205        112.429812   \n", "2         3106.796253        2818.044098         81.199308   \n", "3          312.139451        3096.658157         77.035241   \n", "4          133.084565        3080.001889        -70.789141   \n", "\n", "   108.26574448742355  elephant  Unnamed: 9  hgeorge  1698389707201  false  \\\n", "0          127.004046  elephant         NaN  hgeorge  1698389707201  False   \n", "1          189.465053  elephant         NaN  hgeorge  1698389707201  False   \n", "2          106.183711  elephant         NaN  hgeorge  1698389707201  False   \n", "3           74.953208  elephant         NaN  hgeorge  1698389707201  False   \n", "4          116.593879  elephant         NaN  hgeorge  1698389707201  False   \n", "\n", "   Unnamed: 13  \n", "0          NaN  \n", "1          NaN  \n", "2          NaN  \n", "3          NaN  \n", "4          NaN  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["shdf = pd.read_csv(\"scoutexports/SHscout-export-annotations.csv\")\n", "shdf.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "b9290cb0", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1439, 14)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["shdf.shape"]}, {"cell_type": "code", "execution_count": 4, "id": "eb1176d6", "metadata": {}, "outputs": [], "source": ["shdf.columns=['TaskName', 'TaskID', 'ImageFilename', 'ImageID', 'BoxX', 'BoxY',\n", "       'BoxW', 'BoxH', 'Label', 'LabelConfidence', 'Assignee', 'Timestamp',\n", "       'IsGroundTruth', 'ExcludedByLine']"]}, {"cell_type": "code", "execution_count": 7, "id": "6ba3873f", "metadata": {}, "outputs": [{"data": {"text/plain": ["88"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["shdf['TaskName'].nunique()"]}, {"cell_type": "code", "execution_count": 9, "id": "85b10e6e", "metadata": {}, "outputs": [{"data": {"text/plain": ["elephant            1227\n", "sable                 34\n", "zebra                 31\n", "giraffe               25\n", "impala                21\n", "ec3                   19\n", "white_bones           18\n", "ec4                   15\n", "unknown mammal        11\n", "unknown antelope       6\n", "roan                   6\n", "gazelle_thomsons       5\n", "warthog                4\n", "kudu                   4\n", "unknown carcass        4\n", "human                  4\n", "bird                   2\n", "car                    1\n", "buffalo                1\n", "bushpig                1\n", "Name: Label, dtype: int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["shdf['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "77312620", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "80720af4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "95e81fe0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "edc5a608", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "61d5cd32", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "037ae925", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "51dc271c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}