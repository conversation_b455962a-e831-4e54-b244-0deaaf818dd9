{"cells": [{"cell_type": "markdown", "id": "ff9ed367", "metadata": {}, "source": ["Divide stratum to left and right"]}, {"cell_type": "code", "execution_count": 1, "id": "8daf5dd7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 4, "id": "b489dc6c-8845-4df1-9680-2fd060786fc4", "metadata": {}, "outputs": [], "source": ["coco_file_path = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023/utils/CVATdata/NG26stratum/annotationsNG26.json\""]}, {"cell_type": "code", "execution_count": 5, "id": "23b685d4", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "images_df = pd.DataFrame(coco_data['images'])\n"]}, {"cell_type": "code", "execution_count": 6, "id": "4a1f8fd3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(859, 6)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.shape"]}, {"cell_type": "code", "execution_count": 7, "id": "8e9230e2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051033_M0301597.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051007_M0301584.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051031_M0301596.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051354_M0301697.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   1   7008    4672  KES22_IIM-L_20220924A-051009_M0301585.jpg        1   \n", "1   2   7008    4672  KES22_IIM-L_20220924A-051033_M0301597.jpg        1   \n", "2   3   7008    4672  KES22_IIM-L_20220924A-051007_M0301584.jpg        1   \n", "3   4   7008    4672  KES22_IIM-L_20220924A-051031_M0301596.jpg        1   \n", "4   5   7008    4672  KES22_IIM-L_20220924A-051354_M0301697.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "60719a18", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 8, "id": "fa81bb81-35e1-41cc-b57f-e08360748e87", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 24, "id": "2b99b790-5318-4b36-a53a-16d3756b4037", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in ng26leftposve_dfsorted[\"file_name\"]:\n", "    if item in ng26leftimgsorted[\"file_name\"].values:\n", "        index_in_pos = ng26leftimgsorted[\"file_name\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(ng26leftimgsorted[\"file_name\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(ng26leftimgsorted[\"file_name\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 25, "id": "2be9136b-e16e-42d9-b00a-93ec347ddb84", "metadata": {}, "outputs": [{"data": {"text/plain": ["1293"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 26, "id": "d7696c45-bfd9-436c-afda-4239c5007d50", "metadata": {}, "outputs": [{"data": {"text/plain": ["887"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 27, "id": "008a9c33-b2e3-4b6f-875e-1a9b32e5f81b", "metadata": {}, "outputs": [], "source": ["cvatimg = pd.DataFrame(fin,columns=[\"cvatfilename\"])"]}, {"cell_type": "code", "execution_count": 28, "id": "8a77236e-0e42-482c-892a-0504f417334f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>115</th>\n", "      <td>KES22_IIM-L_20220924A-051005_M0301583.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>479</th>\n", "      <td>KES22_IIM-L_20220924A-051007_M0301584.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>118</th>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>799</th>\n", "      <td>KES22_IIM-L_20220924A-051011_M0301586.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>451</th>\n", "      <td>KES22_IIM-L_20220924A-051029_M0301595.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  c<PERSON><PERSON><PERSON><PERSON>\n", "115  KES22_IIM-L_20220924A-051005_M0301583.jpg\n", "479  KES22_IIM-L_20220924A-051007_M0301584.jpg\n", "118  KES22_IIM-L_20220924A-051009_M0301585.jpg\n", "799  KES22_IIM-L_20220924A-051011_M0301586.jpg\n", "451  KES22_IIM-L_20220924A-051029_M0301595.jpg"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin = cvatimg.sort_values(by=[\"cvatfilename\"],ascending=True)\n", "cvatfin.head()"]}, {"cell_type": "code", "execution_count": 29, "id": "ffdef2e9-a764-40b6-9132-94f29afa7e82", "metadata": {}, "outputs": [{"data": {"text/plain": ["(887, 1)"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin.shape"]}, {"cell_type": "code", "execution_count": 30, "id": "afc94256-61ff-436c-a8a0-976acc8ab884", "metadata": {}, "outputs": [], "source": ["cvatfin.to_csv(\"ng26leftcvat.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "c2d70828-12c5-45b1-a911-9dc64154f5dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "ac85a34b-1a7e-40be-b824-ed9170a5fdb7", "metadata": {}, "source": ["### NG26 Right"]}, {"cell_type": "code", "execution_count": 15, "id": "8ddae76b-c500-4d2d-958d-e20286f9ee1e", "metadata": {}, "outputs": [], "source": ["pattern2 = \"KES22_IIM-R_\""]}, {"cell_type": "code", "execution_count": 16, "id": "1e9aefc9", "metadata": {}, "outputs": [{"data": {"text/plain": ["(428, 6)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "ng26right = images_df[images_df['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "ng26right.shape"]}, {"cell_type": "code", "execution_count": 17, "id": "3ad7fa26", "metadata": {}, "outputs": [], "source": ["ng26rightsorted = ng26right.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 18, "id": "786e514b-1138-4c25-a57f-d0dd97b46ca2", "metadata": {}, "outputs": [], "source": ["ng26rightsorted.to_csv(\"ng26rightimgpos.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 19, "id": "5fd9fbb4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>349</th>\n", "      <td>350</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-050250_M0407922.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>792</th>\n", "      <td>793</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-050958_M0408135.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>797</th>\n", "      <td>798</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-051012_M0408142.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>798</th>\n", "      <td>799</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-051052_M0408162.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>803</th>\n", "      <td>804</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-051054_M0408163.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "349  350   7008    4672  KES22_IIM-R_20220924A-050250_M0407922.jpg        1   \n", "792  793   7008    4672  KES22_IIM-R_20220924A-050958_M0408135.jpg        1   \n", "797  798   7008    4672  KES22_IIM-R_20220924A-051012_M0408142.jpg        1   \n", "798  799   7008    4672  KES22_IIM-R_20220924A-051052_M0408162.jpg        1   \n", "803  804   7008    4672  KES22_IIM-R_20220924A-051054_M0408163.jpg        1   \n", "\n", "    date_captured  \n", "349    2022-09-23  \n", "792    2022-09-23  \n", "797    2022-09-23  \n", "798    2022-09-23  \n", "803    2022-09-23  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26rightsorted.head()"]}, {"cell_type": "code", "execution_count": 35, "id": "03450b69-5e5d-4a6b-b810-50649fb1a63b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>818</th>\n", "      <td>819</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-083824_M0404368.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>821</th>\n", "      <td>822</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-083826_M0404369.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>825</th>\n", "      <td>826</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-083828_M0404370.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>824</th>\n", "      <td>825</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-083842_M0404377.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>827</th>\n", "      <td>828</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-083858_M0404385.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "818  819   7008    4672  KES22_IIM-R_20220924A-083824_M0404368.jpg        1   \n", "821  822   7008    4672  KES22_IIM-R_20220924A-083826_M0404369.jpg        1   \n", "825  826   7008    4672  KES22_IIM-R_20220924A-083828_M0404370.jpg        1   \n", "824  825   7008    4672  KES22_IIM-R_20220924A-083842_M0404377.jpg        1   \n", "827  828   7008    4672  KES22_IIM-R_20220924A-083858_M0404385.jpg        1   \n", "\n", "    date_captured  \n", "818    2022-09-23  \n", "821    2022-09-23  \n", "825    2022-09-23  \n", "824    2022-09-23  \n", "827    2022-09-23  "]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26rightsorted.tail()"]}, {"cell_type": "code", "execution_count": 36, "id": "ca07d990-3f37-4f08-b294-485a4a12362d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(428, 6)"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26rightsorted.shape"]}, {"cell_type": "code", "execution_count": 37, "id": "b38c015b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(5048, 1)"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26imgsuniqueright = ng26imgsdf[ng26imgsdf['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "ng26imgsuniqueright.shape"]}, {"cell_type": "code", "execution_count": 38, "id": "b6a573b1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4089</th>\n", "      <td>KES22_IIM-R_20220924A-050238_M0407916.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4119</th>\n", "      <td>KES22_IIM-R_20220924A-050240_M0407917.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4100</th>\n", "      <td>KES22_IIM-R_20220924A-050242_M0407918.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4099</th>\n", "      <td>KES22_IIM-R_20220924A-050244_M0407919.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4094</th>\n", "      <td>KES22_IIM-R_20220924A-050246_M0407920.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      file_name\n", "4089  KES22_IIM-R_20220924A-050238_M0407916.jpg\n", "4119  KES22_IIM-R_20220924A-050240_M0407917.jpg\n", "4100  KES22_IIM-R_20220924A-050242_M0407918.jpg\n", "4099  KES22_IIM-R_20220924A-050244_M0407919.jpg\n", "4094  KES22_IIM-R_20220924A-050246_M0407920.jpg"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26rightimgsorted = ng26imgsuniqueright.sort_values(by=[\"file_name\"],ascending=True)\n", "ng26rightimgsorted.head()"]}, {"cell_type": "code", "execution_count": 39, "id": "b879b12b-d248-4bc7-baa7-f9119aee8f71", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in ng26rightsorted[\"file_name\"]:\n", "    if item in ng26rightimgsorted[\"file_name\"].values:\n", "        index_in_pos = ng26rightimgsorted[\"file_name\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(ng26rightimgsorted[\"file_name\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(ng26rightimgsorted[\"file_name\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 40, "id": "db6f4e5d-fbb6-46a9-9bee-8476dbe5cbbb", "metadata": {}, "outputs": [{"data": {"text/plain": ["1284"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 41, "id": "7badf276-6239-486d-b2ba-9cb2a2c3bcef", "metadata": {}, "outputs": [{"data": {"text/plain": ["888"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 42, "id": "82318eab-c30a-427e-96de-befaea605c12", "metadata": {}, "outputs": [], "source": ["cvatimg = pd.DataFrame(fin,columns=[\"cvatfilename\"])"]}, {"cell_type": "code", "execution_count": 43, "id": "efc73194-3fdb-436e-bcb9-09a25d0b2c1d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>494</th>\n", "      <td>KES22_IIM-R_20220924A-050248_M0407921.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170</th>\n", "      <td>KES22_IIM-R_20220924A-050250_M0407922.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>709</th>\n", "      <td>KES22_IIM-R_20220924A-050252_M0407923.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>539</th>\n", "      <td>KES22_IIM-R_20220924A-050956_M0408134.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>KES22_IIM-R_20220924A-050958_M0408135.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  c<PERSON><PERSON><PERSON><PERSON>\n", "494  KES22_IIM-R_20220924A-050248_M0407921.jpg\n", "170  KES22_IIM-R_20220924A-050250_M0407922.jpg\n", "709  KES22_IIM-R_20220924A-050252_M0407923.jpg\n", "539  KES22_IIM-R_20220924A-050956_M0408134.jpg\n", "39   KES22_IIM-R_20220924A-050958_M0408135.jpg"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin = cvatimg.sort_values(by=[\"cvatfilename\"],ascending=True)\n", "cvatfin.head()"]}, {"cell_type": "code", "execution_count": 44, "id": "67455f80-c824-4529-b515-437b119701c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(888, 1)"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin.shape"]}, {"cell_type": "code", "execution_count": 45, "id": "dc41c311-5c01-4556-8eca-b38590211628", "metadata": {}, "outputs": [], "source": ["cvatfin.to_csv(\"NG26rightcvat.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "3509c90d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "204ee061", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6ea86789", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2f90db59", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a9973fe2-38f9-4102-8131-4cc4d2db4c4e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b16ac600-595c-47fe-ba9a-b7c7d29f7111", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5259e8b4-d426-487d-91fd-92baeb3e1556", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "cc806d4c-c697-48e4-89e3-129958131725", "metadata": {}, "source": ["### Annotations"]}, {"cell_type": "code", "execution_count": 9, "id": "98589797", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "annotationsdf = pd.DataFrame(coco_data['annotations'])\n"]}, {"cell_type": "code", "execution_count": 10, "id": "09b91e6f", "metadata": {}, "outputs": [], "source": ["homedir = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023/utils/CVATdata/NG26stratum\""]}, {"cell_type": "code", "execution_count": 21, "id": "9ab9d2b2-f241-4195-a1b0-99e7e1ff6688", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4168.738691</td>\n", "      <td>[1347.29522687095, 653.85975250442, -55.061873...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4831.947119</td>\n", "      <td>[1787.79021803182, 333.812610489098, -82.59281...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4837.868623</td>\n", "      <td>[1720.68355922216, 397.477902180318, -73.98939...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4529.950424</td>\n", "      <td>[1662.1803182086, 376.829699469652, -77.430760...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4820.104111</td>\n", "      <td>[1543.45315262227, 347.578078962876, 75.710076...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation         area  \\\n", "0   1         1            3           []  4168.738691   \n", "1   2         1            3           []  4831.947119   \n", "2   3         1            3           []  4837.868623   \n", "3   4         1            3           []  4529.950424   \n", "4   5         1            3           []  4820.104111   \n", "\n", "                                                bbox  iscrowd  \n", "0  [1347.29522687095, 653.85975250442, -55.061873...        0  \n", "1  [1787.79021803182, 333.812610489098, -82.59281...        0  \n", "2  [1720.68355922216, 397.477902180318, -73.98939...        0  \n", "3  [1662.1803182086, 376.829699469652, -77.430760...        0  \n", "4  [1543.45315262227, 347.578078962876, 75.710076...        0  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 22, "id": "1782ab7f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4993, 7)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["#change bbox dimensions to positive\n", "greater_than_zero_mask = [all(item > 0 for item in row) for row in annotationsdf['bbox']]\n", "annotationsdf[greater_than_zero_mask].shape"]}, {"cell_type": "code", "execution_count": 23, "id": "bff4c1e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2663, 7)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["filterpositive = annotationsdf[greater_than_zero_mask]['bbox'].tolist()\n", "less_than_zero_mask = annotationsdf[~annotationsdf['bbox'].isin(filterpositive)]\n", "less_than_zero_mask.shape"]}, {"cell_type": "code", "execution_count": 25, "id": "924bf19a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4168.738691</td>\n", "      <td>[1347.29522687095, 653.85975250442, -55.061873...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4831.947119</td>\n", "      <td>[1787.79021803182, 333.812610489098, -82.59281...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4837.868623</td>\n", "      <td>[1720.68355922216, 397.477902180318, -73.98939...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4529.950424</td>\n", "      <td>[1662.1803182086, 376.829699469652, -77.430760...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td>2</td>\n", "      <td>12</td>\n", "      <td>[]</td>\n", "      <td>1136.623139</td>\n", "      <td>[1179.18175757867, 114.787604720048, -33.71384...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation         area  \\\n", "0   1         1            3           []  4168.738691   \n", "1   2         1            3           []  4831.947119   \n", "2   3         1            3           []  4837.868623   \n", "3   4         1            3           []  4529.950424   \n", "5   6         2           12           []  1136.623139   \n", "\n", "                                                bbox  iscrowd  \n", "0  [1347.29522687095, 653.85975250442, -55.061873...        0  \n", "1  [1787.79021803182, 333.812610489098, -82.59281...        0  \n", "2  [1720.68355922216, 397.477902180318, -73.98939...        0  \n", "3  [1662.1803182086, 376.829699469652, -77.430760...        0  \n", "5  [1179.18175757867, 114.787604720048, -33.71384...        0  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["less_than_zero_mask.head()"]}, {"cell_type": "code", "execution_count": 26, "id": "c0e5f2b4", "metadata": {}, "outputs": [], "source": ["for item in less_than_zero_mask['bbox']:\n", "    \n", "    if((item[-2]<0) and (item[-1]<0)):\n", "        item[-2] = abs(item[-2])\n", "        item[0]  = item[0] + int(abs(item[-2]))\n", "        item[-1] = abs(item[-1])\n", "        item[1] = item[1] + int(abs(item[-1]))\n", "    \n", "    elif((item[0]<0) and (item[1]<0)):\n", "        item[0] = abs(item[0])\n", "        item[-2] = item[-2] + int(abs(item[0]))\n", "        item[1] = abs(item[1])\n", "        item[-1] = item[-1] + int(abs(item[1]))\n", "        \n", "    elif(item[-2]<0):\n", "        item[-2] = abs(item[-2])\n", "        item[0]  = item[0] + int(abs(item[-2]))\n", "    \n", "    elif(item[0]<0):\n", "        item[0] = abs(item[0])\n", "        item[-2] = item[-2] + int(abs(item[0]))\n", "           \n", "    elif(item[1]<0):\n", "        item[1] = abs(item[1])\n", "        item[-1] = item[-1] + int(abs(item[1]))\n", "            \n", "    elif(item[-1]<0):\n", "        item[-1] = abs(item[-1])\n", "        item[1] = item[1] + int(abs(item[-1]))\n", "    "]}, {"cell_type": "code", "execution_count": 27, "id": "c41629b4", "metadata": {}, "outputs": [], "source": ["annotationsdf.update(less_than_zero_mask)"]}, {"cell_type": "code", "execution_count": 28, "id": "a06717ce", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7609, 7)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["#check if all are positives after\n", "greater_than_zero_mask = [all(item > 0 for item in row) for row in annotationsdf['bbox']]\n", "annotationsdf[greater_than_zero_mask].shape"]}, {"cell_type": "code", "execution_count": 29, "id": "db6cd851", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7656, 7)"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.shape"]}, {"cell_type": "code", "execution_count": 30, "id": "e8df4453", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4168.738691</td>\n", "      <td>[1402.29522687095, 653.85975250442, 55.0618738...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4831.947119</td>\n", "      <td>[1869.79021803182, 333.812610489098, 82.592810...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4837.868623</td>\n", "      <td>[1793.68355922216, 397.477902180318, 73.989393...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4529.950424</td>\n", "      <td>[1739.1803182086, 376.829699469652, 77.4307601...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4820.104111</td>\n", "      <td>[1543.45315262227, 347.578078962876, 75.710076...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  image_id  category_id segmentation         area  \\\n", "0  1.0       1.0          3.0           []  4168.738691   \n", "1  2.0       1.0          3.0           []  4831.947119   \n", "2  3.0       1.0          3.0           []  4837.868623   \n", "3  4.0       1.0          3.0           []  4529.950424   \n", "4  5.0       1.0          3.0           []  4820.104111   \n", "\n", "                                                bbox  iscrowd  \n", "0  [1402.29522687095, 653.85975250442, 55.0618738...      0.0  \n", "1  [1869.79021803182, 333.812610489098, 82.592810...      0.0  \n", "2  [1793.68355922216, 397.477902180318, 73.989393...      0.0  \n", "3  [1739.1803182086, 376.829699469652, 77.4307601...      0.0  \n", "4  [1543.45315262227, 347.578078962876, 75.710076...      0.0  "]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 31, "id": "34264da2-93da-46b9-b270-56dc4877fa71", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"id\"]=annotationsdf[\"id\"].astype(int)"]}, {"cell_type": "code", "execution_count": 32, "id": "31f48636-01f8-4d00-a210-f8b4dcc796c1", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"image_id\"] =annotationsdf[\"image_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": 45, "id": "8154d81a-23b8-4317-b614-2b76638d3ba2", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"iscrowd\"] = annotationsdf[\"iscrowd\"].astype(int)"]}, {"cell_type": "code", "execution_count": 46, "id": "68ac5921", "metadata": {}, "outputs": [], "source": ["annotationsdf.to_csv(\"ng26rightpositivebbx.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 48, "id": "1252e21c-ac1a-4015-bf34-27f227fb5272", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7651</th>\n", "      <td>7652</td>\n", "      <td>853</td>\n", "      <td>27</td>\n", "      <td>[]</td>\n", "      <td>2805.083143</td>\n", "      <td>[2571.05678233438, 589.600677971584, 54.889589...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7652</th>\n", "      <td>7653</td>\n", "      <td>853</td>\n", "      <td>27</td>\n", "      <td>[]</td>\n", "      <td>2307.118192</td>\n", "      <td>[2970.42586750789, 833.764715826474, 52.996845...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7653</th>\n", "      <td>7654</td>\n", "      <td>853</td>\n", "      <td>27</td>\n", "      <td>[]</td>\n", "      <td>3672.043706</td>\n", "      <td>[3687.17350157729, 1015.81230283912, 77.602523...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7654</th>\n", "      <td>7655</td>\n", "      <td>494</td>\n", "      <td>27</td>\n", "      <td>[]</td>\n", "      <td>2361.854944</td>\n", "      <td>[6891.94135564505, 1230.43515224484, 52.886042...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7655</th>\n", "      <td>7656</td>\n", "      <td>494</td>\n", "      <td>27</td>\n", "      <td>[]</td>\n", "      <td>5589.265448</td>\n", "      <td>[6803.26535927601, 1166.0218788365, 93.4061402...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        id  image_id  category_id segmentation         area  \\\n", "7651  7652       853           27           []  2805.083143   \n", "7652  7653       853           27           []  2307.118192   \n", "7653  7654       853           27           []  3672.043706   \n", "7654  7655       494           27           []  2361.854944   \n", "7655  7656       494           27           []  5589.265448   \n", "\n", "                                                   bbox  iscrowd  \n", "7651  [2571.05678233438, 589.600677971584, 54.889589...        0  \n", "7652  [2970.42586750789, 833.764715826474, 52.996845...        0  \n", "7653  [3687.17350157729, 1015.81230283912, 77.602523...        0  \n", "7654  [6891.94135564505, 1230.43515224484, 52.886042...        0  \n", "7655  [6803.26535927601, 1166.0218788365, 93.4061402...        0  "]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.tail()"]}, {"cell_type": "code", "execution_count": 17, "id": "e4193fb0-f31a-4583-aee1-5466a6d9ff32", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>350</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-050250_M0407922.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>793</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-050958_M0408135.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>798</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-051012_M0408142.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>799</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-051052_M0408162.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>804</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-051054_M0408163.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0  350   7008    4672  KES22_IIM-R_20220924A-050250_M0407922.jpg        1   \n", "1  793   7008    4672  KES22_IIM-R_20220924A-050958_M0408135.jpg        1   \n", "2  798   7008    4672  KES22_IIM-R_20220924A-051012_M0408142.jpg        1   \n", "3  799   7008    4672  KES22_IIM-R_20220924A-051052_M0408162.jpg        1   \n", "4  804   7008    4672  KES22_IIM-R_20220924A-051054_M0408163.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26rightposimg = pd.read_csv(homedir+\"/ng26rightimgpos.csv\")\n", "ng26rightposimg.head()"]}, {"cell_type": "code", "execution_count": 15, "id": "67ab79c1", "metadata": {}, "outputs": [{"data": {"text/plain": ["(428, 6)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26rightposimg.shape"]}, {"cell_type": "code", "execution_count": 16, "id": "3f83efee", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-R_20220924A-050248_M0407921.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-R_20220924A-050250_M0407922.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-R_20220924A-050252_M0407923.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-R_20220924A-050956_M0408134.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-R_20220924A-050958_M0408135.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                c<PERSON><PERSON><PERSON><PERSON>\n", "0  KES22_IIM-R_20220924A-050248_M0407921.jpg\n", "1  KES22_IIM-R_20220924A-050250_M0407922.jpg\n", "2  KES22_IIM-R_20220924A-050252_M0407923.jpg\n", "3  KES22_IIM-R_20220924A-050956_M0408134.jpg\n", "4  KES22_IIM-R_20220924A-050958_M0408135.jpg"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26right1_img = pd.read_csv(homedir+\"/NG26right1cvat.csv\")\n", "ng26right1_img.head()"]}, {"cell_type": "code", "execution_count": 19, "id": "b4fe278b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(135, 6)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return ng26left1 positive images only \n", "ng26right1_posimg = ng26rightposimg[ng26rightposimg['file_name'].isin(ng26right1_img['cvatfilename'])]\n", "ng26right1_posimg.shape"]}, {"cell_type": "code", "execution_count": 20, "id": "8824033c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>350</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-050250_M0407922.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>793</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-050958_M0408135.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>798</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-051012_M0408142.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>799</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-051052_M0408162.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>804</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-051054_M0408163.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0  350   7008    4672  KES22_IIM-R_20220924A-050250_M0407922.jpg        1   \n", "1  793   7008    4672  KES22_IIM-R_20220924A-050958_M0408135.jpg        1   \n", "2  798   7008    4672  KES22_IIM-R_20220924A-051012_M0408142.jpg        1   \n", "3  799   7008    4672  KES22_IIM-R_20220924A-051052_M0408162.jpg        1   \n", "4  804   7008    4672  KES22_IIM-R_20220924A-051054_M0408163.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26right1_posimg.head()"]}, {"cell_type": "code", "execution_count": 49, "id": "24eb1c57", "metadata": {}, "outputs": [{"data": {"text/plain": ["(969, 7)"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return annotations \n", "\n", "ng26right1_annot = annotationsdf[annotationsdf['image_id'].isin(ng26right1_posimg['id'])]\n", "ng26right1_annot.shape"]}, {"cell_type": "code", "execution_count": 50, "id": "ada5501d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3146</th>\n", "      <td>3147</td>\n", "      <td>350</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>7241.748458</td>\n", "      <td>[1456.25889721932, 3181.32295719844, 113.61867...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3147</th>\n", "      <td>3148</td>\n", "      <td>350</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>7226.989757</td>\n", "      <td>[1456.25889721932, 3185.05802591394, 120.44533...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3148</th>\n", "      <td>3149</td>\n", "      <td>351</td>\n", "      <td>12</td>\n", "      <td>[]</td>\n", "      <td>480.547209</td>\n", "      <td>[2265.05955593993, 488.839459614665, 25.600384...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3149</th>\n", "      <td>3150</td>\n", "      <td>351</td>\n", "      <td>12</td>\n", "      <td>[]</td>\n", "      <td>954.614607</td>\n", "      <td>[1880.3015852475, 530.200357596331, 32.0631642...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3150</th>\n", "      <td>3151</td>\n", "      <td>351</td>\n", "      <td>12</td>\n", "      <td>[]</td>\n", "      <td>419.610816</td>\n", "      <td>[2061.22944051358, 459.203351099514, 22.902260...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        id  image_id  category_id segmentation         area  \\\n", "3146  3147       350           14           []  7241.748458   \n", "3147  3148       350           14           []  7226.989757   \n", "3148  3149       351           12           []   480.547209   \n", "3149  3150       351           12           []   954.614607   \n", "3150  3151       351           12           []   419.610816   \n", "\n", "                                                   bbox  iscrowd  \n", "3146  [1456.25889721932, 3181.32295719844, 113.61867...        0  \n", "3147  [1456.25889721932, 3185.05802591394, 120.44533...        0  \n", "3148  [2265.05955593993, 488.839459614665, 25.600384...        0  \n", "3149  [1880.3015852475, 530.200357596331, 32.0631642...        0  \n", "3150  [2061.22944051358, 459.203351099514, 22.902260...        0  "]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26right1_annot.head()"]}, {"cell_type": "code", "execution_count": 51, "id": "f728a3fe", "metadata": {}, "outputs": [], "source": ["ng26right1_annot.to_csv(\"NG26right1annotations.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "431bd5e3-19b8-4473-b5f2-07460645bd12", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "368d46a6-a4bf-443e-b5ee-4e21fd1d5a89", "metadata": {}, "source": ["### Annotations to COCO file"]}, {"cell_type": "code", "execution_count": null, "id": "cc08233f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 52, "id": "bc5b969d-9351-4fad-83cb-27aec08851b4", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "coco_dict['info']={\"description\": \"NG26 Stratum annotations\", \"version\": \"1.0\", \"year\": 2023, \"contributor\": \"MWS Lab Arusha\", \"date_created\": \"2023-09-24\"}\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = ng26right1_posimg.to_dict(orient='records')\n", "coco_dict['annotations'] = ng26right1_annot.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'ng26right1annotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": 32, "id": "25d27e8d-9b16-49d6-80ad-d0457b4526a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'id': 1,\n", "  'name': 'KAZA Elephant Survey',\n", "  'url': 'https://www.kavangozambezi.org/kaza-elephant-survey/'}]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["coco_data[\"licenses\"]"]}, {"cell_type": "code", "execution_count": 39, "id": "4b3bf7a4", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'description': 'NG26 Stratum annotations',\n", " 'version': '1.0',\n", " 'year': 2023,\n", " 'contributor': 'MWS Lab Arusha',\n", " 'date_created': '2023-09-24'}"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["coco_dict[\"info\"]"]}, {"cell_type": "code", "execution_count": null, "id": "b8c88241", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "92f7585c-221f-4316-9985-c02fbde28597", "metadata": {}, "source": ["### NG26 Right 2"]}, {"cell_type": "code", "execution_count": 53, "id": "0a3d49f2-508f-46ee-a7ff-505cc5ff688d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-R_20220924A-063132_M0400575.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-R_20220924A-063206_M0400592.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-R_20220924A-063208_M0400593.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-R_20220924A-063210_M0400594.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-R_20220924A-063231_M0400604.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                c<PERSON><PERSON><PERSON><PERSON>\n", "0  KES22_IIM-R_20220924A-063132_M0400575.jpg\n", "1  KES22_IIM-R_20220924A-063206_M0400592.jpg\n", "2  KES22_IIM-R_20220924A-063208_M0400593.jpg\n", "3  KES22_IIM-R_20220924A-063210_M0400594.jpg\n", "4  KES22_IIM-R_20220924A-063231_M0400604.jpg"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26right2_img = pd.read_csv(homedir+\"/NG26right2cvat.csv\")\n", "ng26right2_img.head()"]}, {"cell_type": "code", "execution_count": 57, "id": "742bc26e-08a4-46c5-b15e-38ec8ed61128", "metadata": {}, "outputs": [{"data": {"text/plain": ["(138, 6)"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return ng26left1 positive images only \n", "ng26right2_posimg = ng26rightposimg[ng26rightposimg['file_name'].isin(ng26right2_img['cvatfilename'])]\n", "ng26right2_posimg.shape"]}, {"cell_type": "code", "execution_count": 58, "id": "94825725-1b38-4755-b448-5965799ac174", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>135</th>\n", "      <td>407</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-063208_M0400593.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>136</th>\n", "      <td>404</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-063233_M0400605.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>409</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-063235_M0400606.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>138</th>\n", "      <td>403</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-063341_M0400639.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>139</th>\n", "      <td>405</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-063343_M0400640.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "135  407   7008    4672  KES22_IIM-R_20220924A-063208_M0400593.jpg        1   \n", "136  404   7008    4672  KES22_IIM-R_20220924A-063233_M0400605.jpg        1   \n", "137  409   7008    4672  KES22_IIM-R_20220924A-063235_M0400606.jpg        1   \n", "138  403   7008    4672  KES22_IIM-R_20220924A-063341_M0400639.jpg        1   \n", "139  405   7008    4672  KES22_IIM-R_20220924A-063343_M0400640.jpg        1   \n", "\n", "    date_captured  \n", "135    2022-09-23  \n", "136    2022-09-23  \n", "137    2022-09-23  \n", "138    2022-09-23  \n", "139    2022-09-23  "]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26right2_posimg.head()"]}, {"cell_type": "code", "execution_count": 59, "id": "05ca9add-7a58-4d46-83dd-5fc78e054473", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1045, 7)"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return annotations \n", "\n", "ng26right2_annot = annotationsdf[annotationsdf['image_id'].isin(ng26right2_posimg['id'])]\n", "ng26right2_annot.shape"]}, {"cell_type": "code", "execution_count": 60, "id": "4ded3a61-0970-49b0-91cb-e63824d67fe1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3563</th>\n", "      <td>3564</td>\n", "      <td>402</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>23210.532403</td>\n", "      <td>[5503.9422362984, 794.887197114121, 160.304008...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3564</th>\n", "      <td>3565</td>\n", "      <td>402</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>17715.412116</td>\n", "      <td>[7067.57734013264, 936.739780438903, 137.03407...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3565</th>\n", "      <td>3566</td>\n", "      <td>403</td>\n", "      <td>7</td>\n", "      <td>[]</td>\n", "      <td>6488.545922</td>\n", "      <td>[258.616643872803, 589.992234050596, 76.257499...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3566</th>\n", "      <td>3567</td>\n", "      <td>403</td>\n", "      <td>7</td>\n", "      <td>[]</td>\n", "      <td>9504.721043</td>\n", "      <td>[67.8290391527555, 640.562996969218, 79.468341...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3567</th>\n", "      <td>3568</td>\n", "      <td>403</td>\n", "      <td>7</td>\n", "      <td>[]</td>\n", "      <td>1894.371898</td>\n", "      <td>[1.20406578377673, 823.58099610328, 28.0948682...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        id  image_id  category_id segmentation          area  \\\n", "3563  3564       402            9           []  23210.532403   \n", "3564  3565       402            9           []  17715.412116   \n", "3565  3566       403            7           []   6488.545922   \n", "3566  3567       403            7           []   9504.721043   \n", "3567  3568       403            7           []   1894.371898   \n", "\n", "                                                   bbox  iscrowd  \n", "3563  [5503.9422362984, 794.887197114121, 160.304008...        0  \n", "3564  [7067.57734013264, 936.739780438903, 137.03407...        0  \n", "3565  [258.616643872803, 589.992234050596, 76.257499...        0  \n", "3566  [67.8290391527555, 640.562996969218, 79.468341...        0  \n", "3567  [1.20406578377673, 823.58099610328, 28.0948682...        0  "]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26right2_annot.head()"]}, {"cell_type": "code", "execution_count": 61, "id": "348d31d2-d57b-4afd-b782-411200daf9af", "metadata": {}, "outputs": [], "source": ["ng26right2_annot.to_csv(\"NG26right2annotations.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "87d56261-f16c-4a3f-b064-51fcc1f232a1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 62, "id": "bb80ffec", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "coco_dict['info']={\"description\": \"NG26 Stratum annotations\", \"version\": \"1.0\", \"year\": 2023, \"contributor\": \"MWS Lab Arusha\", \"date_created\": \"2023-09-24\"}\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = ng26right2_posimg.to_dict(orient='records')\n", "coco_dict['annotations'] = ng26right2_annot.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'ng26right2annotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "id": "f1a1188f-400e-4b67-af45-f7ba4bf01f56", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5dbbc19b-d853-44a8-baca-a99e1aa0aa6f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "a9235484-e866-461a-9d2e-86c4b3825c49", "metadata": {}, "source": ["### NG26 Right 3"]}, {"cell_type": "code", "execution_count": 63, "id": "88d75755-bde9-485e-ba37-4eee7efe4a8c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-R_20220924A-075353_M0403037.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-R_20220924A-075626_M0403113.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-R_20220924A-075628_M0403114.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-R_20220924A-075630_M0403115.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-R_20220924A-075636_M0403118.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                c<PERSON><PERSON><PERSON><PERSON>\n", "0  KES22_IIM-R_20220924A-075353_M0403037.jpg\n", "1  KES22_IIM-R_20220924A-075626_M0403113.jpg\n", "2  KES22_IIM-R_20220924A-075628_M0403114.jpg\n", "3  KES22_IIM-R_20220924A-075630_M0403115.jpg\n", "4  KES22_IIM-R_20220924A-075636_M0403118.jpg"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26right3_img = pd.read_csv(homedir+\"/NG26right3cvat.csv\")\n", "ng26right3_img.head()"]}, {"cell_type": "code", "execution_count": 64, "id": "4b46a515-1169-45ee-b0f6-a375af5d46cf", "metadata": {}, "outputs": [{"data": {"text/plain": ["(155, 6)"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return ng26left1 positive images only \n", "ng26right3_posimg = ng26rightposimg[ng26rightposimg['file_name'].isin(ng26right3_img['cvatfilename'])]\n", "ng26right3_posimg.shape"]}, {"cell_type": "code", "execution_count": 65, "id": "7c81bd0e-79da-41f4-b0c0-d4f8d3507c14", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>273</th>\n", "      <td>669</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-075628_M0403114.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>274</th>\n", "      <td>678</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-075638_M0403119.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>275</th>\n", "      <td>667</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-075642_M0403121.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>276</th>\n", "      <td>680</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-075644_M0403122.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>277</th>\n", "      <td>679</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-075646_M0403123.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "273  669   7008    4672  KES22_IIM-R_20220924A-075628_M0403114.jpg        1   \n", "274  678   7008    4672  KES22_IIM-R_20220924A-075638_M0403119.jpg        1   \n", "275  667   7008    4672  KES22_IIM-R_20220924A-075642_M0403121.jpg        1   \n", "276  680   7008    4672  KES22_IIM-R_20220924A-075644_M0403122.jpg        1   \n", "277  679   7008    4672  KES22_IIM-R_20220924A-075646_M0403123.jpg        1   \n", "\n", "    date_captured  \n", "273    2022-09-23  \n", "274    2022-09-23  \n", "275    2022-09-23  \n", "276    2022-09-23  \n", "277    2022-09-23  "]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26right3_posimg.head()"]}, {"cell_type": "code", "execution_count": 66, "id": "f0e89692-9ce8-489e-8772-efb47fad080f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1499, 7)"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return annotations \n", "\n", "ng26right3_annot = annotationsdf[annotationsdf['image_id'].isin(ng26right3_posimg['id'])]\n", "ng26right3_annot.shape"]}, {"cell_type": "code", "execution_count": 69, "id": "7580a4d3-1527-4b3a-90aa-d0e9b52c0094", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5667</th>\n", "      <td>5668</td>\n", "      <td>662</td>\n", "      <td>22</td>\n", "      <td>[]</td>\n", "      <td>509.600333</td>\n", "      <td>[2300.30981862939, 885.750696381148, 17.064588...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5668</th>\n", "      <td>5669</td>\n", "      <td>662</td>\n", "      <td>22</td>\n", "      <td>[]</td>\n", "      <td>687.556005</td>\n", "      <td>[2345.70514339959, 961.119296855976, 24.174834...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5669</th>\n", "      <td>5670</td>\n", "      <td>663</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>4639.313756</td>\n", "      <td>[921.443960869726, 1761.38149706723, 66.246056...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5670</th>\n", "      <td>5671</td>\n", "      <td>664</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>18532.135930</td>\n", "      <td>[1336.8344288865, 3213.31801195525, 183.905269...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5671</th>\n", "      <td>5672</td>\n", "      <td>664</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>7819.038173</td>\n", "      <td>[1301.7939741483, 2115.38287935524, 141.078014...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        id  image_id  category_id segmentation          area  \\\n", "5667  5668       662           22           []    509.600333   \n", "5668  5669       662           22           []    687.556005   \n", "5669  5670       663           16           []   4639.313756   \n", "5670  5671       664            3           []  18532.135930   \n", "5671  5672       664            3           []   7819.038173   \n", "\n", "                                                   bbox  iscrowd  \n", "5667  [2300.30981862939, 885.750696381148, 17.064588...        0  \n", "5668  [2345.70514339959, 961.119296855976, 24.174834...        0  \n", "5669  [921.443960869726, 1761.38149706723, 66.246056...        0  \n", "5670  [1336.8344288865, 3213.31801195525, 183.905269...        0  \n", "5671  [1301.7939741483, 2115.38287935524, 141.078014...        0  "]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26right3_annot.head()"]}, {"cell_type": "code", "execution_count": 70, "id": "2c79e39b", "metadata": {}, "outputs": [], "source": ["ng26right3_annot.to_csv(\"NG26right3annotations.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "24043f13-d08e-416f-b568-2450778b428c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 71, "id": "d50fd600", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "coco_dict['info']={\"description\": \"NG26 Stratum annotations\", \"version\": \"1.0\", \"year\": 2023, \"contributor\": \"MWS Lab Arusha\", \"date_created\": \"2023-09-24\"}\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = ng26right3_posimg.to_dict(orient='records')\n", "coco_dict['annotations'] = ng26right3_annot.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'ng26right3annotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "id": "254708b5-3db5-489a-bc35-918c449acee8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16dd420b-4f4d-42de-9aa8-1917a1d7a760", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "043cfaf2-580a-4a72-9871-0e8bbad31a7f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}