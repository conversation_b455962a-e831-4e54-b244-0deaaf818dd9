{"cells": [{"cell_type": "code", "execution_count": null, "id": "eb1de6a8", "metadata": {}, "outputs": [], "source": ["def annodetails(filename):\n", "    ## the input is a csv file of exported annotations from Scout\n", "    ## output is a dataframe with details of the annotation Taskid, annotator name, bounding box count and no of \n", "    ## animals by type\n", "    filedf = pd.read_csv(filename)\n", "    \n", "    if filedf.empty:\n", "        print(\"No annotations on this task\")\n", "    else:\n", "        #check the number of unique tasks on file\n", "        filedf[\"Task Name\"].nunique()\n", "        #list of the unique tasks IDs\n", "        filedf[\"Task Name\"].unique()\n", "        \n", "        tskname = filedf.iloc[0,0]\n", "        bbno = filedf[\"Label\"].count()\n", "        annotator = filedf[\"Assignee\"].unique()\n", "        animalcount = filedf[\"Label\"].value_counts()\n", "        animaldf = animalcount.to_frame().T  #arrange the animals count into a dataframe\n", "        animaldf_reset = animaldf.reset_index(drop=True)  #reset index to remove word Label\n", "        annodf = pd.DataFrame({'TaskID':tskname, 'Annotator':annotator,'BBcount':bbno})\n", "        combinedf = pd.concat([annodf, animaldf_reset], axis=1)\n", "        return combinedf"]}, {"cell_type": "code", "execution_count": null, "id": "9efddf71", "metadata": {}, "outputs": [], "source": ["def append(df1,df2):\n", "    ## the two dataframes of annotation and animal details will be appended\n", "    # df1 is the existing dataframe and df2 the new dataFrame to be appended to df1\n", "    # Check if df2 row has columns not present in the df1 DataFrame\n", "       \n", "    missing_columns = [col for col in df2.keys() if col not in df1.columns]\n", "\n", "    # Add missing columns to the target DataFrame\n", "    for col in missing_columns:\n", "        df1[col] = None\n", "\n", "    # Append the neW row to the existing DataFrame\n", "    df1 = pd.concat([df1,df2],axis=0)\n", "    return df1.T\n"]}, {"cell_type": "code", "execution_count": null, "id": "93dfdcf6", "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "d1fda385", "metadata": {}, "outputs": [], "source": ["# Specify the directory path of the csv annotation files \n", "directory_path = \"/path/to/your/directory\"\n", "\n", "# Use the os.listdir() function to get a list of files in the directory\n", "file_list = os.listdir(directory_path)\n", "\n", "# Loop through the list and get the no of files on folder\n", "for filenm in file_list:\n", "    annotationdf = annodetails(filenm)\n", "    finaldf = append(annotationdf)\n"]}, {"cell_type": "code", "execution_count": null, "id": "d72f5bfb", "metadata": {}, "outputs": [], "source": ["finaldf.to_csv(\"annotationsummary.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "6647f652", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}