import json
import numpy as np
import argparse
import zipfile
import os
import sys
from collections import defaultdict

try:
    from tqdm import tqdm
    HAS_TQDM = True
except ImportError:
    HAS_TQDM = False
    # Fallback progress indicator
    class tqdm:
        def __init__(self, iterable=None, total=None, desc=None, **kwargs):
            self.iterable = iterable
            self.total = total or (len(iterable) if iterable else 0)
            self.desc = desc
            self.count = 0
            if desc:
                print(f"{desc}...")
        
        def __iter__(self):
            for item in self.iterable:
                yield item
                self.update()
            return self
        
        def __enter__(self):
            return self
        
        def __exit__(self, *args):
            if self.desc:
                print(f"{self.desc} completed!")
        
        def update(self, n=1):
            self.count += n
            if self.total > 0 and self.count % max(1, self.total // 20) == 0:
                percent = (self.count / self.total) * 100
                print(f"  Progress: {self.count}/{self.total} ({percent:.1f}%)")
        
        def set_description(self, desc):
            self.desc = desc

def calculate_iou(box1, box2):
    """Calculate Intersection over Union (IoU) of two bounding boxes.
    
    Args:
        box1, box2: [x, y, width, height] in COCO format
    
    Returns:
        IoU value between 0 and 1
    """
    x1, y1, w1, h1 = box1
    x2, y2, w2, h2 = box2
    
    # Calculate intersection coordinates
    x_left = max(x1, x2)
    y_top = max(y1, y2)
    x_right = min(x1 + w1, x2 + w2)
    y_bottom = min(y1 + h1, y2 + h2)
    
    # No intersection
    if x_right < x_left or y_bottom < y_top:
        return 0.0
    
    # Calculate areas
    intersection = (x_right - x_left) * (y_bottom - y_top)
    area1 = w1 * h1
    area2 = w2 * h2
    union = area1 + area2 - intersection
    
    return intersection / union if union > 0 else 0.0

def deduplicate_annotations(coco_data, iou_threshold=0.7):
    """Remove duplicate annotations based on IoU threshold.
    
    Args:
        coco_data: COCO format dictionary
        iou_threshold: IoU threshold for considering boxes as duplicates
    
    Returns:
        New COCO data with deduplicated annotations
    """
    # Group annotations by image_id and category_id
    grouped_annotations = defaultdict(list)
    for ann in coco_data['annotations']:
        key = (ann['image_id'], ann['category_id'])
        grouped_annotations[key].append(ann)
    
    # Count total comparisons for progress tracking
    total_comparisons = 0
    groups_to_process = []
    for key, annotations in grouped_annotations.items():
        if len(annotations) > 1:
            # n*(n-1)/2 pairwise comparisons
            comparisons = len(annotations) * (len(annotations) - 1) // 2
            total_comparisons += comparisons
            groups_to_process.append((key, annotations, comparisons))
        else:
            groups_to_process.append((key, annotations, 0))
    
    print(f"Processing {len(grouped_annotations)} groups with {total_comparisons:,} pairwise comparisons")
    
    deduplicated_annotations = []
    next_annotation_id = 1
    
    # Progress bar for groups
    with tqdm(total=len(groups_to_process), desc="Processing annotation groups") as pbar:
        for (image_id, category_id), annotations, expected_comparisons in groups_to_process:
            if len(annotations) == 1:
                # No duplicates possible
                ann = annotations[0]
                ann['id'] = next_annotation_id
                deduplicated_annotations.append(ann)
                next_annotation_id += 1
                pbar.update(1)
                continue
            
            # Find duplicate groups using IoU with progress tracking
            processed = set()
            comparisons_done = 0
            
            # Update progress bar description for large groups
            if expected_comparisons > 1000:
                pbar.set_description(f"Processing group (img:{image_id}, cat:{category_id}) - {len(annotations)} boxes")
            
            for i, ann1 in enumerate(annotations):
                if i in processed:
                    continue
                    
                # Start a new group with this annotation
                duplicate_group = [ann1]
                processed.add(i)
                
                # Find all annotations that overlap with this one
                for j, ann2 in enumerate(annotations):
                    if j in processed or j <= i:
                        continue
                        
                    iou = calculate_iou(ann1['bbox'], ann2['bbox'])
                    comparisons_done += 1
                    
                    if iou >= iou_threshold:
                        duplicate_group.append(ann2)
                        processed.add(j)
                
                # Merge the duplicate group
                merged_annotation = merge_annotations(duplicate_group, next_annotation_id)
                deduplicated_annotations.append(merged_annotation)
                next_annotation_id += 1
            
            pbar.update(1)
            # Reset description for next group
            if expected_comparisons > 1000:
                pbar.set_description("Processing annotation groups")
    
    # Create new COCO data
    new_coco_data = coco_data.copy()
    new_coco_data['annotations'] = deduplicated_annotations
    
    return new_coco_data

def merge_annotations(annotations, new_id):
    """Merge a group of duplicate annotations.
    
    Strategy:
    - Use the largest bounding box (union)
    - Set occluded=True if any annotation was occluded
    - Use the first annotation's other properties as base
    """
    if len(annotations) == 1:
        ann = annotations[0].copy()
        ann['id'] = new_id
        return ann
    
    # Calculate union of all bounding boxes
    min_x = min(ann['bbox'][0] for ann in annotations)
    min_y = min(ann['bbox'][1] for ann in annotations)
    max_x = max(ann['bbox'][0] + ann['bbox'][2] for ann in annotations)
    max_y = max(ann['bbox'][1] + ann['bbox'][3] for ann in annotations)
    
    union_bbox = [min_x, min_y, max_x - min_x, max_y - min_y]
    union_area = (max_x - min_x) * (max_y - min_y)
    
    # Check if any annotation was occluded
    any_occluded = any(ann.get('attributes', {}).get('occluded', False) for ann in annotations)
    
    # Use first annotation as base and update key fields
    merged = annotations[0].copy()
    merged['id'] = new_id
    merged['bbox'] = union_bbox
    merged['area'] = union_area
    
    # Ensure attributes exist and set occluded flag
    if 'attributes' not in merged:
        merged['attributes'] = {}
    merged['attributes']['occluded'] = any_occluded
    
    return merged

def analyze_duplicates(coco_data, iou_threshold=0.7):
    """Analyze potential duplicates before processing."""
    grouped_annotations = defaultdict(list)
    for ann in coco_data['annotations']:
        key = (ann['image_id'], ann['category_id'])
        grouped_annotations[key].append(ann)
    
    duplicate_stats = {
        'total_groups': len(grouped_annotations),
        'groups_with_duplicates': 0,
        'total_duplicates_found': 0,
        'images_affected': set(),
        'largest_group_size': 0,
        'total_comparisons': 0
    }
    
    # Calculate total comparisons needed
    for (image_id, category_id), annotations in grouped_annotations.items():
        if len(annotations) > duplicate_stats['largest_group_size']:
            duplicate_stats['largest_group_size'] = len(annotations)
        
        if len(annotations) <= 1:
            continue
        
        # Add to total comparisons count
        comparisons = len(annotations) * (len(annotations) - 1) // 2
        duplicate_stats['total_comparisons'] += comparisons
            
        # Check for duplicates in this group with progress for large groups
        has_duplicates = False
        if len(annotations) > 100:  # Show progress for large groups
            iterator = tqdm(range(len(annotations)), 
                          desc=f"Analyzing image {image_id}, category {category_id}")
        else:
            iterator = range(len(annotations))
            
        for i in iterator:
            for j in range(i + 1, len(annotations)):
                iou = calculate_iou(annotations[i]['bbox'], annotations[j]['bbox'])
                if iou >= iou_threshold:
                    has_duplicates = True
                    duplicate_stats['total_duplicates_found'] += 1
        
        if has_duplicates:
            duplicate_stats['groups_with_duplicates'] += 1
            duplicate_stats['images_affected'].add(image_id)
    
    duplicate_stats['images_affected'] = len(duplicate_stats['images_affected'])
    return duplicate_stats

def load_coco_data(input_path):
    """Load COCO data from either a JSON file or ZIP archive.
    
    Args:
        input_path: Path to JSON file or ZIP archive
        
    Returns:
        COCO data dictionary
    """
    if input_path.lower().endswith('.zip'):
        # Handle ZIP file
        with zipfile.ZipFile(input_path, 'r') as zip_file:
            # Look for instances_default.json or similar
            json_files = [f for f in zip_file.namelist() if f.endswith('.json') and 'instances' in f]
            
            if not json_files:
                raise ValueError(f"No instances JSON file found in ZIP: {input_path}")
            
            if len(json_files) > 1:
                print(f"Multiple JSON files found: {json_files}")
                print(f"Using: {json_files[0]}")
            
            json_file = json_files[0]
            with zip_file.open(json_file) as f:
                return json.load(f)
    
    elif input_path.lower().endswith('.json'):
        # Handle JSON file directly
        with open(input_path, 'r') as f:
            return json.load(f)
    
    else:
        raise ValueError(f"Unsupported file type. Expected .json or .zip, got: {input_path}")

def main():
    parser = argparse.ArgumentParser(
        description="Deduplicate COCO annotations based on bounding box IoU",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python deduplicate_coco.py data.zip
  python deduplicate_coco.py instances_default.json --iou-threshold 0.8
  python deduplicate_coco.py data.zip --output cleaned_data.json --analyze-only
        """
    )
    
    parser.add_argument('input', 
                       help='Input COCO file (JSON) or ZIP archive containing COCO data')
    
    parser.add_argument('--iou-threshold', '-t', 
                       type=float, 
                       default=0.7,
                       help='IoU threshold for considering boxes as duplicates (default: 0.7)')
    
    parser.add_argument('--output', '-o',
                       help='Output file path (default: adds "_deduplicated" to input name)')
    
    parser.add_argument('--analyze-only', '-a',
                       action='store_true',
                       help='Only analyze duplicates without creating output file')
    
    args = parser.parse_args()
    
    # Validate input file exists
    if not os.path.exists(args.input):
        print(f"Error: Input file not found: {args.input}")
        sys.exit(1)
    
    # Validate IoU threshold
    if not 0 <= args.iou_threshold <= 1:
        print(f"Error: IoU threshold must be between 0 and 1, got: {args.iou_threshold}")
        sys.exit(1)
    
    try:
        # Load COCO data
        print(f"Loading COCO data from: {args.input}")
        coco_data = load_coco_data(args.input)
        print(f"Loaded {len(coco_data['annotations'])} annotations from {len(coco_data['images'])} images")
        
        # Analyze duplicates
        print(f"\nAnalyzing duplicates with IoU threshold: {args.iou_threshold}")
        stats = analyze_duplicates(coco_data, iou_threshold=args.iou_threshold)
        
        print(f"Analysis results:")
        print(f"  - Total annotation groups: {stats['total_groups']}")
        print(f"  - Groups with potential duplicates: {stats['groups_with_duplicates']}")
        print(f"  - Total duplicate pairs found: {stats['total_duplicates_found']}")
        print(f"  - Images affected: {stats['images_affected']}")
        print(f"  - Largest group size: {stats['largest_group_size']} annotations")
        print(f"  - Total comparisons needed: {stats['total_comparisons']:,}")
        
        if not HAS_TQDM and stats['total_comparisons'] > 10000:
            print(f"  Note: Install 'tqdm' for better progress bars: pip install tqdm")
        
        if args.analyze_only:
            print("\nAnalysis complete (--analyze-only specified)")
            return
        
        # Deduplicate
        print(f"\nDeduplicating annotations...")
        deduplicated_data = deduplicate_annotations(coco_data, iou_threshold=args.iou_threshold)
        
        removed_count = len(coco_data['annotations']) - len(deduplicated_data['annotations'])
        print(f"Deduplicated: {len(deduplicated_data['annotations'])} annotations")
        print(f"Removed: {removed_count} duplicate annotations")
        
        # Determine output path
        if args.output:
            output_path = args.output
        else:
            base_name = os.path.splitext(args.input)[0]
            output_path = f"{base_name}_deduplicated.json"
        
        # Save result
        print(f"Saving deduplicated data to: {output_path}")
        with open(output_path, 'w') as f:
            json.dump(deduplicated_data, f, indent=2)
        
        print("Done!")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()