{"cells": [{"cell_type": "markdown", "id": "ff9ed367", "metadata": {}, "source": ["Divide stratum to left and right"]}, {"cell_type": "code", "execution_count": 1, "id": "8daf5dd7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "b489dc6c-8845-4df1-9680-2fd060786fc4", "metadata": {}, "outputs": [], "source": ["coco_file_path = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023/utils/CVATdata/DANstratum/annotationsdancoco.json\""]}, {"cell_type": "code", "execution_count": 3, "id": "23b685d4", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "images_df = pd.DataFrame(coco_data['images'])\n"]}, {"cell_type": "code", "execution_count": 4, "id": "4a1f8fd3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(274, 6)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "8e9230e2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062738_M0309381.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062756_M0309390.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220906A-062317_M0409632.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   1   7008    4672  KES22_IIM-L_20220906A-062714_M0309369.jpg        1   \n", "1   2   7008    4672  KES22_IIM-L_20220906A-062716_M0309370.jpg        1   \n", "2   3   7008    4672  KES22_IIM-L_20220906A-062738_M0309381.jpg        1   \n", "3   4   7008    4672  KES22_IIM-L_20220906A-062756_M0309390.jpg        1   \n", "4   5   7008    4672  KES22_IIM-R_20220906A-062317_M0409632.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-06  \n", "1    2022-09-06  \n", "2    2022-09-06  \n", "3    2022-09-06  \n", "4    2022-09-06  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "60719a18", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "id": "28706da0", "metadata": {}, "outputs": [], "source": ["pattern1 = \"KES22_IIM-L_\""]}, {"cell_type": "code", "execution_count": 8, "id": "4e4444a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(134, 6)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "danposve_df = images_df[images_df['file_name'].str.contains(pattern1, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "danposve_df.shape"]}, {"cell_type": "code", "execution_count": 10, "id": "060a581a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062738_M0309381.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062756_M0309390.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>31</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060403_M0308676.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0    1   7008    4672  KES22_IIM-L_20220906A-062714_M0309369.jpg        1   \n", "1    2   7008    4672  KES22_IIM-L_20220906A-062716_M0309370.jpg        1   \n", "2    3   7008    4672  KES22_IIM-L_20220906A-062738_M0309381.jpg        1   \n", "3    4   7008    4672  KES22_IIM-L_20220906A-062756_M0309390.jpg        1   \n", "30  31   7008    4672  KES22_IIM-L_20220906A-060403_M0308676.jpg        1   \n", "\n", "   date_captured  \n", "0     2022-09-06  \n", "1     2022-09-06  \n", "2     2022-09-06  \n", "3     2022-09-06  \n", "30    2022-09-06  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["danposve_df.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "594ad7b9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>269</th>\n", "      <td>270</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065544_M0300222.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>270</th>\n", "      <td>271</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065835_M0300307.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>271</th>\n", "      <td>272</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065833_M0300306.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>272</th>\n", "      <td>273</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065943_M0300341.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>273</th>\n", "      <td>274</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065550_M0300225.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "269  270   7008    4672  KES22_IIM-L_20220906A-065544_M0300222.jpg        1   \n", "270  271   7008    4672  KES22_IIM-L_20220906A-065835_M0300307.jpg        1   \n", "271  272   7008    4672  KES22_IIM-L_20220906A-065833_M0300306.jpg        1   \n", "272  273   7008    4672  KES22_IIM-L_20220906A-065943_M0300341.jpg        1   \n", "273  274   7008    4672  KES22_IIM-L_20220906A-065550_M0300225.jpg        1   \n", "\n", "    date_captured  \n", "269    2022-09-06  \n", "270    2022-09-06  \n", "271    2022-09-06  \n", "272    2022-09-06  \n", "273    2022-09-06  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["danposve_df.tail()"]}, {"cell_type": "code", "execution_count": 30, "id": "164fc814", "metadata": {}, "outputs": [], "source": ["DANleftsorted = danposve_df.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 45, "id": "932d181b-abf7-45cb-b26d-c73c7d9163f0", "metadata": {}, "outputs": [], "source": ["DANleftsorted.to_csv(\"DANleftimagespos.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 31, "id": "7bc344e3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>35</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060329_M0308659.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>34</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060331_M0308660.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>37</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060335_M0308662.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>130</th>\n", "      <td>131</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060339_M0308664.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>31</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060403_M0308676.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "34    35   7008    4672  KES22_IIM-L_20220906A-060329_M0308659.jpg        1   \n", "33    34   7008    4672  KES22_IIM-L_20220906A-060331_M0308660.jpg        1   \n", "36    37   7008    4672  KES22_IIM-L_20220906A-060335_M0308662.jpg        1   \n", "130  131   7008    4672  KES22_IIM-L_20220906A-060339_M0308664.jpg        1   \n", "30    31   7008    4672  KES22_IIM-L_20220906A-060403_M0308676.jpg        1   \n", "\n", "    date_captured  \n", "34     2022-09-06  \n", "33     2022-09-06  \n", "36     2022-09-06  \n", "130    2022-09-06  \n", "30     2022-09-06  "]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["DANleftsorted.head()"]}, {"cell_type": "code", "execution_count": 49, "id": "07d3b903", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>Task ID</th>\n", "      <th>ImageFilename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062708_M0309366.jpg</td>\n", "      <td>6530d3400e8be9004ca314e2</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062718_M0309371.jpg</td>\n", "      <td>6530d3cb0e8be9004ca31510</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062712_M0309368.jpg</td>\n", "      <td>6530d3f80e8be9004ca3151d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "      <td>6530d42a0e8be9004ca3152d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062706_M0309365.jpg</td>\n", "      <td>6530d4af0e8be9004ca3155e</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      TaskName                   Task ID  \\\n", "0  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "1  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "2  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "3  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "4  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "\n", "                               ImageFilename                  Image ID  \\\n", "0  KES22_IIM-L_20220906A-062708_M0309366.jpg  6530d3400e8be9004ca314e2   \n", "1  KES22_IIM-L_20220906A-062718_M0309371.jpg  6530d3cb0e8be9004ca31510   \n", "2  KES22_IIM-L_20220906A-062712_M0309368.jpg  6530d3f80e8be9004ca3151d   \n", "3  KES22_IIM-L_20220906A-062716_M0309370.jpg  6530d42a0e8be9004ca3152d   \n", "4  KES22_IIM-L_20220906A-062706_M0309365.jpg  6530d4af0e8be9004ca3155e   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                 True   \n", "1          4672         7008             NaN                 True   \n", "2          4672         7008             NaN                 True   \n", "3          4672         7008             NaN                 True   \n", "4          4672         7008             NaN                 True   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["imgpath = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023/scoutexports/DANgtscout-export-images.csv\"\n", "danimgs = pd.read_csv(imgpath)\n", "danimgs.head()"]}, {"cell_type": "code", "execution_count": 17, "id": "87ff32c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(13154, 11)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["danimgs.shape"]}, {"cell_type": "code", "execution_count": 18, "id": "fbff7df6", "metadata": {}, "outputs": [{"data": {"text/plain": ["8534"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["danimgs[\"ImageFilename\"].nunique()"]}, {"cell_type": "code", "execution_count": 19, "id": "6b578304", "metadata": {}, "outputs": [], "source": ["danimgsunique = danimgs[\"ImageFilename\"].unique()"]}, {"cell_type": "code", "execution_count": 20, "id": "7675cd62", "metadata": {}, "outputs": [{"data": {"text/plain": ["8534"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["len(danimgsunique)"]}, {"cell_type": "code", "execution_count": 21, "id": "c1e7f0f8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220906A-062708_M0309366.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220906A-062718_M0309371.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220906A-062712_M0309368.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220906A-062706_M0309365.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   file_name\n", "0  KES22_IIM-L_20220906A-062708_M0309366.jpg\n", "1  KES22_IIM-L_20220906A-062718_M0309371.jpg\n", "2  KES22_IIM-L_20220906A-062712_M0309368.jpg\n", "3  KES22_IIM-L_20220906A-062716_M0309370.jpg\n", "4  KES22_IIM-L_20220906A-062706_M0309365.jpg"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["danimgsdf = pd.DataFrame(danimgsunique,columns=[\"file_name\"])\n", "danimgsdf.head()"]}, {"cell_type": "code", "execution_count": 22, "id": "b6debf9d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3995, 1)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["danimgsuniqueleft = danimgsdf[danimgsdf['file_name'].str.contains(pattern1, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "danimgsuniqueleft.shape"]}, {"cell_type": "code", "execution_count": 23, "id": "929403df-0066-490d-b8ef-5c1435665e37", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220906A-062708_M0309366.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220906A-062718_M0309371.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220906A-062712_M0309368.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220906A-062706_M0309365.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   file_name\n", "0  KES22_IIM-L_20220906A-062708_M0309366.jpg\n", "1  KES22_IIM-L_20220906A-062718_M0309371.jpg\n", "2  KES22_IIM-L_20220906A-062712_M0309368.jpg\n", "3  KES22_IIM-L_20220906A-062716_M0309370.jpg\n", "4  KES22_IIM-L_20220906A-062706_M0309365.jpg"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["danimgsuniqueleft.head()"]}, {"cell_type": "code", "execution_count": 24, "id": "d6ef5859-5519-42a5-b3da-d836cb433c7e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>8529</th>\n", "      <td>KES22_IIM-L_20220906A-065851_M0300315.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8530</th>\n", "      <td>KES22_IIM-L_20220906A-065747_M0300283.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8531</th>\n", "      <td>KES22_IIM-L_20220906A-065626_M0300243.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8532</th>\n", "      <td>KES22_IIM-L_20220906A-065953_M0300346.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8533</th>\n", "      <td>KES22_IIM-L_20220906A-065528_M0300214.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      file_name\n", "8529  KES22_IIM-L_20220906A-065851_M0300315.jpg\n", "8530  KES22_IIM-L_20220906A-065747_M0300283.jpg\n", "8531  KES22_IIM-L_20220906A-065626_M0300243.jpg\n", "8532  KES22_IIM-L_20220906A-065953_M0300346.jpg\n", "8533  KES22_IIM-L_20220906A-065528_M0300214.jpg"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["danimgsuniqueleft.tail()"]}, {"cell_type": "code", "execution_count": 27, "id": "247cec3a-dece-43d8-ab76-c7ff9b1a5244", "metadata": {}, "outputs": [], "source": ["DANleftimgsorted = danimgsuniqueleft.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 28, "id": "6cc04368-35fb-4d16-a052-218249487290", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>328</th>\n", "      <td>KES22_IIM-L_20220906A-060229_M0308629.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>253</th>\n", "      <td>KES22_IIM-L_20220906A-060231_M0308630.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>309</th>\n", "      <td>KES22_IIM-L_20220906A-060233_M0308631.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>251</th>\n", "      <td>KES22_IIM-L_20220906A-060235_M0308632.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>239</th>\n", "      <td>KES22_IIM-L_20220906A-060237_M0308633.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                     file_name\n", "328  KES22_IIM-L_20220906A-060229_M0308629.jpg\n", "253  KES22_IIM-L_20220906A-060231_M0308630.jpg\n", "309  KES22_IIM-L_20220906A-060233_M0308631.jpg\n", "251  KES22_IIM-L_20220906A-060235_M0308632.jpg\n", "239  KES22_IIM-L_20220906A-060237_M0308633.jpg"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["DANleftimgsorted.head()"]}, {"cell_type": "code", "execution_count": 32, "id": "2b99b790-5318-4b36-a53a-16d3756b4037", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in DANleftsorted[\"file_name\"]:\n", "    if item in DANleftimgsorted[\"file_name\"].values:\n", "        index_in_pos = DANleftimgsorted[\"file_name\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(DANleftimgsorted[\"file_name\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(DANleftimgsorted[\"file_name\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 33, "id": "2be9136b-e16e-42d9-b00a-93ec347ddb84", "metadata": {}, "outputs": [{"data": {"text/plain": ["402"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 34, "id": "d7696c45-bfd9-436c-afda-4239c5007d50", "metadata": {}, "outputs": [{"data": {"text/plain": ["284"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 35, "id": "008a9c33-b2e3-4b6f-875e-1a9b32e5f81b", "metadata": {}, "outputs": [], "source": ["cvatimg = pd.DataFrame(fin,columns=[\"cvatfilename\"])"]}, {"cell_type": "code", "execution_count": 36, "id": "8a77236e-0e42-482c-892a-0504f417334f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>234</th>\n", "      <td>KES22_IIM-L_20220906A-060327_M0308658.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>103</th>\n", "      <td>KES22_IIM-L_20220906A-060329_M0308659.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220906A-060331_M0308660.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>238</th>\n", "      <td>KES22_IIM-L_20220906A-060333_M0308661.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>239</th>\n", "      <td>KES22_IIM-L_20220906A-060335_M0308662.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  c<PERSON><PERSON><PERSON><PERSON>\n", "234  KES22_IIM-L_20220906A-060327_M0308658.jpg\n", "103  KES22_IIM-L_20220906A-060329_M0308659.jpg\n", "3    KES22_IIM-L_20220906A-060331_M0308660.jpg\n", "238  KES22_IIM-L_20220906A-060333_M0308661.jpg\n", "239  KES22_IIM-L_20220906A-060335_M0308662.jpg"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin = cvatimg.sort_values(by=[\"cvatfilename\"],ascending=True)\n", "cvatfin.head()"]}, {"cell_type": "code", "execution_count": 37, "id": "ffdef2e9-a764-40b6-9132-94f29afa7e82", "metadata": {}, "outputs": [{"data": {"text/plain": ["(284, 1)"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin.shape"]}, {"cell_type": "code", "execution_count": 38, "id": "afc94256-61ff-436c-a8a0-976acc8ab884", "metadata": {}, "outputs": [], "source": ["cvatfin.to_csv(\"DANleftcvat.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "c2d70828-12c5-45b1-a911-9dc64154f5dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "ac85a34b-1a7e-40be-b824-ed9170a5fdb7", "metadata": {}, "source": ["### DAN Right"]}, {"cell_type": "code", "execution_count": null, "id": "8ddae76b-c500-4d2d-958d-e20286f9ee1e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 39, "id": "1ca074ad", "metadata": {}, "outputs": [], "source": ["pattern2 = \"KES22_IIM-R_\""]}, {"cell_type": "code", "execution_count": 52, "id": "1e9aefc9", "metadata": {}, "outputs": [{"data": {"text/plain": ["(140, 6)"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "DANfiltered_df2 = images_df[images_df['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "DANfiltered_df2.shape"]}, {"cell_type": "code", "execution_count": 64, "id": "3ad7fa26", "metadata": {}, "outputs": [], "source": ["DANrightsorted = DANfiltered_df2.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 54, "id": "786e514b-1138-4c25-a57f-d0dd97b46ca2", "metadata": {}, "outputs": [], "source": ["DANrightsorted.to_csv(\"DANrightimagespos.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 55, "id": "5fd9fbb4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>238</th>\n", "      <td>239</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220906A-060249_M0409020.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>239</th>\n", "      <td>240</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220906A-060251_M0409021.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>237</th>\n", "      <td>238</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220906A-060255_M0409023.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>240</th>\n", "      <td>241</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220906A-060544_M0409107.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>241</th>\n", "      <td>242</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220906A-060546_M0409108.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "238  239   7008    4672  KES22_IIM-R_20220906A-060249_M0409020.jpg        1   \n", "239  240   7008    4672  KES22_IIM-R_20220906A-060251_M0409021.jpg        1   \n", "237  238   7008    4672  KES22_IIM-R_20220906A-060255_M0409023.jpg        1   \n", "240  241   7008    4672  KES22_IIM-R_20220906A-060544_M0409107.jpg        1   \n", "241  242   7008    4672  KES22_IIM-R_20220906A-060546_M0409108.jpg        1   \n", "\n", "    date_captured  \n", "238    2022-09-06  \n", "239    2022-09-06  \n", "237    2022-09-06  \n", "240    2022-09-06  \n", "241    2022-09-06  "]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["DANrightsorted.head()"]}, {"cell_type": "code", "execution_count": null, "id": "4a5e0113-6fbd-40f5-98ae-e7927a9dbdd9", "metadata": {}, "outputs": [], "source": ["# Filter the DataFrame based on the pattern for positive and negative images\n", "DANimgright_rightdf2 = danimgsdf[danimgsdf['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "DANimgright_df2.shape"]}, {"cell_type": "code", "execution_count": 56, "id": "64d5e89c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4539, 1)"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["danimgsuniqueright = danimgsdf[danimgsdf['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "danimgsuniqueright.shape"]}, {"cell_type": "code", "execution_count": 59, "id": "bccea1bf-8ba4-4731-b395-f9884d37d808", "metadata": {}, "outputs": [], "source": ["DANimgrightsorted = danimgsuniqueright.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 60, "id": "557fa857-7da4-489d-9f5c-8db5342e9fac", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6596</th>\n", "      <td>KES22_IIM-R_20220906A-060229_M0409010.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6619</th>\n", "      <td>KES22_IIM-R_20220906A-060231_M0409011.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6612</th>\n", "      <td>KES22_IIM-R_20220906A-060233_M0409012.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6618</th>\n", "      <td>KES22_IIM-R_20220906A-060235_M0409013.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6621</th>\n", "      <td>KES22_IIM-R_20220906A-060237_M0409014.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      file_name\n", "6596  KES22_IIM-R_20220906A-060229_M0409010.jpg\n", "6619  KES22_IIM-R_20220906A-060231_M0409011.jpg\n", "6612  KES22_IIM-R_20220906A-060233_M0409012.jpg\n", "6618  KES22_IIM-R_20220906A-060235_M0409013.jpg\n", "6621  KES22_IIM-R_20220906A-060237_M0409014.jpg"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["DANimgrightsorted.head()"]}, {"cell_type": "code", "execution_count": 62, "id": "ca07d990-3f37-4f08-b294-485a4a12362d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4539, 1)"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["DANimgrightsorted.shape"]}, {"cell_type": "code", "execution_count": 65, "id": "0aeadeba-27d9-4356-9ce1-0ef494a78e4a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(140, 6)"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["DANrightsorted.shape"]}, {"cell_type": "code", "execution_count": 66, "id": "b879b12b-d248-4bc7-baa7-f9119aee8f71", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in DANrightsorted[\"file_name\"]:\n", "    if item in DANimgrightsorted[\"file_name\"].values:\n", "        index_in_pos = DANimgrightsorted[\"file_name\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(DANimgrightsorted[\"file_name\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(DANimgrightsorted[\"file_name\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 67, "id": "db6f4e5d-fbb6-46a9-9bee-8476dbe5cbbb", "metadata": {}, "outputs": [{"data": {"text/plain": ["420"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 68, "id": "7badf276-6239-486d-b2ba-9cb2a2c3bcef", "metadata": {}, "outputs": [{"data": {"text/plain": ["304"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 69, "id": "82318eab-c30a-427e-96de-befaea605c12", "metadata": {}, "outputs": [], "source": ["cvatimg = pd.DataFrame(fin,columns=[\"cvatfilename\"])"]}, {"cell_type": "code", "execution_count": 70, "id": "efc73194-3fdb-436e-bcb9-09a25d0b2c1d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>KES22_IIM-R_20220906A-060247_M0409019.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>145</th>\n", "      <td>KES22_IIM-R_20220906A-060249_M0409020.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>289</th>\n", "      <td>KES22_IIM-R_20220906A-060251_M0409021.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>287</th>\n", "      <td>KES22_IIM-R_20220906A-060253_M0409022.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>293</th>\n", "      <td>KES22_IIM-R_20220906A-060255_M0409023.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  c<PERSON><PERSON><PERSON><PERSON>\n", "39   KES22_IIM-R_20220906A-060247_M0409019.jpg\n", "145  KES22_IIM-R_20220906A-060249_M0409020.jpg\n", "289  KES22_IIM-R_20220906A-060251_M0409021.jpg\n", "287  KES22_IIM-R_20220906A-060253_M0409022.jpg\n", "293  KES22_IIM-R_20220906A-060255_M0409023.jpg"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin = cvatimg.sort_values(by=[\"cvatfilename\"],ascending=True)\n", "cvatfin.head()"]}, {"cell_type": "code", "execution_count": 71, "id": "67455f80-c824-4529-b515-437b119701c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(304, 1)"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin.shape"]}, {"cell_type": "code", "execution_count": 72, "id": "dc41c311-5c01-4556-8eca-b38590211628", "metadata": {}, "outputs": [], "source": ["cvatfin.to_csv(\"DANrightcvat.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "a9973fe2-38f9-4102-8131-4cc4d2db4c4e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b16ac600-595c-47fe-ba9a-b7c7d29f7111", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5259e8b4-d426-487d-91fd-92baeb3e1556", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "cc806d4c-c697-48e4-89e3-129958131725", "metadata": {}, "source": ["Annotations"]}, {"cell_type": "code", "execution_count": 73, "id": "98589797", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "annotationsdf = pd.DataFrame(coco_data['annotations'])\n"]}, {"cell_type": "code", "execution_count": 74, "id": "9ab9d2b2-f241-4195-a1b0-99e7e1ff6688", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>2797.910556</td>\n", "      <td>[6351.043017, 1166.623453, 60.22392457, 46.458...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1497.501603</td>\n", "      <td>[6482.702368, 1211.366282, 41.64071594, 35.962...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1235.976203</td>\n", "      <td>[6102.257645, 1084.551374, 43.53347576, 28.391...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1053.266678</td>\n", "      <td>[6230.965313, 1126.19209, 39.74795613, 26.4986...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1203.733346</td>\n", "      <td>[6405.099216, 1175.403845, -39.74795613, 30.28...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation         area  \\\n", "0   1         1           16           []  2797.910556   \n", "1   2         1           16           []  1497.501603   \n", "2   3         1           16           []  1235.976203   \n", "3   4         1           16           []  1053.266678   \n", "4   5         1           16           []  1203.733346   \n", "\n", "                                                bbox  iscrowd  \n", "0  [6351.043017, 1166.623453, 60.22392457, 46.458...        0  \n", "1  [6482.702368, 1211.366282, 41.64071594, 35.962...        0  \n", "2  [6102.257645, 1084.551374, 43.53347576, 28.391...        0  \n", "3  [6230.965313, 1126.19209, 39.74795613, 26.4986...        0  \n", "4  [6405.099216, 1175.403845, -39.74795613, 30.28...        0  "]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 75, "id": "e4193fb0-f31a-4583-aee1-5466a6d9ff32", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>35</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060329_M0308659.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>34</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060331_M0308660.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>37</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060335_M0308662.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>130</th>\n", "      <td>131</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060339_M0308664.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>31</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060403_M0308676.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "34    35   7008    4672  KES22_IIM-L_20220906A-060329_M0308659.jpg        1   \n", "33    34   7008    4672  KES22_IIM-L_20220906A-060331_M0308660.jpg        1   \n", "36    37   7008    4672  KES22_IIM-L_20220906A-060335_M0308662.jpg        1   \n", "130  131   7008    4672  KES22_IIM-L_20220906A-060339_M0308664.jpg        1   \n", "30    31   7008    4672  KES22_IIM-L_20220906A-060403_M0308676.jpg        1   \n", "\n", "    date_captured  \n", "34     2022-09-06  \n", "33     2022-09-06  \n", "36     2022-09-06  \n", "130    2022-09-06  \n", "30     2022-09-06  "]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["DANleftsorted.head()"]}, {"cell_type": "code", "execution_count": 76, "id": "a8ad9f17-01f2-48aa-b76f-0f35d3009a2a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062738_M0309381.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062756_M0309390.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>31</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060403_M0308676.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>269</th>\n", "      <td>270</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065544_M0300222.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>270</th>\n", "      <td>271</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065835_M0300307.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>271</th>\n", "      <td>272</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065833_M0300306.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>272</th>\n", "      <td>273</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065943_M0300341.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>273</th>\n", "      <td>274</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065550_M0300225.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>134 rows × 6 columns</p>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "0      1   7008    4672  KES22_IIM-L_20220906A-062714_M0309369.jpg        1   \n", "1      2   7008    4672  KES22_IIM-L_20220906A-062716_M0309370.jpg        1   \n", "2      3   7008    4672  KES22_IIM-L_20220906A-062738_M0309381.jpg        1   \n", "3      4   7008    4672  KES22_IIM-L_20220906A-062756_M0309390.jpg        1   \n", "30    31   7008    4672  KES22_IIM-L_20220906A-060403_M0308676.jpg        1   \n", "..   ...    ...     ...                                        ...      ...   \n", "269  270   7008    4672  KES22_IIM-L_20220906A-065544_M0300222.jpg        1   \n", "270  271   7008    4672  KES22_IIM-L_20220906A-065835_M0300307.jpg        1   \n", "271  272   7008    4672  KES22_IIM-L_20220906A-065833_M0300306.jpg        1   \n", "272  273   7008    4672  KES22_IIM-L_20220906A-065943_M0300341.jpg        1   \n", "273  274   7008    4672  KES22_IIM-L_20220906A-065550_M0300225.jpg        1   \n", "\n", "    date_captured  \n", "0      2022-09-06  \n", "1      2022-09-06  \n", "2      2022-09-06  \n", "3      2022-09-06  \n", "30     2022-09-06  \n", "..            ...  \n", "269    2022-09-06  \n", "270    2022-09-06  \n", "271    2022-09-06  \n", "272    2022-09-06  \n", "273    2022-09-06  \n", "\n", "[134 rows x 6 columns]"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["DANleftsorted.sort_values(by=[\"id\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 77, "id": "fd7c2b81-3bad-49b8-98c7-9bd405abc281", "metadata": {}, "outputs": [], "source": ["# Compare image 'ID' columns and return annotations \n", "result_values = annotationsdf[annotationsdf['image_id'].isin(DANleftsorted['id'])]"]}, {"cell_type": "code", "execution_count": 78, "id": "dc2d2883-f90f-4b14-8e97-23bed48462ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1107, 7)"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values.shape"]}, {"cell_type": "code", "execution_count": 79, "id": "a013ab74-3c01-47b8-8c87-83c9ddd2ac7c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1839, 7)"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.shape"]}, {"cell_type": "code", "execution_count": 80, "id": "c57c8809-7d45-470d-8e25-4f583132f5b7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>2797.910556</td>\n", "      <td>[6351.043017, 1166.623453, 60.22392457, 46.458...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1497.501603</td>\n", "      <td>[6482.702368, 1211.366282, 41.64071594, 35.962...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1235.976203</td>\n", "      <td>[6102.257645, 1084.551374, 43.53347576, 28.391...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1053.266678</td>\n", "      <td>[6230.965313, 1126.19209, 39.74795613, 26.4986...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1203.733346</td>\n", "      <td>[6405.099216, 1175.403845, -39.74795613, 30.28...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation         area  \\\n", "0   1         1           16           []  2797.910556   \n", "1   2         1           16           []  1497.501603   \n", "2   3         1           16           []  1235.976203   \n", "3   4         1           16           []  1053.266678   \n", "4   5         1           16           []  1203.733346   \n", "\n", "                                                bbox  iscrowd  \n", "0  [6351.043017, 1166.623453, 60.22392457, 46.458...        0  \n", "1  [6482.702368, 1211.366282, 41.64071594, 35.962...        0  \n", "2  [6102.257645, 1084.551374, 43.53347576, 28.391...        0  \n", "3  [6230.965313, 1126.19209, 39.74795613, 26.4986...        0  \n", "4  [6405.099216, 1175.403845, -39.74795613, 30.28...        0  "]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values.head()"]}, {"cell_type": "code", "execution_count": 81, "id": "c8e87cea-2d9f-4ee4-b71f-1eaaced692da", "metadata": {}, "outputs": [], "source": ["result_values.to_csv(\"DANleftannotations.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "431bd5e3-19b8-4473-b5f2-07460645bd12", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 82, "id": "2e2b725d-2c17-4191-87d6-b76cd869ca62", "metadata": {}, "outputs": [], "source": ["# Compare image 'ID' columns and return annotations \n", "result_valuesright = annotationsdf[annotationsdf['image_id'].isin(DANrightsorted['id'])]"]}, {"cell_type": "code", "execution_count": 83, "id": "438e2ee3-45d1-4789-b428-c18564fd830f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(732, 7)"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["result_valuesright.shape"]}, {"cell_type": "code", "execution_count": 84, "id": "329c5336-6af2-4697-8242-d9471608b45f", "metadata": {}, "outputs": [], "source": ["result_valuesright.to_csv(\"DANrightannotations.csv\",index=False)"]}, {"cell_type": "markdown", "id": "368d46a6-a4bf-443e-b5ee-4e21fd1d5a89", "metadata": {}, "source": ["### <PERSON><PERSON> left COCO file"]}, {"cell_type": "code", "execution_count": 86, "id": "e844f260-cf39-4a71-9c92-22e761e93057", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>2797.910556</td>\n", "      <td>[6351.043017, 1166.623453, 60.22392457, 46.458...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1497.501603</td>\n", "      <td>[6482.702368, 1211.366282, 41.64071594, 35.962...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1235.976203</td>\n", "      <td>[6102.257645, 1084.551374, 43.53347576, 28.391...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1053.266678</td>\n", "      <td>[6230.965313, 1126.19209, 39.74795613, 26.4986...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1203.733346</td>\n", "      <td>[6405.099216, 1175.403845, -39.74795613, 30.28...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation         area  \\\n", "0   1         1           16           []  2797.910556   \n", "1   2         1           16           []  1497.501603   \n", "2   3         1           16           []  1235.976203   \n", "3   4         1           16           []  1053.266678   \n", "4   5         1           16           []  1203.733346   \n", "\n", "                                                bbox  iscrowd  \n", "0  [6351.043017, 1166.623453, 60.22392457, 46.458...        0  \n", "1  [6482.702368, 1211.366282, 41.64071594, 35.962...        0  \n", "2  [6102.257645, 1084.551374, 43.53347576, 28.391...        0  \n", "3  [6230.965313, 1126.19209, 39.74795613, 26.4986...        0  \n", "4  [6405.099216, 1175.403845, -39.74795613, 30.28...        0  "]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["danleftanno = pd.read_csv(\"DANleftannotations.csv\")\n", "danleftanno.head()"]}, {"cell_type": "code", "execution_count": 88, "id": "c8281f42-a5ea-4e4f-beaf-0d88136baa5e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>35</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060329_M0308659.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>34</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060331_M0308660.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>37</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060335_M0308662.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>131</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060339_M0308664.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>31</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060403_M0308676.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0   35   7008    4672  KES22_IIM-L_20220906A-060329_M0308659.jpg        1   \n", "1   34   7008    4672  KES22_IIM-L_20220906A-060331_M0308660.jpg        1   \n", "2   37   7008    4672  KES22_IIM-L_20220906A-060335_M0308662.jpg        1   \n", "3  131   7008    4672  KES22_IIM-L_20220906A-060339_M0308664.jpg        1   \n", "4   31   7008    4672  KES22_IIM-L_20220906A-060403_M0308676.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-06  \n", "1    2022-09-06  \n", "2    2022-09-06  \n", "3    2022-09-06  \n", "4    2022-09-06  "]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["danleftimages = pd.read_csv(\"DANleftimagespos.csv\")\n", "danleftimages.head()"]}, {"cell_type": "code", "execution_count": 89, "id": "bc5b969d-9351-4fad-83cb-27aec08851b4", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = danleftimages.to_dict(orient='records')\n", "coco_dict['annotations'] = danleftanno.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'danleftannotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": 32, "id": "25d27e8d-9b16-49d6-80ad-d0457b4526a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'id': 1,\n", "  'name': 'KAZA Elephant Survey',\n", "  'url': 'https://www.kavangozambezi.org/kaza-elephant-survey/'}]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["coco_data[\"licenses\"]"]}, {"cell_type": "code", "execution_count": 90, "id": "36226de4-c23f-45dd-8d40-92c76b860f57", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>35</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>11585.745700</td>\n", "      <td>[3147.13928, 691.8088799, 124.9211356, -92.744...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>36</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>6315.915175</td>\n", "      <td>[3561.822336, 474.1432647, 81.38801262, 77.602...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>37</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>4882.922510</td>\n", "      <td>[3726.491106, 697.4871133, 54.88958991, -88.95...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>38</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>11177.342797</td>\n", "      <td>[3382.011611, 890.5470502, 123.0283912, -90.85...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>39</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>16067.430272</td>\n", "      <td>[5190.787042, 356.793107, -130.5993691, -123.0...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0  35         5            5           []  11585.745700   \n", "1  36         5            5           []   6315.915175   \n", "2  37         5            5           []   4882.922510   \n", "3  38         5            5           []  11177.342797   \n", "4  39         5            5           []  16067.430272   \n", "\n", "                                                bbox  iscrowd  \n", "0  [3147.13928, 691.8088799, 124.9211356, -92.744...        0  \n", "1  [3561.822336, 474.1432647, 81.38801262, 77.602...        0  \n", "2  [3726.491106, 697.4871133, 54.88958991, -88.95...        0  \n", "3  [3382.011611, 890.5470502, 123.0283912, -90.85...        0  \n", "4  [5190.787042, 356.793107, -130.5993691, -123.0...        0  "]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["danrightanno = pd.read_csv(\"DANrightannotations.csv\")\n", "danrightanno.head()"]}, {"cell_type": "code", "execution_count": 91, "id": "a55bfcb9-13ee-4468-bb3e-31c973044267", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>239</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220906A-060249_M0409020.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>240</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220906A-060251_M0409021.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>238</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220906A-060255_M0409023.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>241</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220906A-060544_M0409107.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>242</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220906A-060546_M0409108.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0  239   7008    4672  KES22_IIM-R_20220906A-060249_M0409020.jpg        1   \n", "1  240   7008    4672  KES22_IIM-R_20220906A-060251_M0409021.jpg        1   \n", "2  238   7008    4672  KES22_IIM-R_20220906A-060255_M0409023.jpg        1   \n", "3  241   7008    4672  KES22_IIM-R_20220906A-060544_M0409107.jpg        1   \n", "4  242   7008    4672  KES22_IIM-R_20220906A-060546_M0409108.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-06  \n", "1    2022-09-06  \n", "2    2022-09-06  \n", "3    2022-09-06  \n", "4    2022-09-06  "]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["danrightimages = pd.read_csv(\"DANrightimagespos.csv\")\n", "danrightimages.head()"]}, {"cell_type": "code", "execution_count": 92, "id": "6df4b37a-be3d-44d3-a10a-b92fe5a7ec73", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dictright = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dictright['images'] = danrightimages.to_dict(orient='records')\n", "coco_dictright['annotations'] = danrightanno.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'danrightannotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dictright, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "id": "254708b5-3db5-489a-bc35-918c449acee8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16dd420b-4f4d-42de-9aa8-1917a1d7a760", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "043cfaf2-580a-4a72-9871-0e8bbad31a7f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}