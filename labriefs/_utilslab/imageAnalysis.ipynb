{"cells": [{"cell_type": "code", "execution_count": 1, "id": "31c2224b", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "086764f0-961f-4607-948d-6784ef54c5ce", "metadata": {}, "outputs": [], "source": ["homedir = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023\""]}, {"cell_type": "code", "execution_count": 3, "id": "92bd1a6f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DAN05-L_174</th>\n", "      <th>6530de5f05667e0045cadc4e</th>\n", "      <th>KES22_IIM-L_20220906A-062708_M0309366.jpg</th>\n", "      <th>6530d3400e8be9004ca314e2</th>\n", "      <th>4672</th>\n", "      <th>7008</th>\n", "      <th>Unnamed: 6</th>\n", "      <th>true</th>\n", "      <th>right</th>\n", "      <th>Unnamed: 9</th>\n", "      <th>Unnamed: 10</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062718_M0309371.jpg</td>\n", "      <td>6530d3cb0e8be9004ca31510</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062712_M0309368.jpg</td>\n", "      <td>6530d3f80e8be9004ca3151d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "      <td>6530d42a0e8be9004ca3152d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062706_M0309365.jpg</td>\n", "      <td>6530d4af0e8be9004ca3155e</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062722_M0309373.jpg</td>\n", "      <td>6530d4d70e8be9004ca3156d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   DAN05-L_174  6530de5f05667e0045cadc4e  \\\n", "0  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "1  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "2  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "3  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "4  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "\n", "   KES22_IIM-L_20220906A-062708_M0309366.jpg  6530d3400e8be9004ca314e2  4672  \\\n", "0  KES22_IIM-L_20220906A-062718_M0309371.jpg  6530d3cb0e8be9004ca31510  4672   \n", "1  KES22_IIM-L_20220906A-062712_M0309368.jpg  6530d3f80e8be9004ca3151d  4672   \n", "2  KES22_IIM-L_20220906A-062716_M0309370.jpg  6530d42a0e8be9004ca3152d  4672   \n", "3  KES22_IIM-L_20220906A-062706_M0309365.jpg  6530d4af0e8be9004ca3155e  4672   \n", "4  KES22_IIM-L_20220906A-062722_M0309373.jpg  6530d4d70e8be9004ca3156d  4672   \n", "\n", "   7008  Unnamed: 6  true  right  Unnamed: 9  Unnamed: 10  \n", "0  7008         NaN  True  right         NaN          NaN  \n", "1  7008         NaN  True  right         NaN          NaN  \n", "2  7008         NaN  True  right         NaN          NaN  \n", "3  7008         NaN  True  right         NaN          NaN  \n", "4  7008         NaN  True  right         NaN          NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["#images and tasks\n", "tasksdf = pd.read_csv(homedir + \"/scoutexports/DANgtscout-export-images.csv\")\n", "tasksdf.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "b8477a28", "metadata": {}, "outputs": [], "source": ["tasksdf.columns = [\"TaskName\",\"TaskID\",\"imageFilename\",\"imageID\",\"imageHeight\",\"imageWidth\",\"WIConfidence\",\"GTstatus\",\"exclusionSide\",\"inclusionTopX\",\"inclusionBottomX\"]"]}, {"cell_type": "code", "execution_count": 5, "id": "fe946ada", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>imageFilename</th>\n", "      <th>imageID</th>\n", "      <th>imageHeight</th>\n", "      <th>imageWidth</th>\n", "      <th>WIConfidence</th>\n", "      <th>GTstatus</th>\n", "      <th>exclusionSide</th>\n", "      <th>inclusionTopX</th>\n", "      <th>inclusionBottomX</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062718_M0309371.jpg</td>\n", "      <td>6530d3cb0e8be9004ca31510</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062712_M0309368.jpg</td>\n", "      <td>6530d3f80e8be9004ca3151d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "      <td>6530d42a0e8be9004ca3152d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062706_M0309365.jpg</td>\n", "      <td>6530d4af0e8be9004ca3155e</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062722_M0309373.jpg</td>\n", "      <td>6530d4d70e8be9004ca3156d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      TaskName                    TaskID  \\\n", "0  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "1  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "2  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "3  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "4  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "\n", "                               imageFilename                   imageID  \\\n", "0  KES22_IIM-L_20220906A-062718_M0309371.jpg  6530d3cb0e8be9004ca31510   \n", "1  KES22_IIM-L_20220906A-062712_M0309368.jpg  6530d3f80e8be9004ca3151d   \n", "2  KES22_IIM-L_20220906A-062716_M0309370.jpg  6530d42a0e8be9004ca3152d   \n", "3  KES22_IIM-L_20220906A-062706_M0309365.jpg  6530d4af0e8be9004ca3155e   \n", "4  KES22_IIM-L_20220906A-062722_M0309373.jpg  6530d4d70e8be9004ca3156d   \n", "\n", "   imageHeight  imageWidth  WIConfidence  GTstatus exclusionSide  \\\n", "0         4672        7008           NaN      True         right   \n", "1         4672        7008           NaN      True         right   \n", "2         4672        7008           NaN      True         right   \n", "3         4672        7008           NaN      True         right   \n", "4         4672        7008           NaN      True         right   \n", "\n", "   inclusionTopX  inclusionBottomX  \n", "0            NaN               NaN  \n", "1            NaN               NaN  \n", "2            NaN               NaN  \n", "3            NaN               NaN  \n", "4            NaN               NaN  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["tasksdf.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "211aabcf", "metadata": {}, "outputs": [{"data": {"text/plain": ["(13153, 11)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["tasksdf.shape"]}, {"cell_type": "code", "execution_count": 7, "id": "cf73b5c2-3142-410a-a233-ae314668bd16", "metadata": {}, "outputs": [{"data": {"text/plain": ["TaskName             object\n", "TaskID               object\n", "imageFilename        object\n", "imageID              object\n", "imageHeight           int64\n", "imageWidth            int64\n", "WIConfidence        float64\n", "GTstatus               bool\n", "exclusionSide        object\n", "inclusionTopX       float64\n", "inclusionBottomX    float64\n", "dtype: object"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["tasksdf.dtypes"]}, {"cell_type": "code", "execution_count": 8, "id": "fcc15d51", "metadata": {}, "outputs": [{"data": {"text/plain": ["147"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["tasksdf[\"TaskName\"].nunique()"]}, {"cell_type": "code", "execution_count": 9, "id": "991c9336", "metadata": {}, "outputs": [{"data": {"text/plain": ["8534"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["tasksdf[\"imageFilename\"].nunique()"]}, {"cell_type": "code", "execution_count": 17, "id": "a17c80dd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Empty DataFrame\n", "Columns: [TaskName, TaskID, imageFilename, imageID, imageHeight, imageWidth, WIConfidence, GTstatus, exclusionSide, inclusionTopX, inclusionBottomX]\n", "Index: []\n"]}], "source": ["# Pattern to filter\n", "pattern = \"M03092\"  # This can be a simple substring or a more complex regular expression\n", "\n", "# Filter the DataFrame based on the pattern\n", "filtered_df = tasksdf[tasksdf['imageFilename'].str.contains(pattern, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "print(filtered_df)"]}, {"cell_type": "code", "execution_count": null, "id": "91e68c9d", "metadata": {}, "outputs": [], "source": ["filtered_df.shape"]}, {"cell_type": "code", "execution_count": null, "id": "2478775f-1dba-400b-9ea7-bb62443affc4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 10, "id": "0a0d0137", "metadata": {}, "outputs": [{"data": {"text/plain": ["False    7839\n", "True     5314\n", "Name: GTstatus, dtype: int64"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["tasksdf[\"GTstatus\"].value_counts()  #Not unique images"]}, {"cell_type": "code", "execution_count": 11, "id": "8f7473a9-5827-4969-9f86-b553904e1f76", "metadata": {}, "outputs": [], "source": ["# filter GT image names\"\n", "filtergt = tasksdf.loc[tasksdf[\"GTstatus\"], [\"TaskName\",\"imageFilename\"]]"]}, {"cell_type": "code", "execution_count": 12, "id": "7dd5c14c-2a56-4ddf-b5db-8c617279a908", "metadata": {}, "outputs": [{"data": {"text/plain": ["(5314, 2)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["filtergt.shape"]}, {"cell_type": "code", "execution_count": 13, "id": "b83ff386", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>imageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>KES22_IIM-L_20220906A-062718_M0309371.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>KES22_IIM-L_20220906A-062712_M0309368.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>KES22_IIM-L_20220906A-062706_M0309365.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>KES22_IIM-L_20220906A-062722_M0309373.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      TaskName                              imageFilename\n", "0  DAN05-L_174  KES22_IIM-L_20220906A-062718_M0309371.jpg\n", "1  DAN05-L_174  KES22_IIM-L_20220906A-062712_M0309368.jpg\n", "2  DAN05-L_174  KES22_IIM-L_20220906A-062716_M0309370.jpg\n", "3  DAN05-L_174  KES22_IIM-L_20220906A-062706_M0309365.jpg\n", "4  DAN05-L_174  KES22_IIM-L_20220906A-062722_M0309373.jpg"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["filtergt.head()"]}, {"cell_type": "code", "execution_count": 14, "id": "8986a16f-541c-4391-8501-2af99874e2dd", "metadata": {}, "outputs": [{"data": {"text/plain": ["2809"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["filtergt[\"imageFilename\"].nunique()"]}, {"cell_type": "code", "execution_count": 15, "id": "b7cf03bd-de94-4b1d-80a9-bd47cdbe373f", "metadata": {}, "outputs": [], "source": ["gtimg = pd.DataFrame({\"GTimagefilename\":filtergt[\"imageFilename\"].unique()})"]}, {"cell_type": "code", "execution_count": 19, "id": "bbd3d45b-d99e-4cf7-b131-b06daf491711", "metadata": {}, "outputs": [], "source": ["gtimg.sort_values(by=\"GTimagefilename\",ignore_index=True,inplace=True)"]}, {"cell_type": "code", "execution_count": 22, "id": "bf3ca93a-55dc-4938-984c-8b13cc2d270f", "metadata": {}, "outputs": [], "source": ["gtimg.to_csv(homedir+\"/labriefs/_media/GTimages_DAN.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "1236bf2d-c69b-4751-88a0-0bb6e94f8789", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5eadb8eb-e563-4874-a9a0-9f6abd9ed721", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8b1b78b8-c1b9-4fed-9f18-ddde68c933cd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b8a1ac90-0672-4112-a836-d93d753b1651", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c17b95ff-e71b-465e-aa18-49f92587a6a2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c4040b48-dc8f-4c5b-b316-6e7b0f509f68", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}