{"cells": [{"cell_type": "markdown", "id": "ff9ed367", "metadata": {}, "source": ["Divide stratum to left and right"]}, {"cell_type": "code", "execution_count": 1, "id": "8daf5dd7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "b489dc6c-8845-4df1-9680-2fd060786fc4", "metadata": {}, "outputs": [], "source": ["coco_file_path = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023/utils/CVATData/MTstratum/annotationsMT.json\""]}, {"cell_type": "code", "execution_count": 3, "id": "23b685d4", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "images_df = pd.DataFrame(coco_data['images'])\n"]}, {"cell_type": "code", "execution_count": 4, "id": "4a1f8fd3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(296, 6)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.shape"]}, {"cell_type": "code", "execution_count": 11, "id": "8e9230e2", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-070510_M0801395.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-053746_M0808781.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054013_M0808854.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_WOT-L_20220909A-054205_M0808910.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   1   7008    4672  KES22_WOT-L_20220909A-070510_M0801395.jpg        1   \n", "1   2   7008    4672  KES22_WOT-L_20220909A-053748_M0808782.jpg        1   \n", "2   3   7008    4672  KES22_WOT-L_20220909A-053746_M0808781.jpg        1   \n", "3   4   7008    4672  KES22_WOT-L_20220909A-054013_M0808854.jpg        1   \n", "4   5   7008    4672  KES22_WOT-L_20220909A-054205_M0808910.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-09  \n", "1    2022-09-09  \n", "2    2022-09-09  \n", "3    2022-09-09  \n", "4    2022-09-09  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "60719a18", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 5, "id": "4e4444a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(83, 6)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "MTbleftposve = pd.read_csv(\"MTbleftimagespos.csv\")\n", "\n", "# Print the resulting filtered DataFrame\n", "MTbleftposve.shape"]}, {"cell_type": "code", "execution_count": 7, "id": "cedc6db4-e0ad-4ebf-bd47-bacec0eae70c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>147</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072836_M1006264.jpg</td>\n", "      <td>1</td>\n", "      <td>09/09/2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>148</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072838_M1006265.jpg</td>\n", "      <td>1</td>\n", "      <td>09/09/2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>149</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072840_M1006266.jpg</td>\n", "      <td>1</td>\n", "      <td>09/09/2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>146</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-072842_M1006267.jpg</td>\n", "      <td>1</td>\n", "      <td>09/09/2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>150</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-L_20220908A-073213_M1006372.jpg</td>\n", "      <td>1</td>\n", "      <td>09/09/2022</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0  147   7008    4672  KES22_LJB-L_20220908A-072836_M1006264.jpg        1   \n", "1  148   7008    4672  KES22_LJB-L_20220908A-072838_M1006265.jpg        1   \n", "2  149   7008    4672  KES22_LJB-L_20220908A-072840_M1006266.jpg        1   \n", "3  146   7008    4672  KES22_LJB-L_20220908A-072842_M1006267.jpg        1   \n", "4  150   7008    4672  KES22_LJB-L_20220908A-073213_M1006372.jpg        1   \n", "\n", "  date_captured  \n", "0    09/09/2022  \n", "1    09/09/2022  \n", "2    09/09/2022  \n", "3    09/09/2022  \n", "4    09/09/2022  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["MTbleftposve.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "5936bd33-4a1a-49d6-9470-15b911ac6e25", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'annotations' section to a DataFrame\n", "annotationsdf = pd.DataFrame(coco_data['annotations'])\n"]}, {"cell_type": "code", "execution_count": 11, "id": "ff19e11c-dd47-45e8-bc46-9f0a7cc1af26", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>3531.029913</td>\n", "      <td>[3136.4668019044, 1525.14141578463, -60.966167...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>5315.129237</td>\n", "      <td>[3136.4668019044, 1576.96265824868, 67.0627843...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>12456.356031</td>\n", "      <td>[1456.64331726931, 1710.85830811493, 115.45772...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>20377.265781</td>\n", "      <td>[1346.86384096244, 1915.27526399669, 149.52721...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>4413.641252</td>\n", "      <td>[1519.10405378873, 1877.42027216673, 52.996988...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1           14           []   3531.029913   \n", "1   2         1           14           []   5315.129237   \n", "2   3         2            9           []  12456.356031   \n", "3   4         2            9           []  20377.265781   \n", "4   5         2            9           []   4413.641252   \n", "\n", "                                                bbox  iscrowd  \n", "0  [3136.4668019044, 1525.14141578463, -60.966167...        0  \n", "1  [3136.4668019044, 1576.96265824868, 67.0627843...        0  \n", "2  [1456.64331726931, 1710.85830811493, 115.45772...        0  \n", "3  [1346.86384096244, 1915.27526399669, 149.52721...        0  \n", "4  [1519.10405378873, 1877.42027216673, 52.996988...        0  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 12, "id": "09c5bc67-82a0-4631-8957-3e46c545e71b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2414, 7)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.shape"]}, {"cell_type": "code", "execution_count": 16, "id": "fd576871-5145-46a5-af0d-adb66418bfe6", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1628, 7)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["#change bbox dimensions to positive\n", "greater_than_zero_mask = [all(item > 0 for item in row) for row in annotationsdf['bbox']]\n", "annotationsdf[greater_than_zero_mask].shape"]}, {"cell_type": "code", "execution_count": 17, "id": "08dc8bc0-1e9e-4f99-8379-849d5e5a1ec7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(786, 7)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["filterpositive = annotationsdf[greater_than_zero_mask]['bbox'].tolist()\n", "less_than_zero_mask = annotationsdf[~annotationsdf['bbox'].isin(filterpositive)]\n", "less_than_zero_mask.shape"]}, {"cell_type": "code", "execution_count": 18, "id": "8c8992de-1b70-4380-8c1a-67582d4a9212", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>3531.029913</td>\n", "      <td>[3136.4668019044, 1525.14141578463, -60.966167...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>12456.356031</td>\n", "      <td>[1456.64331726931, 1710.85830811493, 115.45772...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>20377.265781</td>\n", "      <td>[1346.86384096244, 1915.27526399669, 149.52721...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>4413.641252</td>\n", "      <td>[1519.10405378873, 1877.42027216673, 52.996988...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>14659.594161</td>\n", "      <td>[854.748976054088, 2299.5033733086, 124.921473...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1           14           []   3531.029913   \n", "2   3         2            9           []  12456.356031   \n", "3   4         2            9           []  20377.265781   \n", "4   5         2            9           []   4413.641252   \n", "5   6         2            9           []  14659.594161   \n", "\n", "                                                bbox  iscrowd  \n", "0  [3136.4668019044, 1525.14141578463, -60.966167...        0  \n", "2  [1456.64331726931, 1710.85830811493, 115.45772...        0  \n", "3  [1346.86384096244, 1915.27526399669, 149.52721...        0  \n", "4  [1519.10405378873, 1877.42027216673, 52.996988...        0  \n", "5  [854.748976054088, 2299.5033733086, 124.921473...        0  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["less_than_zero_mask.head()"]}, {"cell_type": "code", "execution_count": 19, "id": "52c8e99b-43e3-41f9-8d08-1203a74c52ea", "metadata": {}, "outputs": [], "source": ["for item in less_than_zero_mask['bbox']:\n", "    \n", "    if((item[-2]<0) and (item[-1]<0)):\n", "        item[-2] = abs(item[-2])\n", "        item[0]  = item[0] + int(abs(item[-2]))\n", "        item[-1] = abs(item[-1])\n", "        item[1] = item[1] + int(abs(item[-1]))\n", "    \n", "    elif((item[0]<0) and (item[1]<0)):\n", "        item[0] = abs(item[0])\n", "        item[-2] = item[-2] + int(abs(item[0]))\n", "        item[1] = abs(item[1])\n", "        item[-1] = item[-1] + int(abs(item[1]))\n", "        \n", "    elif(item[-2]<0):\n", "        item[-2] = abs(item[-2])\n", "        item[0]  = item[0] + int(abs(item[-2]))\n", "    \n", "    elif(item[0]<0):\n", "        item[0] = abs(item[0])\n", "        item[-2] = item[-2] + int(abs(item[0]))\n", "           \n", "    elif(item[1]<0):\n", "        item[1] = abs(item[1])\n", "        item[-1] = item[-1] + int(abs(item[1]))\n", "            \n", "    elif(item[-1]<0):\n", "        item[-1] = abs(item[-1])\n", "        item[1] = item[1] + int(abs(item[-1]))\n", "    "]}, {"cell_type": "code", "execution_count": 20, "id": "e5fc86dd-4057-491d-b9fc-d34c29927b5c", "metadata": {}, "outputs": [], "source": ["annotationsdf.update(less_than_zero_mask)"]}, {"cell_type": "code", "execution_count": 21, "id": "2fca4c7a-61b9-48e0-8635-630acbf19901", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2389, 7)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["#check if all are positives after\n", "greater_than_zero_mask = [all(item > 0 for item in row) for row in annotationsdf['bbox']]\n", "annotationsdf[greater_than_zero_mask].shape"]}, {"cell_type": "code", "execution_count": 22, "id": "b95268a0-7e58-4fd9-9656-0e0330531626", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2414, 7)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.shape"]}, {"cell_type": "code", "execution_count": 23, "id": "c1b9041b-bc2e-4905-910e-a8e0da8f6f02", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>14.0</td>\n", "      <td>[]</td>\n", "      <td>3531.029913</td>\n", "      <td>[3196.4668019044, 1525.14141578463, 60.9661676...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>14.0</td>\n", "      <td>[]</td>\n", "      <td>5315.129237</td>\n", "      <td>[3136.4668019044, 1576.96265824868, 67.0627843...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>9.0</td>\n", "      <td>[]</td>\n", "      <td>12456.356031</td>\n", "      <td>[1456.64331726931, 1817.85830811493, 115.45772...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.0</td>\n", "      <td>2.0</td>\n", "      <td>9.0</td>\n", "      <td>[]</td>\n", "      <td>20377.265781</td>\n", "      <td>[1346.86384096244, 2051.2752639966902, 149.527...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.0</td>\n", "      <td>2.0</td>\n", "      <td>9.0</td>\n", "      <td>[]</td>\n", "      <td>4413.641252</td>\n", "      <td>[1519.10405378873, 1960.42027216673, 52.996988...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  image_id  category_id segmentation          area  \\\n", "0  1.0       1.0         14.0           []   3531.029913   \n", "1  2.0       1.0         14.0           []   5315.129237   \n", "2  3.0       2.0          9.0           []  12456.356031   \n", "3  4.0       2.0          9.0           []  20377.265781   \n", "4  5.0       2.0          9.0           []   4413.641252   \n", "\n", "                                                bbox  iscrowd  \n", "0  [3196.4668019044, 1525.14141578463, 60.9661676...      0.0  \n", "1  [3136.4668019044, 1576.96265824868, 67.0627843...      0.0  \n", "2  [1456.64331726931, 1817.85830811493, 115.45772...      0.0  \n", "3  [1346.86384096244, 2051.2752639966902, 149.527...      0.0  \n", "4  [1519.10405378873, 1960.42027216673, 52.996988...      0.0  "]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 24, "id": "7107393e-eec5-4752-aa97-8cfc3c58a516", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"id\"]=annotationsdf[\"id\"].astype(int)"]}, {"cell_type": "code", "execution_count": 25, "id": "b4cf6930-435c-49e1-a77f-f2e4adf8ff39", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"image_id\"] =annotationsdf[\"image_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": 26, "id": "65aa6f1e-346d-4f73-afee-8868adb71fb6", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"iscrowd\"] = annotationsdf[\"iscrowd\"].astype(int)"]}, {"cell_type": "code", "execution_count": 29, "id": "b2ed837d-4cf5-4a65-9516-d835e35cd51e", "metadata": {}, "outputs": [], "source": ["annotationsdf[\"category_id\"] = annotationsdf[\"category_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": 32, "id": "71be29f0-dd7b-4364-b7b7-8d85dd58b98e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>3531.029913</td>\n", "      <td>[3196.4668019044, 1525.14141578463, 60.9661676...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "      <td>[]</td>\n", "      <td>5315.129237</td>\n", "      <td>[3136.4668019044, 1576.96265824868, 67.0627843...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>12456.356031</td>\n", "      <td>[1456.64331726931, 1817.85830811493, 115.45772...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>20377.265781</td>\n", "      <td>[1346.86384096244, 2051.2752639966902, 149.527...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>4413.641252</td>\n", "      <td>[1519.10405378873, 1960.42027216673, 52.996988...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation          area  \\\n", "0   1         1           14           []   3531.029913   \n", "1   2         1           14           []   5315.129237   \n", "2   3         2            9           []  12456.356031   \n", "3   4         2            9           []  20377.265781   \n", "4   5         2            9           []   4413.641252   \n", "\n", "                                                bbox  iscrowd  \n", "0  [3196.4668019044, 1525.14141578463, 60.9661676...        0  \n", "1  [3136.4668019044, 1576.96265824868, 67.0627843...        0  \n", "2  [1456.64331726931, 1817.85830811493, 115.45772...        0  \n", "3  [1346.86384096244, 2051.2752639966902, 149.527...        0  \n", "4  [1519.10405378873, 1960.42027216673, 52.996988...        0  "]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 33, "id": "5a456950-00c8-44c5-a8d5-dc0f1e5c9ea6", "metadata": {}, "outputs": [{"data": {"text/plain": ["(994, 7)"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return MTb left annotations \n", "\n", "mtbleft_annot = annotationsdf[annotationsdf['image_id'].isin(MTbleftposve['id'])]\n", "mtbleft_annot.shape"]}, {"cell_type": "code", "execution_count": 34, "id": "b335d2f9-bdee-4562-b1da-c34c3d770ddd", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1001</th>\n", "      <td>1002</td>\n", "      <td>146</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>44165.072527</td>\n", "      <td>[186.608370255381, 1722.21474790178, 253.62844...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1002</th>\n", "      <td>1003</td>\n", "      <td>147</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>4831.610139</td>\n", "      <td>[4976.42750190927, 2251.39167778104, 63.345822...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1003</th>\n", "      <td>1004</td>\n", "      <td>147</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>6588.103483</td>\n", "      <td>[5721.06410351307, 1606.57205155592, 69.809681...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1004</th>\n", "      <td>1005</td>\n", "      <td>147</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>1998.825917</td>\n", "      <td>[5729.50143783516, 1523.36714499964, 33.612068...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1005</th>\n", "      <td>1006</td>\n", "      <td>147</td>\n", "      <td>9</td>\n", "      <td>[]</td>\n", "      <td>4813.226289</td>\n", "      <td>[5646.08333460157, 1520.51170744395, 58.174734...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        id  image_id  category_id segmentation          area  \\\n", "1001  1002       146            9           []  44165.072527   \n", "1002  1003       147            9           []   4831.610139   \n", "1003  1004       147            9           []   6588.103483   \n", "1004  1005       147            9           []   1998.825917   \n", "1005  1006       147            9           []   4813.226289   \n", "\n", "                                                   bbox  iscrowd  \n", "1001  [186.608370255381, 1722.21474790178, 253.62844...        0  \n", "1002  [4976.42750190927, 2251.39167778104, 63.345822...        0  \n", "1003  [5721.06410351307, 1606.57205155592, 69.809681...        0  \n", "1004  [5729.50143783516, 1523.36714499964, 33.612068...        0  \n", "1005  [5646.08333460157, 1520.51170744395, 58.174734...        0  "]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["mtbleft_annot.head()"]}, {"cell_type": "code", "execution_count": null, "id": "d5d3a7f2-6092-4b37-94fa-670eaa95aebd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "249aff5f-661d-489b-addf-99b46659b006", "metadata": {}, "source": ["### MTb Right"]}, {"cell_type": "code", "execution_count": 36, "id": "fe86e143-ef1b-42a5-b73b-45f39f19ed48", "metadata": {}, "outputs": [{"data": {"text/plain": ["(63, 6)"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "MTbrightposve = pd.read_csv(\"MTbrightimagespos.csv\")\n", "\n", "# Print the resulting filtered DataFrame\n", "MTbrightposve.shape"]}, {"cell_type": "code", "execution_count": 37, "id": "a48292ce-9b9c-4206-a2f4-2ba7f8655cb3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>223</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-072747_M0907060.jpg</td>\n", "      <td>1</td>\n", "      <td>09/09/2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>229</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-073432_M0907262.jpg</td>\n", "      <td>1</td>\n", "      <td>09/09/2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>225</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-073434_M0907263.jpg</td>\n", "      <td>1</td>\n", "      <td>09/09/2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>224</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-073518_M0907285.jpg</td>\n", "      <td>1</td>\n", "      <td>09/09/2022</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>228</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_LJB-R_20220908A-073520_M0907286.jpg</td>\n", "      <td>1</td>\n", "      <td>09/09/2022</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0  223   7008    4672  KES22_LJB-R_20220908A-072747_M0907060.jpg        1   \n", "1  229   7008    4672  KES22_LJB-R_20220908A-073432_M0907262.jpg        1   \n", "2  225   7008    4672  KES22_LJB-R_20220908A-073434_M0907263.jpg        1   \n", "3  224   7008    4672  KES22_LJB-R_20220908A-073518_M0907285.jpg        1   \n", "4  228   7008    4672  KES22_LJB-R_20220908A-073520_M0907286.jpg        1   \n", "\n", "  date_captured  \n", "0    09/09/2022  \n", "1    09/09/2022  \n", "2    09/09/2022  \n", "3    09/09/2022  \n", "4    09/09/2022  "]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["MTbrightposve.head()"]}, {"cell_type": "code", "execution_count": 38, "id": "0fd06ba9-3246-4e04-8116-fd8dbfcd94d3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(408, 7)"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return MTb left annotations \n", "\n", "mtbright_annot = annotationsdf[annotationsdf['image_id'].isin(MTbrightposve['id'])]\n", "mtbright_annot.shape"]}, {"cell_type": "code", "execution_count": 39, "id": "02d49587-910f-46c1-8e3b-87759cbf6054", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1945</th>\n", "      <td>1946</td>\n", "      <td>223</td>\n", "      <td>1</td>\n", "      <td>[]</td>\n", "      <td>2293.252153</td>\n", "      <td>[4064.21285165665, 4324.90368651581, 43.804607...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1946</th>\n", "      <td>1947</td>\n", "      <td>223</td>\n", "      <td>1</td>\n", "      <td>[]</td>\n", "      <td>2297.818110</td>\n", "      <td>[4245.84171200934, 4471.27517985886, 65.172708...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1947</th>\n", "      <td>1948</td>\n", "      <td>223</td>\n", "      <td>1</td>\n", "      <td>[]</td>\n", "      <td>3892.478766</td>\n", "      <td>[3169.71670190919, 4084.51254781372, 66.241113...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1948</th>\n", "      <td>1949</td>\n", "      <td>224</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>578.355934</td>\n", "      <td>[614.599907881389, 181.319349491786, 31.285079...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1949</th>\n", "      <td>1950</td>\n", "      <td>224</td>\n", "      <td>23</td>\n", "      <td>[]</td>\n", "      <td>730.022700</td>\n", "      <td>[880.523083141632, 162.832711639469, 27.018932...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        id  image_id  category_id segmentation         area  \\\n", "1945  1946       223            1           []  2293.252153   \n", "1946  1947       223            1           []  2297.818110   \n", "1947  1948       223            1           []  3892.478766   \n", "1948  1949       224           23           []   578.355934   \n", "1949  1950       224           23           []   730.022700   \n", "\n", "                                                   bbox  iscrowd  \n", "1945  [4064.21285165665, 4324.90368651581, 43.804607...        0  \n", "1946  [4245.84171200934, 4471.27517985886, 65.172708...        0  \n", "1947  [3169.71670190919, 4084.51254781372, 66.241113...        0  \n", "1948  [614.599907881389, 181.319349491786, 31.285079...        0  \n", "1949  [880.523083141632, 162.832711639469, 27.018932...        0  "]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["mtbright_annot.head()"]}, {"cell_type": "code", "execution_count": null, "id": "a5eeda17-c028-4290-aa80-7a39ba8f1d0d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "c153bc6e-39f7-492a-83bf-a56de8b1a847", "metadata": {}, "source": ["### Annotations to COCO file"]}, {"cell_type": "code", "execution_count": 35, "id": "6418fee6-aa56-4a36-a0dc-5458e9f02126", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "coco_dict['info']={\"description\": \"MTb Stratum annotations\", \"version\": \"1.0\", \"year\": 2023, \"contributor\": \"MWS Lab Arusha\", \"date_created\": \"2023-09-08\"}\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = MTbleftposve.to_dict(orient='records')\n", "coco_dict['annotations'] = mtbleft_annot.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'MTbleftannotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "id": "04c4ccdd-d819-403d-baaf-dee4705c85ab", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 40, "id": "23a2d769-19ad-474e-9fd2-a702d81b3823", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "coco_dict['info']={\"description\": \"MTb Stratum annotations\", \"version\": \"1.0\", \"year\": 2023, \"contributor\": \"MWS Lab Arusha\", \"date_created\": \"2023-09-08\"}\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = MTbrightposve.to_dict(orient='records')\n", "coco_dict['annotations'] = mtbright_annot.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'MTbrightannotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "id": "ccd6e132-d8a0-4bfd-86f6-be840592a89f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6164e8ce-2f02-4d88-ab61-f148f9ca5e16", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c2d70828-12c5-45b1-a911-9dc64154f5dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2cf464d8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "066b6085", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6e19bfc6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "af278aa8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fc895799", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "254708b5-3db5-489a-bc35-918c449acee8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16dd420b-4f4d-42de-9aa8-1917a1d7a760", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "043cfaf2-580a-4a72-9871-0e8bbad31a7f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}