{"cells": [{"cell_type": "code", "execution_count": 1, "id": "44beacae", "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd"]}, {"cell_type": "markdown", "id": "ec5fa45a", "metadata": {}, "source": ["1. Get complete list of images in .scout-hidden/images"]}, {"cell_type": "code", "execution_count": 2, "id": "dabbe268", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>File Name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1679213409689.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1679236299624.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1679236302122.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1679236304640.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1679236307183.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           File Name\n", "0  1679213409689.jpg\n", "1  1679236299624.jpg\n", "2  1679236302122.jpg\n", "3  1679236304640.jpg\n", "4  1679236307183.jpg"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["#list of all images from /nas/.scout-hidden/images\n", "imagelistdf = pd.read_csv(\"scoutexports/filelistscout30Oct.csv\")\n", "imagelistdf.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "82c6e2c4", "metadata": {}, "outputs": [{"data": {"text/plain": ["File Name    20707\n", "dtype: int64"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["#total no of images in scout-hidden\n", "imagelistdf.count()"]}, {"cell_type": "code", "execution_count": 4, "id": "9c4a96f0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1679213409689.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1679236299624.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1679236302122.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1679236304640.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1679236307183.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            filename\n", "0  1679213409689.jpg\n", "1  1679236299624.jpg\n", "2  1679236302122.jpg\n", "3  1679236304640.jpg\n", "4  1679236307183.jpg"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["imagelistdf.rename(columns={'File Name':'filename'},inplace=True)\n", "imagelistdf.head()"]}, {"cell_type": "markdown", "id": "6d574461", "metadata": {}, "source": ["2. Export unfiltered CSV of all tasks from Scout\n", "    - Load the database JSON data to filter images that are not associated to tasks\n", "3. Use the CSV as a filter of the directory list to remove images still associated with active Tasks in Scout\n"]}, {"cell_type": "code", "execution_count": 5, "id": "01380143", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>originalFilename</th>\n", "      <th>filename</th>\n", "      <th>taskIds</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'$oid': '64dcd2f2e31598004c4c378d'}</td>\n", "      <td>DSC03452</td>\n", "      <td>1692193522043.jpg</td>\n", "      <td>[653d8463c6072200454e977f]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>{'$oid': '64dcd2f6e31598004c4c378e'}</td>\n", "      <td>DSC03462</td>\n", "      <td>1692193526742.jpg</td>\n", "      <td>[653d8463c6072200454e977f]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>{'$oid': '64dcd2fbe31598004c4c378f'}</td>\n", "      <td>DSC03822</td>\n", "      <td>1692193531544.jpg</td>\n", "      <td>[653d8463c6072200454e977f]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>{'$oid': '64dcd305e31598004c4c3791'}</td>\n", "      <td>DSC03852</td>\n", "      <td>1692193541212.jpg</td>\n", "      <td>[653d8463c6072200454e977f]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>{'$oid': '64dcd30ae31598004c4c3792'}</td>\n", "      <td>DSC03842</td>\n", "      <td>1692193545986.jpg</td>\n", "      <td>[653d8463c6072200454e977f]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                    _id originalFilename           filename  \\\n", "0  {'$oid': '64dcd2f2e31598004c4c378d'}         DSC03452  1692193522043.jpg   \n", "1  {'$oid': '64dcd2f6e31598004c4c378e'}         DSC03462  1692193526742.jpg   \n", "2  {'$oid': '64dcd2fbe31598004c4c378f'}         DSC03822  1692193531544.jpg   \n", "3  {'$oid': '64dcd305e31598004c4c3791'}         DSC03852  1692193541212.jpg   \n", "4  {'$oid': '64dcd30ae31598004c4c3792'}         DSC03842  1692193545986.jpg   \n", "\n", "                      taskIds  \n", "0  [653d8463c6072200454e977f]  \n", "1  [653d8463c6072200454e977f]  \n", "2  [653d8463c6072200454e977f]  \n", "3  [653d8463c6072200454e977f]  \n", "4  [653d8463c6072200454e977f]  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# JSON data\n", "scoutdatadf = pd.read_json(\"scoutexports/imagesdbdata.json\")\n", "\n", "scoutdatadf.head()\n"]}, {"cell_type": "code", "execution_count": 6, "id": "d6e7cdc6", "metadata": {}, "outputs": [{"data": {"text/plain": ["_id                 16508\n", "originalFilename    16508\n", "filename            16508\n", "taskIds             16508\n", "dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["#count of scoutdb images\n", "scoutdatadf.count()"]}, {"cell_type": "code", "execution_count": 119, "id": "dc84d599", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AllImages</td>\n", "      <td>653d8463c6072200454e977f</td>\n", "      <td>DSC03452.jpg</td>\n", "      <td>64dcd2f2e31598004c4c378d</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AllImages</td>\n", "      <td>653d8463c6072200454e977f</td>\n", "      <td>DSC03462.jpg</td>\n", "      <td>64dcd2f6e31598004c4c378e</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AllImages</td>\n", "      <td>653d8463c6072200454e977f</td>\n", "      <td>DSC03822.jpg</td>\n", "      <td>64dcd2fbe31598004c4c378f</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AllImages</td>\n", "      <td>653d8463c6072200454e977f</td>\n", "      <td>DSC03852.jpg</td>\n", "      <td>64dcd305e31598004c4c3791</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AllImages</td>\n", "      <td>653d8463c6072200454e977f</td>\n", "      <td>DSC03842.jpg</td>\n", "      <td>64dcd30ae31598004c4c3792</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Task Name                   Task ID Image Filename  \\\n", "0  AllImages  653d8463c6072200454e977f   DSC03452.jpg   \n", "1  AllImages  653d8463c6072200454e977f   DSC03462.jpg   \n", "2  AllImages  653d8463c6072200454e977f   DSC03822.jpg   \n", "3  AllImages  653d8463c6072200454e977f   DSC03852.jpg   \n", "4  AllImages  653d8463c6072200454e977f   DSC03842.jpg   \n", "\n", "                   Image ID  Image Height  Image Width  WIC Confidence  \\\n", "0  64dcd2f2e31598004c4c378d          6336         9504             NaN   \n", "1  64dcd2f6e31598004c4c378e          6336         9504             NaN   \n", "2  64dcd2fbe31598004c4c378f          6336         9504             NaN   \n", "3  64dcd305e31598004c4c3791          6336         9504             NaN   \n", "4  64dcd30ae31598004c4c3792          6336         9504             NaN   \n", "\n", "   Ground Truth Status Exclusion Side  Inclusion Top X Fraction  \\\n", "0                False          right                       NaN   \n", "1                False          right                       NaN   \n", "2                False          right                       NaN   \n", "3                False          right                       NaN   \n", "4                False          right                       NaN   \n", "\n", "   Inclusion Bottom X Fraction  \n", "0                          NaN  \n", "1                          NaN  \n", "2                          NaN  \n", "3                          NaN  \n", "4                          NaN  "]}, "execution_count": 119, "metadata": {}, "output_type": "execute_result"}], "source": ["#count of images that are duplicates\n", "imgduplicatedf = pd.read_csv(\"scoutexports/Allscout-export-images.csv\")\n", "interfacedf.head()"]}, {"cell_type": "code", "execution_count": 120, "id": "d7b0d954", "metadata": {}, "outputs": [{"data": {"text/plain": ["Task Name                      16508\n", "Task ID                        16508\n", "Image Filename                 16508\n", "Image ID                       16508\n", "Image Height                   16508\n", "Image Width                    16508\n", "WIC Confidence                     0\n", "Ground Truth Status            16508\n", "Exclusion Side                 16508\n", "Inclusion Top X Fraction           0\n", "Inclusion Bottom X Fraction        0\n", "dtype: int64"]}, "execution_count": 120, "metadata": {}, "output_type": "execute_result"}], "source": ["interfacedf.count()"]}, {"cell_type": "code", "execution_count": 139, "id": "6e1e4391", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["filename            20707\n", "deleteimg_img1      20707\n", "_id                 16508\n", "originalFilename    16508\n", "taskIds             16508\n", "deleteimg_img2      16508\n", "dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "      <th>deleteimg_img1</th>\n", "      <th>_id</th>\n", "      <th>originalFilename</th>\n", "      <th>taskIds</th>\n", "      <th>deleteimg_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1679213409689.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1679236299624.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1679236302122.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1679236304640.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1679236307183.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            filename deleteimg_img1  _id originalFilename taskIds  \\\n", "0  1679213409689.jpg       imageid1  NaN              NaN     NaN   \n", "1  1679236299624.jpg       imageid1  NaN              NaN     NaN   \n", "2  1679236302122.jpg       imageid1  NaN              NaN     NaN   \n", "3  1679236304640.jpg       imageid1  NaN              NaN     NaN   \n", "4  1679236307183.jpg       imageid1  NaN              NaN     NaN   \n", "\n", "  deleteimg_img2  \n", "0            NaN  \n", "1            NaN  \n", "2            NaN  \n", "3            NaN  \n", "4            NaN  "]}, "execution_count": 139, "metadata": {}, "output_type": "execute_result"}], "source": ["#compare dataframes to find files to delete\n", "# 20707 (scout hidden) - 16508 (in tasks) = 4,199\n", "\n", "imagelistdf['deleteimg'] ='imageid1'\n", "scoutdatadf['deleteimg'] = 'imageid2'\n", "\n", "merged_df = imagelistdf.merge(scoutdatadf, on='filename',how='left', suffixes=('_img1','_img2'))\n", "print(merged_df.count())\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": 140, "id": "bfe6c018", "metadata": {}, "outputs": [{"data": {"text/plain": ["filename            4199\n", "deleteimg_img1      4199\n", "_id                    0\n", "originalFilename       0\n", "taskIds                0\n", "deleteimg_img2         0\n", "dtype: int64"]}, "execution_count": 140, "metadata": {}, "output_type": "execute_result"}], "source": ["rows_not_in_tasks = merged_df[merged_df['deleteimg_img2'].isna()]\n", "\n", "#rows_not_in_df2 = merged_df[merged_df['ID_df2'].isna()]\n", "\n", "rows_not_in_tasks.count()"]}, {"cell_type": "code", "execution_count": 141, "id": "9409ae67", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "      <th>deleteimg_img1</th>\n", "      <th>_id</th>\n", "      <th>originalFilename</th>\n", "      <th>taskIds</th>\n", "      <th>deleteimg_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1679213409689.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1679236299624.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1679236302122.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1679236304640.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1679236307183.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            filename deleteimg_img1  _id originalFilename taskIds  \\\n", "0  1679213409689.jpg       imageid1  NaN              NaN     NaN   \n", "1  1679236299624.jpg       imageid1  NaN              NaN     NaN   \n", "2  1679236302122.jpg       imageid1  NaN              NaN     NaN   \n", "3  1679236304640.jpg       imageid1  NaN              NaN     NaN   \n", "4  1679236307183.jpg       imageid1  NaN              NaN     NaN   \n", "\n", "  deleteimg_img2  \n", "0            NaN  \n", "1            NaN  \n", "2            NaN  \n", "3            NaN  \n", "4            NaN  "]}, "execution_count": 141, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "rows_not_in_tasks.head()"]}, {"cell_type": "code", "execution_count": 144, "id": "fbb3f104", "metadata": {}, "outputs": [], "source": ["#save the filenames to delete\n", "rows_not_in_tasks['filename'].to_csv(\"filestodelete.csv\",header=None,index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "6d2337aa", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}