{"cells": [{"cell_type": "code", "execution_count": 2, "id": "6aeae49b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 3, "id": "f812a706", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>taskId</th>\n", "      <th>imageId</th>\n", "      <th>queuedImageId</th>\n", "      <th>assigneeType</th>\n", "      <th>assigneeDiplayName</th>\n", "      <th>assignee</th>\n", "      <th>boundingBoxes</th>\n", "      <th>createdAt</th>\n", "      <th>updatedAt</th>\n", "      <th>wicConfidence</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'$oid': '651e61a0899c400045a59f6a'}</td>\n", "      <td>651d5336b5c3e0004614dbf6</td>\n", "      <td>651d385d99f002004b039c41</td>\n", "      <td>651d533796c9b3004d7ed916</td>\n", "      <td>user</td>\n", "      <td>annotator1</td>\n", "      <td>651d517eb5c3e0004614dbf4</td>\n", "      <td>[{'id': 'box-1696489864196', 'x': 290.94226226...</td>\n", "      <td>1.696490e+12</td>\n", "      <td>1.696490e+12</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>{'$oid': '651e672b899c400045a59f6b'}</td>\n", "      <td>651d5336b5c3e0004614dbf6</td>\n", "      <td>651d38d599f002004b039c6d</td>\n", "      <td>651d533796c9b3004d7ed922</td>\n", "      <td>user</td>\n", "      <td>annotator1</td>\n", "      <td>651d517eb5c3e0004614dbf4</td>\n", "      <td>[{'id': 'box-1696491222049', 'x': 4221.6059483...</td>\n", "      <td>1.696491e+12</td>\n", "      <td>1.696491e+12</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>{'$oid': '651e6810899c400045a59f6c'}</td>\n", "      <td>651d5336b5c3e0004614dbf6</td>\n", "      <td>651d38e099f002004b039c71</td>\n", "      <td>651d533796c9b3004d7ed923</td>\n", "      <td>user</td>\n", "      <td>annotator1</td>\n", "      <td>651d517eb5c3e0004614dbf4</td>\n", "      <td>[{'id': 'box-1696491404783', 'x': 764.84767960...</td>\n", "      <td>1.696492e+12</td>\n", "      <td>1.696492e+12</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>{'$oid': '651e686c899c400045a59f6d'}</td>\n", "      <td>651d568bb5c3e0004614dbf8</td>\n", "      <td>651d385d99f002004b039c41</td>\n", "      <td>651d568b96c9b3004d7edadc</td>\n", "      <td>user</td>\n", "      <td>annotator2</td>\n", "      <td>651d51b4b5c3e0004614dbf5</td>\n", "      <td>[{'id': 'box-1696491454641', 'x': 276.65318184...</td>\n", "      <td>1.696492e+12</td>\n", "      <td>1.696492e+12</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>{'$oid': '651e6ee4899c400045a59f6f'}</td>\n", "      <td>651d568bb5c3e0004614dbf8</td>\n", "      <td>651d38d599f002004b039c6d</td>\n", "      <td>651d568b96c9b3004d7edae8</td>\n", "      <td>user</td>\n", "      <td>annotator2</td>\n", "      <td>651d51b4b5c3e0004614dbf5</td>\n", "      <td>[{'id': 'box-1696493189214', 'x': 4226.0605485...</td>\n", "      <td>1.696493e+12</td>\n", "      <td>1.696493e+12</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                    _id                    taskId  \\\n", "0  {'$oid': '651e61a0899c400045a59f6a'}  651d5336b5c3e0004614dbf6   \n", "1  {'$oid': '651e672b899c400045a59f6b'}  651d5336b5c3e0004614dbf6   \n", "2  {'$oid': '651e6810899c400045a59f6c'}  651d5336b5c3e0004614dbf6   \n", "3  {'$oid': '651e686c899c400045a59f6d'}  651d568bb5c3e0004614dbf8   \n", "4  {'$oid': '651e6ee4899c400045a59f6f'}  651d568bb5c3e0004614dbf8   \n", "\n", "                    imageId             queuedImageId assigneeType  \\\n", "0  651d385d99f002004b039c41  651d533796c9b3004d7ed916         user   \n", "1  651d38d599f002004b039c6d  651d533796c9b3004d7ed922         user   \n", "2  651d38e099f002004b039c71  651d533796c9b3004d7ed923         user   \n", "3  651d385d99f002004b039c41  651d568b96c9b3004d7edadc         user   \n", "4  651d38d599f002004b039c6d  651d568b96c9b3004d7edae8         user   \n", "\n", "  assigneeDiplayName                  assignee  \\\n", "0         annotator1  651d517eb5c3e0004614dbf4   \n", "1         annotator1  651d517eb5c3e0004614dbf4   \n", "2         annotator1  651d517eb5c3e0004614dbf4   \n", "3         annotator2  651d51b4b5c3e0004614dbf5   \n", "4         annotator2  651d51b4b5c3e0004614dbf5   \n", "\n", "                                       boundingBoxes     createdAt  \\\n", "0  [{'id': 'box-1696489864196', 'x': 290.94226226...  1.696490e+12   \n", "1  [{'id': 'box-1696491222049', 'x': 4221.6059483...  1.696491e+12   \n", "2  [{'id': 'box-1696491404783', 'x': 764.84767960...  1.696492e+12   \n", "3  [{'id': 'box-1696491454641', 'x': 276.65318184...  1.696492e+12   \n", "4  [{'id': 'box-1696493189214', 'x': 4226.0605485...  1.696493e+12   \n", "\n", "      updatedAt  wicConfidence  \n", "0  1.696490e+12            0.0  \n", "1  1.696491e+12            0.0  \n", "2  1.696492e+12            0.0  \n", "3  1.696492e+12            0.0  \n", "4  1.696493e+12            0.0  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["annotatedf = pd.read_json(\"scoutexports/annosample.json\")\n", "annotatedf.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "8d093846", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'taskdf' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m tasksdf \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_json(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mscoutexports/tasks_scoutdb.json\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m----> 2\u001b[0m \u001b[43mtaskdf\u001b[49m\u001b[38;5;241m.\u001b[39mhead()\n", "\u001b[0;31mNameError\u001b[0m: name 'taskdf' is not defined"]}], "source": ["tasksdf = pd.read_json(\"scoutexports/tasks_scoutdb.json\")\n", "taskdf.head()"]}, {"cell_type": "code", "execution_count": null, "id": "488cc8fb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cd4b5360", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0abc74c8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4a1fe996", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}