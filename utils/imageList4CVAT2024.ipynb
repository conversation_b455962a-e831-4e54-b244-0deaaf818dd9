{"cells": [{"cell_type": "code", "execution_count": 1, "id": "31c2224b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 52, "id": "1fa3085a-0fcd-4e3c-bd45-3f82c746bf3b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>test1</td>\n", "      <td>66b07afa1d061e004564a9de</td>\n", "      <td>DSC05576.jpg</td>\n", "      <td>66ac8f666a0d19004c9db0cf</td>\n", "      <td>6325.453243</td>\n", "      <td>924.657426</td>\n", "      <td>-54.391613</td>\n", "      <td>-15.540461</td>\n", "      <td>redle<PERSON>we</td>\n", "      <td>NaN</td>\n", "      <td>wrichard</td>\n", "      <td>1722850006408</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>test1</td>\n", "      <td>66b07afa1d061e004564a9de</td>\n", "      <td>DSC05576.jpg</td>\n", "      <td>66ac8f666a0d19004c9db0cf</td>\n", "      <td>6391.500202</td>\n", "      <td>934.370214</td>\n", "      <td>18.454297</td>\n", "      <td>41.764989</td>\n", "      <td>redle<PERSON>we</td>\n", "      <td>NaN</td>\n", "      <td>wrichard</td>\n", "      <td>1722850006408</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>test1</td>\n", "      <td>66b07afa1d061e004564a9de</td>\n", "      <td>DSC05576.jpg</td>\n", "      <td>66ac8f666a0d19004c9db0cf</td>\n", "      <td>6315.740455</td>\n", "      <td>786.735835</td>\n", "      <td>54.391613</td>\n", "      <td>36.908595</td>\n", "      <td>redle<PERSON>we</td>\n", "      <td>NaN</td>\n", "      <td>wrichard</td>\n", "      <td>1722850006408</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>test1</td>\n", "      <td>66b07afa1d061e004564a9de</td>\n", "      <td>DSC05576.jpg</td>\n", "      <td>66ac8f666a0d19004c9db0cf</td>\n", "      <td>5904.052918</td>\n", "      <td>969.513214</td>\n", "      <td>67.989542</td>\n", "      <td>29.138375</td>\n", "      <td>redle<PERSON>we</td>\n", "      <td>NaN</td>\n", "      <td>wrichard</td>\n", "      <td>1722850006408</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>test1</td>\n", "      <td>66b07afa1d061e004564a9de</td>\n", "      <td>DSC05576.jpg</td>\n", "      <td>66ac8f666a0d19004c9db0cf</td>\n", "      <td>6283.370757</td>\n", "      <td>876.348284</td>\n", "      <td>50.418221</td>\n", "      <td>19.901929</td>\n", "      <td>redle<PERSON>we</td>\n", "      <td>NaN</td>\n", "      <td>wrichard</td>\n", "      <td>1722850006408</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Task Name                   Task ID Image Filename  \\\n", "0     test1  66b07afa1d061e004564a9de   DSC05576.jpg   \n", "1     test1  66b07afa1d061e004564a9de   DSC05576.jpg   \n", "2     test1  66b07afa1d061e004564a9de   DSC05576.jpg   \n", "3     test1  66b07afa1d061e004564a9de   DSC05576.jpg   \n", "4     test1  66b07afa1d061e004564a9de   DSC05576.jpg   \n", "\n", "                   Image ID        Box X       Box Y      Box W      Box H  \\\n", "0  66ac8f666a0d19004c9db0cf  6325.453243  924.657426 -54.391613 -15.540461   \n", "1  66ac8f666a0d19004c9db0cf  6391.500202  934.370214  18.454297  41.764989   \n", "2  66ac8f666a0d19004c9db0cf  6315.740455  786.735835  54.391613  36.908595   \n", "3  66ac8f666a0d19004c9db0cf  5904.052918  969.513214  67.989542  29.138375   \n", "4  66ac8f666a0d19004c9db0cf  6283.370757  876.348284  50.418221  19.901929   \n", "\n", "       Label  Label Confidence  Assignee      Timestamp  Is Ground Truth  \\\n", "0  redlechwe               NaN  wrichard  1722850006408            False   \n", "1  redlechwe               NaN  wrichard  1722850006408            False   \n", "2  redlechwe               NaN  wrichard  1722850006408            False   \n", "3  redlechwe               NaN  wrichard  1722850006408            False   \n", "4  redlechwe               NaN  wrichard  1722850006408            False   \n", "\n", "   Excluded By Line  \n", "0               NaN  \n", "1               NaN  \n", "2               NaN  \n", "3               NaN  \n", "4               NaN  "]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["#images and bboxs\n", "imgbboxdf = pd.read_csv(\"Pregroundtruth/scout-export-annotations-290824.csv\")\n", "imgbboxdf.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "bd67f09f-e812-40ef-b5ea-22f98d7b7772", "metadata": {}, "outputs": [{"data": {"text/plain": ["Task Name            object\n", "Task ID              object\n", "Image Filename       object\n", "Image ID             object\n", "Box X               float64\n", "Box Y               float64\n", "Box W               float64\n", "Box H               float64\n", "Label                object\n", "Label Confidence    float64\n", "Assignee             object\n", "Timestamp             int64\n", "Is Ground Truth        bool\n", "Excluded By Line    float64\n", "dtype: object"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["imgbboxdf.dtypes"]}, {"cell_type": "code", "execution_count": 53, "id": "23761d35-fa70-420b-82bd-938a75c2d8f6", "metadata": {}, "outputs": [{"data": {"text/plain": ["108"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["imgbboxdf['Task Name'].nunique()"]}, {"cell_type": "code", "execution_count": 57, "id": "33243573-71b6-413e-891f-824e9db53e2c", "metadata": {}, "outputs": [], "source": ["tasksunique = imgbboxdf['Task Name'].unique()"]}, {"cell_type": "code", "execution_count": 58, "id": "7e42db9c-9f57-499e-909e-b148801b3ca1", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['test1', 'test2', 'test5', 'D01-L', 'D02-L', 'D03-L', 'D04-L',\n", "       'D05-L', 'D06-L', 'D07-L_1', 'D07-L_2', 'D08-L_1', 'D08-L_2',\n", "       'D01-R', 'D02-R', 'D03-R', 'D04-R', 'D05-R', 'D06-R', 'D07-R_1',\n", "       'D07-R_2', 'D08-R_1', 'D08-R_2', 'L200_240525', 'L400_240525',\n", "       'L600_240525', 'L800_240525', 'L1000_240525', 'L1200_240525',\n", "       'L1400_240525', 'L1600_240525', 'L1800_240525', 'L2000_240525',\n", "       'L2200_240525', 'L2400_240525', 'R200_240525', 'R400_240525',\n", "       'R600_240525', 'R800_240525', 'R1000_240525', 'R1200_240525',\n", "       'R1400_240525', 'R1600_240525', 'R1800_240525', 'R2000_240525',\n", "       'R2200_240525', 'R2400_240525', 'L2600_240525', 'L2800_240525',\n", "       'L3000_240525', 'L3200_240525', 'L3400_240525', 'L3600_240525',\n", "       'L3842_240525', 'R2600_240525', 'R2800_240525', 'R3000_240525',\n", "       'R3200_240525', 'R3400_240525', 'R3600_240525', 'R3809_240525',\n", "       'Sango-test_20240813-1131', 'Sango-test_3c_0.8-1.0', 'D03_L2',\n", "       'L200_240528', 'L400_240528', 'L600_240528', 'L800_240528',\n", "       'L1000_240528', 'L1200_240528', 'L1400_240528', 'L1600_240528',\n", "       'L1800_240528', 'L1921_240528', 'R200_240528', 'R400_240528',\n", "       'R600_240528', 'R800_240528', 'R1000_240528', 'R1200_240528',\n", "       'R1400_240528', 'R1596_240528', 'L200_240527', 'L400_240527',\n", "       'L600_240527', 'L800_240527', 'L1000_240527', 'L1200_240527',\n", "       'L1400_240527', 'L1600_240527', 'L1800_240527', 'L2000_240527',\n", "       'L2200_240527', 'L2400_240527', 'L2599_240527', 'R200_240527',\n", "       'R400_240527', 'R600_240527', 'R800_240527', 'R1000_240527',\n", "       'R1400_240527', 'R1600_240527', 'R1800_240527', 'R2000_240527',\n", "       'R2200_240527', 'R2400_240527', 'R2595_240527', 'Example-filter'],\n", "      dtype=object)"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["tasksunique"]}, {"cell_type": "code", "execution_count": 59, "id": "3b4db66c-0272-4063-be99-bbac5bc3ca95", "metadata": {}, "outputs": [], "source": ["#lists of test tasks to delete\n", "tasksdelete = ['test1', 'test2', 'test5', 'Sango-test_20240813-1131', 'Sango-test_3c_0.8-1.0','D03_L2','Example-filter']"]}, {"cell_type": "code", "execution_count": 60, "id": "38abdd44-0a01-4ff8-8267-7997026f5e71", "metadata": {}, "outputs": [{"data": {"text/plain": ["['test1',\n", " 'test2',\n", " 'test5',\n", " 'Sango-test_20240813-1131',\n", " 'Sango-test_3c_0.8-1.0',\n", " 'D03_L2',\n", " 'Example-filter']"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["tasksdelete"]}, {"cell_type": "code", "execution_count": 61, "id": "e66b63e1-fd22-4098-832a-827eacb5a42b", "metadata": {}, "outputs": [], "source": ["#remove the unwanted tasks on the annotations list\n", "imgbbox_filtered = imgbboxdf[~imgbboxdf['Task Name'].isin(tasksdelete)]"]}, {"cell_type": "code", "execution_count": 406, "id": "b06602c6-02ec-4064-b729-00d952f60eaf", "metadata": {}, "outputs": [{"data": {"text/plain": ["Task Name             101\n", "Task ID               101\n", "Image Filename       2168\n", "Image ID             2168\n", "Box X               15051\n", "Box Y               14823\n", "Box W                6212\n", "Box H                5079\n", "Label                  37\n", "Label Confidence        0\n", "Assignee                7\n", "Timestamp            2172\n", "Is Ground Truth         1\n", "Excluded By Line        0\n", "dtype: int64"]}, "execution_count": 406, "metadata": {}, "output_type": "execute_result"}], "source": ["imgbbox_filtered.nunique()"]}, {"cell_type": "code", "execution_count": 407, "id": "a17c80dd", "metadata": {}, "outputs": [], "source": ["# Get the list of positive images \n", "uniqueimagbbox = imgbbox_filtered['Image Filename'].unique()\n", "uniqueimagbboxdf = pd.DataFrame(uniqueimagbbox, columns=['PositiveImage'])\n", "\n", "posimagesdf=uniqueimagbboxdf.sort_values(by=[\"PositiveImage\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 23, "id": "4c5cf2ed-6fc0-4919-a33d-d51b80712085", "metadata": {}, "outputs": [], "source": ["posimagesdf.to_csv(\"PositiveImages2024.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 29, "id": "2478775f-1dba-400b-9ea7-bb62443affc4", "metadata": {}, "outputs": [], "source": ["# Get the list of tasks with positive images\n", "uniquetasks = imgbbox_filtered['Task Name'].unique()\n", "uniquetasksdf = pd.DataFrame(uniquetasks, columns=['TaskName'])\n", "uniquetasksdf.to_csv(\"TaskNameCVAT.csv\", index=False)                         "]}, {"cell_type": "code", "execution_count": 36, "id": "bbd3d45b-d99e-4cf7-b131-b06daf491711", "metadata": {}, "outputs": [], "source": ["# Group by 'Task Name' and count the number of positive images for each unique task\n", "# 101 tasks and 2168 positive images\n", "\n", "image_counts = imgbbox_filtered.groupby('Task Name')['Image Filename'].nunique().reset_index(name='No Images')\n", "image_counts.to_csv(\"NoImagesByTask.csv\",index=False)\n"]}, {"cell_type": "code", "execution_count": 28, "id": "bf3ca93a-55dc-4938-984c-8b13cc2d270f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["       Task Name  No Images\n", "0          D01-L          5\n", "1          D01-R          2\n", "2          D02-L         16\n", "3          D02-R          7\n", "4          D03-L          8\n", "..           ...        ...\n", "96   R600_240527          3\n", "97   R600_240528         13\n", "98   R800_240525         52\n", "99   R800_240527          6\n", "100  R800_240528          5\n", "\n", "[101 rows x 2 columns]\n"]}], "source": ["print(image_counts)"]}, {"cell_type": "code", "execution_count": 64, "id": "24bd479f-44a8-4b17-98aa-78ec0bfeabb3", "metadata": {}, "outputs": [], "source": ["# Get the positive filenames grouped by 'Task Name' \n", "# 101 tasks and 2168 positive images\n", "image_unique = imgbbox_filtered.groupby('Task Name')['Image Filename'].unique().reset_index(name=\"Image Filename\")\n", "#image_unique.to_csv(\"PosImagesByTask.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 65, "id": "310994e4-d61f-494b-a8be-b4c013ddc12a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Image Filename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>D01-L</td>\n", "      <td>[ELE-L_20240524B_070342_DSC05759.jpg, ELE-L_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>D01-R</td>\n", "      <td>[ELE-R_20240524B_070328_DSC05880.jpg, ELE-R_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>D02-L</td>\n", "      <td>[ELE-L_20240524B_070521_DSC05808.jpg, ELE-L_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>D02-R</td>\n", "      <td>[ELE-R_20240524B_070633_DSC05972.jpg, ELE-R_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>D03-L</td>\n", "      <td>[ELE-L_20240524B_070936_DSC05927.jpg, ELE-L_20...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Task Name                                     Image Filename\n", "0     D01-L  [ELE-L_20240524B_070342_DSC05759.jpg, ELE-L_20...\n", "1     D01-R  [ELE-R_20240524B_070328_DSC05880.jpg, ELE-R_20...\n", "2     D02-L  [ELE-L_20240524B_070521_DSC05808.jpg, ELE-L_20...\n", "3     D02-R  [ELE-R_20240524B_070633_DSC05972.jpg, ELE-R_20...\n", "4     D03-L  [ELE-L_20240524B_070936_DSC05927.jpg, ELE-L_20..."]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["image_unique.head()"]}, {"cell_type": "code", "execution_count": 66, "id": "5bc7156a-25e4-4b29-b05d-5f0c9b83b5e2", "metadata": {}, "outputs": [{"data": {"text/plain": ["(101, 2)"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["image_unique.shape"]}, {"cell_type": "code", "execution_count": 67, "id": "694f435a-0cfa-42cd-9915-a2a701013d0a", "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["len(image_unique[\"Image Filename\"][0])"]}, {"cell_type": "code", "execution_count": 48, "id": "99ffb686-4151-4fac-83f9-9ae7e49f97dd", "metadata": {}, "outputs": [], "source": ["image_unique.to_csv(\"PosImagesByTask.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 51, "id": "38daea25-556e-473c-886f-50804c5811b9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5\n"]}], "source": ["for idx, row in image_unique.iterrows():\n", "    if row['Task Name'] == 'D01-L':\n", "        print(len(row['Image Filename']))\n", "        #for name in row['PosImages']:\n", "         #   print(name)"]}, {"cell_type": "code", "execution_count": 53, "id": "ace3b735-fcb9-40c7-8eef-aa18bbf2e83b", "metadata": {}, "outputs": [], "source": ["image_unique.to_csv(\"PosImagesByTask.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 31, "id": "7dd88be1-cfd5-45a2-97fe-49962d7e752d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Image Filename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>D01-L</td>\n", "      <td>[ELE-L_20240524B_070342_DSC05759.jpg, ELE-L_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>D01-R</td>\n", "      <td>[ELE-R_20240524B_070328_DSC05880.jpg, ELE-R_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>D02-L</td>\n", "      <td>[ELE-L_20240524B_070521_DSC05808.jpg, ELE-L_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>D02-R</td>\n", "      <td>[ELE-R_20240524B_070633_DSC05972.jpg, ELE-R_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>D03-L</td>\n", "      <td>[ELE-L_20240524B_070936_DSC05927.jpg, ELE-L_20...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Task Name                                     Image Filename\n", "0     D01-L  [ELE-L_20240524B_070342_DSC05759.jpg, ELE-L_20...\n", "1     D01-R  [ELE-R_20240524B_070328_DSC05880.jpg, ELE-R_20...\n", "2     D02-L  [ELE-L_20240524B_070521_DSC05808.jpg, ELE-L_20...\n", "3     D02-R  [ELE-R_20240524B_070633_DSC05972.jpg, ELE-R_20...\n", "4     D03-L  [ELE-L_20240524B_070936_DSC05927.jpg, ELE-L_20..."]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["image_names.head()"]}, {"cell_type": "markdown", "id": "c325fd95-1f90-4763-bad4-595fd9caba1b", "metadata": {}, "source": ["Annotations - Bounding Boxes"]}, {"cell_type": "code", "execution_count": 44, "id": "1236bf2d-c69b-4751-88a0-0bb6e94f8789", "metadata": {}, "outputs": [], "source": ["# add annotators of tasks\n", "\n", "annotators_task = imgbbox_filtered.groupby('Task Name')['Assignee'].unique().reset_index(name='annotators')\n", "annotators_task.to_csv(\"AnnotatorsByTask.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 43, "id": "df20e1cb-390d-4445-9752-292d6bc8d473", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>annotators</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>D01-L</td>\n", "      <td>[uminja]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>D01-R</td>\n", "      <td>[wrichard]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>D02-L</td>\n", "      <td>[uminja]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>D02-R</td>\n", "      <td>[wrichard]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>D03-L</td>\n", "      <td>[a<PERSON><PERSON>]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Task Name  annotators\n", "0     D01-<PERSON>    [um<PERSON><PERSON>]\n", "1     D01-R  [wrichard]\n", "2     D02-<PERSON>    [um<PERSON><PERSON>]\n", "3     D02-R  [wrichard]\n", "4     D03-L  [a<PERSON><PERSON>]"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["annotators_task.head()"]}, {"cell_type": "code", "execution_count": 45, "id": "c0536f6d-66cb-48c9-bc27-451f0497c9b2", "metadata": {}, "outputs": [{"data": {"text/plain": ["37"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["imgbbox_filtered['Label'].nunique()"]}, {"cell_type": "code", "execution_count": 46, "id": "612476c2-7753-4ce5-9599-ac3a9734cbae", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['warthog', 'unknown antelope', 'roof_mabati', 'unknown animal',\n", "       'elephant', 'crocodile', 'redlechwe', 'waterbuck', 'baboon',\n", "       'sitatunga', 'zebra', 'white_bones', 'buffalo', 'hippo', 'giraffe',\n", "       'vehicle', 'ec4', 'lion', 'rhino', 'canoe', 'bushbuck', 'puku',\n", "       'hyena', 'kudu', 'topi', 'car', 'human', 'wild dog', 'impala',\n", "       'reedbuck', 'wildebeest', 'sable', 'ec2', 'eland', 'steenbok',\n", "       'roan', 'bushpig'], dtype=object)"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["imgbbox_filtered['Label'].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "754050f5-ac92-470b-b65f-c205d7292f70", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 74, "id": "92bd1a6f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Date Exif</th>\n", "      <th>GPS Lat Exif</th>\n", "      <th>GPS Long Exif</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>test1</td>\n", "      <td>66b07afa1d061e004564a9de</td>\n", "      <td>DSC05651.jpg</td>\n", "      <td>5/24/2024</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>66ac8f3b6a0d19004c9db0be</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>test1</td>\n", "      <td>66b07afa1d061e004564a9de</td>\n", "      <td>DSC05414.jpg</td>\n", "      <td>5/24/2024</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>66ac8f3d6a0d19004c9db0bf</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>test1</td>\n", "      <td>66b07afa1d061e004564a9de</td>\n", "      <td>ELR00040.jpg</td>\n", "      <td>5/28/2024</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>66ac8f406a0d19004c9db0c0</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>test1</td>\n", "      <td>66b07afa1d061e004564a9de</td>\n", "      <td>ELL06086.jpg</td>\n", "      <td>5/28/2024</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>66ac8f426a0d19004c9db0c1</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>test1</td>\n", "      <td>66b07afa1d061e004564a9de</td>\n", "      <td>ELL06097.jpg</td>\n", "      <td>5/28/2024</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>66ac8f456a0d19004c9db0c2</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Task Name                   Task ID Image Filename  Date Exif  GPS Lat Exif  \\\n", "0     test1  66b07afa1d061e004564a9de   DSC05651.jpg  5/24/2024           NaN   \n", "1     test1  66b07afa1d061e004564a9de   DSC05414.jpg  5/24/2024           NaN   \n", "2     test1  66b07afa1d061e004564a9de   ELR00040.jpg  5/28/2024           NaN   \n", "3     test1  66b07afa1d061e004564a9de   ELL06086.jpg  5/28/2024           NaN   \n", "4     test1  66b07afa1d061e004564a9de   ELL06097.jpg  5/28/2024           NaN   \n", "\n", "   GPS Long Exif                  Image ID  Image Height  Image Width  \\\n", "0            NaN  66ac8f3b6a0d19004c9db0be          4672         7008   \n", "1            NaN  66ac8f3d6a0d19004c9db0bf          4672         7008   \n", "2            NaN  66ac8f406a0d19004c9db0c0          4672         7008   \n", "3            NaN  66ac8f426a0d19004c9db0c1          4672         7008   \n", "4            NaN  66ac8f456a0d19004c9db0c2          4672         7008   \n", "\n", "   WIC Confidence  Ground Truth Status Exclusion Side  \\\n", "0             NaN                 True          right   \n", "1             NaN                False          right   \n", "2             NaN                False          right   \n", "3             NaN                False          right   \n", "4             NaN                False          right   \n", "\n", "   Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0                       NaN                          NaN  \n", "1                       NaN                          NaN  \n", "2                       NaN                          NaN  \n", "3                       NaN                          NaN  \n", "4                       NaN                          NaN  "]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["#images and tasks\n", "tasksdf = pd.read_csv(\"Pregroundtruth/scout-export-images-290824.csv\")\n", "tasksdf.head()"]}, {"cell_type": "code", "execution_count": 75, "id": "0d80c2af-fb05-4862-99b4-42d3c2223976", "metadata": {}, "outputs": [{"data": {"text/plain": ["112"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["tasksdf['Task Name'].nunique()"]}, {"cell_type": "code", "execution_count": 76, "id": "534d8c9b-656f-4ef8-9f62-a0257c5cff4f", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['test1', 'test2', 'test3', 'test5', 'test6', 'autotask', 'D01-L',\n", "       'D02-L', 'D03-L', 'D04-L', 'D05-L', 'D06-L', 'D07-L_1', 'D07-L_2',\n", "       'D08-L_1', 'D08-L_2', 'D01-R', 'D02-R', 'D03-R', 'D04-R', 'D05-R',\n", "       'D06-R', 'D07-R_1', 'D07-R_2', 'D08-R_1', 'D08-R_2', 'L200_240525',\n", "       'L400_240525', 'L600_240525', 'L800_240525', 'L1000_240525',\n", "       'L1200_240525', 'L1400_240525', 'L1600_240525', 'L1800_240525',\n", "       'L2000_240525', 'L2200_240525', 'L2400_240525', 'R200_240525',\n", "       'R400_240525', 'R600_240525', 'R800_240525', 'R1000_240525',\n", "       'R1200_240525', 'R1400_240525', 'R1600_240525', 'R1800_240525',\n", "       'R2000_240525', 'R2200_240525', 'R2400_240525', 'L2600_240525',\n", "       'L2800_240525', 'L3000_240525', 'L3200_240525', 'L3400_240525',\n", "       'L3600_240525', 'L3842_240525', 'R2600_240525', 'R2800_240525',\n", "       'R3000_240525', 'R3200_240525', 'R3400_240525', 'R3600_240525',\n", "       'R3809_240525', 'Sango-test_20240813-1131',\n", "       'Sango-test_3c_0.8-1.0', 'D03_L2', 'L200_240528', 'L400_240528',\n", "       'L600_240528', 'L800_240528', 'L1000_240528', 'L1200_240528',\n", "       'L1400_240528', 'L1600_240528', 'L1800_240528', 'L1921_240528',\n", "       'R200_240528', 'R400_240528', 'R600_240528', 'R800_240528',\n", "       'R1000_240528', 'R1200_240528', 'R1400_240528', 'R1596_240528',\n", "       'L200_240527', 'L400_240527', 'L600_240527', 'L800_240527',\n", "       'L1000_240527', 'L1200_240527', 'L1400_240527', 'L1600_240527',\n", "       'L1800_240527', 'L2000_240527', 'L2200_240527', 'L2400_240527',\n", "       'L2599_240527', 'R200_240527', 'R400_240527', 'R600_240527',\n", "       'R800_240527', 'R1000_240527', 'R1200_240527', 'R1400_240527',\n", "       'R1600_240527', 'R1800_240527', 'R2000_240527', 'R2200_240527',\n", "       'R2400_240527', 'R2595_240527', 'Example-filter'], dtype=object)"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["tasksdf['Task Name'].unique()"]}, {"cell_type": "code", "execution_count": 77, "id": "0d736f71-7dab-4340-a49a-cca4aa7ce458", "metadata": {}, "outputs": [], "source": ["taskimagesdelete =['test1', 'test2', 'test3', 'test5', 'test6', 'autotask','Sango-test_20240813-1131',\n", "       'Sango-test_3c_0.8-1.0', 'D03_L2','Example-filter',\"R1200_240527\"]"]}, {"cell_type": "code", "execution_count": 78, "id": "17d1bf16-ccc9-4907-beaa-27a7f8d18012", "metadata": {}, "outputs": [], "source": ["#remove the unwanted tasks on the images(+ve and -ve) list\n", "\n", "imglist_filtered = tasksdf[~tasksdf['Task Name'].isin(taskimagesdelete)]"]}, {"cell_type": "code", "execution_count": 79, "id": "25eceb55-4900-4363-b156-a85ca342760d", "metadata": {}, "outputs": [{"data": {"text/plain": ["101"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["imglist_filtered['Task Name'].nunique()"]}, {"cell_type": "code", "execution_count": 80, "id": "714d1b6f-0209-4227-9a3b-afb4c4d5d223", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Date Exif</th>\n", "      <th>GPS Lat Exif</th>\n", "      <th>GPS Long Exif</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>120</th>\n", "      <td>D01-L</td>\n", "      <td>66b0d3cf0d58830044008ab0</td>\n", "      <td>ELE-L_20240524B_070220_DSC05718.jpg</td>\n", "      <td>5/24/2024</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>66b0a74c363376004b3689aa</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>121</th>\n", "      <td>D01-L</td>\n", "      <td>66b0d3cf0d58830044008ab0</td>\n", "      <td>ELE-L_20240524B_070228_DSC05722.jpg</td>\n", "      <td>5/24/2024</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>66b0a74f363376004b3689ab</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>122</th>\n", "      <td>D01-L</td>\n", "      <td>66b0d3cf0d58830044008ab0</td>\n", "      <td>ELE-L_20240524B_070232_DSC05724.jpg</td>\n", "      <td>5/24/2024</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>66b0a752363376004b3689ac</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>123</th>\n", "      <td>D01-L</td>\n", "      <td>66b0d3cf0d58830044008ab0</td>\n", "      <td>ELE-L_20240524B_070256_DSC05736.jpg</td>\n", "      <td>5/24/2024</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>66b0a755363376004b3689ad</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>124</th>\n", "      <td>D01-L</td>\n", "      <td>66b0d3cf0d58830044008ab0</td>\n", "      <td>ELE-L_20240524B_070238_DSC05727.jpg</td>\n", "      <td>5/24/2024</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>66b0a75a363376004b3689af</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Task Name                   Task ID                       Image Filename  \\\n", "120     D01-L  66b0d3cf0d58830044008ab0  ELE-L_20240524B_070220_DSC05718.jpg   \n", "121     D01-L  66b0d3cf0d58830044008ab0  ELE-L_20240524B_070228_DSC05722.jpg   \n", "122     D01-L  66b0d3cf0d58830044008ab0  ELE-L_20240524B_070232_DSC05724.jpg   \n", "123     D01-L  66b0d3cf0d58830044008ab0  ELE-L_20240524B_070256_DSC05736.jpg   \n", "124     D01-L  66b0d3cf0d58830044008ab0  ELE-L_20240524B_070238_DSC05727.jpg   \n", "\n", "     Date Exif  GPS Lat Exif  GPS Long Exif                  Image ID  \\\n", "120  5/24/2024           NaN            NaN  66b0a74c363376004b3689aa   \n", "121  5/24/2024           NaN            NaN  66b0a74f363376004b3689ab   \n", "122  5/24/2024           NaN            NaN  66b0a752363376004b3689ac   \n", "123  5/24/2024           NaN            NaN  66b0a755363376004b3689ad   \n", "124  5/24/2024           NaN            NaN  66b0a75a363376004b3689af   \n", "\n", "     Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "120          4672         7008             NaN                False   \n", "121          4672         7008             NaN                False   \n", "122          4672         7008             NaN                False   \n", "123          4672         7008             NaN                False   \n", "124          4672         7008             NaN                False   \n", "\n", "    Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "120          right                       NaN                          NaN  \n", "121          right                       NaN                          NaN  \n", "122          right                       NaN                          NaN  \n", "123          right                       NaN                          NaN  \n", "124          right                       NaN                          NaN  "]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["imglist_filtered.head()"]}, {"cell_type": "code", "execution_count": 81, "id": "09392081-3ace-470f-80de-293b53353b33", "metadata": {}, "outputs": [], "source": ["# Get the filenames of all images grouped by 'Task Name' \n", "allimage_names = imglist_filtered.groupby('Task Name')['Image Filename'].apply(list).reset_index()\n", "#allimage_names.to_csv(\"AllImagesByTask.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 82, "id": "cc4793a8-dd3d-4a88-a8e6-df379a4b5545", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Image Filename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>D01-L</td>\n", "      <td>[ELE-L_20240524B_070220_DSC05718.jpg, ELE-L_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>D01-R</td>\n", "      <td>[ELE-R_20240524B_070220_DSC05846.jpg, ELE-R_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>D02-L</td>\n", "      <td>[ELE-L_20240524B_070440_DSC05788.jpg, ELE-L_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>D02-R</td>\n", "      <td>[ELE-R_20240524B_070633_DSC05972.jpg, ELE-R_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>D03-L</td>\n", "      <td>[ELE-L_20240524B_071001_DSC05939.jpg, ELE-L_20...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Task Name                                     Image Filename\n", "0     D01-L  [ELE-L_20240524B_070220_DSC05718.jpg, ELE-L_20...\n", "1     D01-R  [ELE-R_20240524B_070220_DSC05846.jpg, ELE-R_20...\n", "2     D02-L  [ELE-L_20240524B_070440_DSC05788.jpg, ELE-L_20...\n", "3     D02-R  [ELE-R_20240524B_070633_DSC05972.jpg, ELE-R_20...\n", "4     D03-L  [ELE-L_20240524B_071001_DSC05939.jpg, ELE-L_20..."]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["allimage_names.head() "]}, {"cell_type": "code", "execution_count": 58, "id": "7cf3c802-45ff-4c22-bb77-356cb079008b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["47\n"]}], "source": ["for idx, row in allimage_names.iterrows():\n", "    if row['Task Name'] == 'D01-L':\n", "        print(len(row['Image Filename']))\n", "        #for name in row['Images']:\n", "         #   print(name)"]}, {"cell_type": "code", "execution_count": 3, "id": "a9a17bed-7473-4de3-8cf1-9ef1c2fd65cb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Image Filename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>D01-L</td>\n", "      <td>['ELE-L_20240524B_070220_DSC05718.jpg', 'ELE-L...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>D01-R</td>\n", "      <td>['ELE-R_20240524B_070220_DSC05846.jpg', 'ELE-R...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>D02-L</td>\n", "      <td>['ELE-L_20240524B_070440_DSC05788.jpg', 'ELE-L...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>D02-R</td>\n", "      <td>['ELE-R_20240524B_070633_DSC05972.jpg', 'ELE-R...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>D03-L</td>\n", "      <td>['ELE-L_20240524B_071001_DSC05939.jpg', 'ELE-L...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Task Name                                     Image Filename\n", "0     D01-L  ['ELE-L_20240524B_070220_DSC05718.jpg', 'ELE-L...\n", "1     D01-R  ['ELE-R_20240524B_070220_DSC05846.jpg', 'ELE-R...\n", "2     D02-L  ['ELE-L_20240524B_070440_DSC05788.jpg', 'ELE-L...\n", "3     D02-R  ['ELE-R_20240524B_070633_DSC05972.jpg', 'ELE-R...\n", "4     D03-L  ['ELE-L_20240524B_071001_DSC05939.jpg', 'ELE-L..."]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["allimage_names = pd.read_csv(\"AllImagesByTask.csv\")\n", "allimage_names.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "d2f6177b-3c88-43d7-9a8c-3630b2467cf5", "metadata": {}, "outputs": [{"data": {"text/plain": ["(102, 2)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["allimage_names.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "46b887a2-c426-44f2-bffa-b12651532ba7", "metadata": {}, "outputs": [], "source": ["allimgfilter = allimage_names[allimage_names['Task Name']!= \"R1200_240527\"]\n"]}, {"cell_type": "code", "execution_count": 6, "id": "5d0da2fb-dfa5-4596-9bc9-7ab2b366d325", "metadata": {}, "outputs": [{"data": {"text/plain": ["(101, 2)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["allimgfilter.shape"]}, {"cell_type": "code", "execution_count": 56, "id": "9747ed43-ab66-4376-a46e-4466212f133c", "metadata": {}, "outputs": [{"data": {"text/plain": ["1833"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["len(allimgfilter[\"Image Filename\"][0])"]}, {"cell_type": "code", "execution_count": 7, "id": "c3b0b6f6-8822-4e6f-a9ad-89eea57576fa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Image Filename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>D01-L</td>\n", "      <td>['ELE-L_20240524B_070342_DSC05759.jpg'\\n 'ELE-...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>D01-R</td>\n", "      <td>['ELE-R_20240524B_070328_DSC05880.jpg'\\n 'ELE-...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>D02-L</td>\n", "      <td>['ELE-L_20240524B_070521_DSC05808.jpg'\\n 'ELE-...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>D02-R</td>\n", "      <td>['ELE-R_20240524B_070633_DSC05972.jpg'\\n 'ELE-...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>D03-L</td>\n", "      <td>['ELE-L_20240524B_070936_DSC05927.jpg'\\n 'ELE-...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Task Name                                     Image Filename\n", "0     D01-L  ['ELE-L_20240524B_070342_DSC05759.jpg'\\n 'ELE-...\n", "1     D01-R  ['ELE-R_20240524B_070328_DSC05880.jpg'\\n 'ELE-...\n", "2     D02-L  ['ELE-L_20240524B_070521_DSC05808.jpg'\\n 'ELE-...\n", "3     D02-R  ['ELE-R_20240524B_070633_DSC05972.jpg'\\n 'ELE-...\n", "4     D03-L  ['ELE-L_20240524B_070936_DSC05927.jpg'\\n 'ELE-..."]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["posimagesbytask = pd.read_csv(\"PosImagesByTask.csv\")\n", "posimagesbytask.head()\n", "#print(posimagesbytask.shape)"]}, {"cell_type": "code", "execution_count": 50, "id": "afa3e4df-fe01-4809-9a74-638cf34dd57b", "metadata": {}, "outputs": [{"data": {"text/plain": ["195"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["len(posimagesbytask[\"Image Filename\"][0])"]}, {"cell_type": "code", "execution_count": 65, "id": "777f7de4-ebff-4989-85dd-61afa103b715", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Image Filename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>D01-L</td>\n", "      <td>[ELE-L_20240524B_070342_DSC05759.jpg, ELE-L_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>D01-R</td>\n", "      <td>[ELE-R_20240524B_070328_DSC05880.jpg, ELE-R_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>D02-L</td>\n", "      <td>[ELE-L_20240524B_070521_DSC05808.jpg, ELE-L_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>D02-R</td>\n", "      <td>[ELE-R_20240524B_070633_DSC05972.jpg, ELE-R_20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>D03-L</td>\n", "      <td>[ELE-L_20240524B_070936_DSC05927.jpg, ELE-L_20...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Task Name                                     Image Filename\n", "0     D01-L  [ELE-L_20240524B_070342_DSC05759.jpg, ELE-L_20...\n", "1     D01-R  [ELE-R_20240524B_070328_DSC05880.jpg, ELE-R_20...\n", "2     D02-L  [ELE-L_20240524B_070521_DSC05808.jpg, ELE-L_20...\n", "3     D02-R  [ELE-R_20240524B_070633_DSC05972.jpg, ELE-R_20...\n", "4     D03-L  [ELE-L_20240524B_070936_DSC05927.jpg, ELE-L_20..."]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["image_unique.head()"]}, {"cell_type": "code", "execution_count": 392, "id": "b311486d-bb28-44c4-96b3-d3d3c70abf51", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "for idx, row in image_unique.iterrows():\n", "    if row['Task Name'] == 'D08-R_2':\n", "        sortedpos = row[\"Image Filename\"]\n", "  \n", "#print(sortedpos)        \n", "posimgdf =pd.DataFrame(sortedpos, columns=[\"filename\"])  "]}, {"cell_type": "code", "execution_count": 393, "id": "efd1b0e6-08f7-4824-b6d6-8db13a1cd4bb", "metadata": {}, "outputs": [{"data": {"text/plain": ["(70, 1)"]}, "execution_count": 393, "metadata": {}, "output_type": "execute_result"}], "source": ["posimgdf.shape"]}, {"cell_type": "code", "execution_count": 394, "id": "5f519dba-11ea-4d79-8703-4a559f945605", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ELE-R_20240524B_075134_DSC07286.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ELE-R_20240524B_075126_DSC07282.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ELE-R_20240524B_075046_DSC07262.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ELE-R_20240524B_075136_DSC07287.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ELE-R_20240524B_075018_DSC07248.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                              filename\n", "0  ELE-R_20240524B_075134_DSC07286.jpg\n", "1  ELE-R_20240524B_075126_DSC07282.jpg\n", "2  ELE-R_20240524B_075046_DSC07262.jpg\n", "3  ELE-R_20240524B_075136_DSC07287.jpg\n", "4  ELE-R_20240524B_075018_DSC07248.jpg"]}, "execution_count": 394, "metadata": {}, "output_type": "execute_result"}], "source": ["posimgdf.head()"]}, {"cell_type": "code", "execution_count": 395, "id": "da9f052b-5ff0-401d-9e24-d5b12f2becd7", "metadata": {}, "outputs": [], "source": ["d01leftsorted = posimgdf.sort_values(by=[\"filename\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 396, "id": "d33f91da-a36d-4743-99c9-d7dd1a7ed9e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["(70, 1)"]}, "execution_count": 396, "metadata": {}, "output_type": "execute_result"}], "source": ["d01leftsorted.shape"]}, {"cell_type": "code", "execution_count": 397, "id": "f9100260-c24b-4b9a-93c0-a5381c6d6b59", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "\n", "for idx, row in allimage_names.iterrows():\n", "    if row['Task Name'] == 'D08-R_2':\n", "        allimgsort = row[\"Image Filename\"]\n", "  \n", "#print(sortedpos)        \n", "allimgdf =pd.DataFrame(allimgsort, columns=[\"filename\"])  "]}, {"cell_type": "code", "execution_count": 398, "id": "c4330936-e844-46be-a806-658469711029", "metadata": {}, "outputs": [], "source": ["allimgsorted = allimgdf.sort_values(by=[\"filename\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 399, "id": "dea4d59c-6807-4656-b53e-3b80c93936e3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(103, 1)"]}, "execution_count": 399, "metadata": {}, "output_type": "execute_result"}], "source": ["allimgsorted.shape"]}, {"cell_type": "code", "execution_count": 400, "id": "37e62252-bc20-4afc-b54a-d6c5272567da", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in d01leftsorted[\"filename\"]:\n", "    if item in allimgsorted[\"filename\"].values:\n", "        index_in_pos = allimgsorted[\"filename\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(allimgsorted[\"filename\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(allimgsorted[\"filename\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 401, "id": "ad3384ac-4470-4bb5-ba17-8cc6cf3acfd7", "metadata": {}, "outputs": [{"data": {"text/plain": ["210"]}, "execution_count": 401, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 402, "id": "a8a254d8-e647-4413-ac04-d179d1fef6af", "metadata": {}, "outputs": [{"data": {"text/plain": ["93"]}, "execution_count": 402, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 403, "id": "37f96012-6fdf-47b4-ac7f-1bb391656e9d", "metadata": {}, "outputs": [], "source": ["d01ldf =pd.DataFrame(fin,columns=[\"filename\"])"]}, {"cell_type": "code", "execution_count": 404, "id": "dc47a43e-bb3b-4bdd-a2d2-f99d4125489b", "metadata": {}, "outputs": [], "source": ["d01ldfsorted=d01ldf.sort_values(by=[\"filename\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 405, "id": "fa04f76e-4199-4205-b902-983359586e36", "metadata": {}, "outputs": [], "source": ["d01ldfsorted.to_csv(\"do1.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "fbb7b2a4-9c6c-4cdc-9e9d-b9036df039c9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "1793d4ad-d91e-4de6-b298-55dea1267a3a", "metadata": {}, "source": ["### Bounding boxes"]}, {"cell_type": "code", "execution_count": null, "id": "f1f816e8-07ec-4d43-893e-e34c0ca6785f", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "annotationsdf = pd.DataFrame(coco_data['annotations'])\n"]}, {"cell_type": "code", "execution_count": null, "id": "7dbabc37-71eb-4fee-bdda-5ca0db2305c5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2cbd26d5-917c-4e61-9f83-9d71f2f0fff6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c7d85a9b-6ae4-46e1-ba8a-4e696aee48aa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3ac43d9c-5899-460a-b68e-5a8204062bb7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0ac0cf95-e8de-46ee-8b58-8eb8c2976e93", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b3f607e5-5351-46ce-8587-f3e601c876d0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}