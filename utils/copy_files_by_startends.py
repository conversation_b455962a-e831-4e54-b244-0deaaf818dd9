# Select files matching start-end times of transects then copy to a target directory
# Inputs:
#   - Transect file with start-end times
#   - EXIF data file with DateTimeOriginal from file
#   - Source directory to read from
#   - Target directory to copy to

import csv
import shutil
import os
from datetime import datetime, timedelta
import argparse

# Create the parser & arguments
parser = argparse.ArgumentParser(description='Copy files by transect times.')
parser.add_argument('times_csv', type=str, help='Path to transect source csv')
parser.add_argument('exif_csv', type=str, help='Path to exifdata csv')
parser.add_argument('source_directory', type=str, help='Source directory')
parser.add_argument('target_directory', type=str, help='Target directory', default= '/nas')
parser.add_argument('session_date', type = str)

# Parse the arguments
args = parser.parse_args()

# Function to check if a given datetime is within the start and end times
def is_within_range(dt, start, end):
    return start <= dt <= end

# Read start and end times from CSV
def read_time_ranges(csv_file, specified_date, offset = -2):
    with open(csv_file, 'r') as f:
        reader = csv.DictReader(f, delimiter=';')
        # Subtract 2 hours from both start and end times using timedelta
        # and combine the specified date with the times
        ranges = [(datetime.combine(specified_date, datetime.strptime(row['SU_START_TIME'], '%H:%M:%S').time()) + timedelta(hours=offset), 
                   datetime.combine(specified_date, datetime.strptime(row['SU_END_TIME'], '%H:%M:%S').time()) + timedelta(hours=offset)) for row in reader]
    return ranges

# Read EXIF data from CSV
def read_exif_data(csv_file):
    with open(csv_file, 'r') as f:
        reader = csv.DictReader(f)
        return [(row['FileName'], datetime.strptime(row['DateTimeOriginal'], '%Y:%m:%d %H:%M:%S')) for row in reader]

# Copy files that match the time ranges
def copy_files(time_ranges, exif_data, source_dir, target_dir):
    for start, end in time_ranges:
        for filename, dt in exif_data:
            if is_within_range(dt, start, end):
                shutil.copy(os.path.join(source_dir, filename), target_dir)
                print(filename)

if __name__ == "__main__":
    # Define paths
    times_csv = args.times_csv
    exif_csv = args.exif_csv
    source_directory = args.source_directory
    target_directory = args.target_directory
    session_date = args.session_date

    # Ensure target directory exists
    if not os.path.exists(target_directory):
        os.makedirs(target_directory)

    time_ranges = read_time_ranges(times_csv, specified_date=session_date)
    exif_data = read_exif_data(exif_csv)
    copy_files(time_ranges, exif_data, source_directory, target_directory)