{"cells": [{"cell_type": "markdown", "id": "ff9ed367", "metadata": {}, "source": ["Divide stratum to left and right"]}, {"cell_type": "code", "execution_count": 1, "id": "8daf5dd7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "b489dc6c-8845-4df1-9680-2fd060786fc4", "metadata": {}, "outputs": [], "source": ["coco_file_path = \"/home/<USER>/Dropbox/Conservation/MWSLab-2023/utils/CVATdata/A2stratum/annotationsA2.json\""]}, {"cell_type": "code", "execution_count": 3, "id": "23b685d4", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "images_df = pd.DataFrame(coco_data['images'])\n"]}, {"cell_type": "code", "execution_count": 6, "id": "4a1f8fd3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(320, 6)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.shape"]}, {"cell_type": "code", "execution_count": 7, "id": "8e9230e2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-L_20220827A-071157_M0203630.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-L_20220827A-070941_M0203562.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-L_20220827A-071514_M0203728.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-L_20220827A-072446_M0204013.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-L_20220827A-072448_M0204014.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   1   7008    4672  KAF22_MFZ-L_20220827A-071157_M0203630.jpg        1   \n", "1   2   7008    4672  KAF22_MFZ-L_20220827A-070941_M0203562.jpg        1   \n", "2   3   7008    4672  KAF22_MFZ-L_20220827A-071514_M0203728.jpg        1   \n", "3   4   7008    4672  KAF22_MFZ-L_20220827A-072446_M0204013.jpg        1   \n", "4   5   7008    4672  KAF22_MFZ-L_20220827A-072448_M0204014.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "60719a18", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "id": "28706da0", "metadata": {}, "outputs": [], "source": ["pattern1 = \"KAF22_MFZ-L_\""]}, {"cell_type": "code", "execution_count": 8, "id": "fa81bb81-35e1-41cc-b57f-e08360748e87", "metadata": {}, "outputs": [], "source": ["pattern2 =  \"KAF22_CMA-L_\""]}, {"cell_type": "code", "execution_count": 8, "id": "4e4444a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(133, 6)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "a2posve_df = images_df[images_df['file_name'].str.contains(pattern1, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "a2posve_df.shape"]}, {"cell_type": "code", "execution_count": 9, "id": "df980d23-bc97-4e58-9ddd-b94a2f4dbd26", "metadata": {}, "outputs": [{"data": {"text/plain": ["(21, 6)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "a2posve_df2 = images_df[images_df['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "a2posve_df2.shape"]}, {"cell_type": "code", "execution_count": 10, "id": "271aee32-9821-4b37-a639-02f33eab122a", "metadata": {}, "outputs": [], "source": ["A2leftposve = pd.concat([a2posve_df,a2posve_df2],ignore_index=True)"]}, {"cell_type": "code", "execution_count": 11, "id": "060a581a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(154, 6)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["A2leftposve.shape"]}, {"cell_type": "code", "execution_count": 12, "id": "b3990d59-9f89-48d7-874a-8150256b6b6e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-L_20220827A-071157_M0203630.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-L_20220827A-070941_M0203562.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-L_20220827A-071514_M0203728.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-L_20220827A-072446_M0204013.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-L_20220827A-072448_M0204014.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   1   7008    4672  KAF22_MFZ-L_20220827A-071157_M0203630.jpg        1   \n", "1   2   7008    4672  KAF22_MFZ-L_20220827A-070941_M0203562.jpg        1   \n", "2   3   7008    4672  KAF22_MFZ-L_20220827A-071514_M0203728.jpg        1   \n", "3   4   7008    4672  KAF22_MFZ-L_20220827A-072446_M0204013.jpg        1   \n", "4   5   7008    4672  KAF22_MFZ-L_20220827A-072448_M0204014.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["A2leftposve.head()"]}, {"cell_type": "code", "execution_count": 13, "id": "594ad7b9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>149</th>\n", "      <td>288</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-083107_M0603473.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>289</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-083105_M0603472.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>151</th>\n", "      <td>290</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-083738_M0603668.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>152</th>\n", "      <td>291</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-083720_M0603659.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>153</th>\n", "      <td>292</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-083734_M0603666.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "149  288   7008    4672  KAF22_CMA-L_20220827A-083107_M0603473.jpg        1   \n", "150  289   7008    4672  KAF22_CMA-L_20220827A-083105_M0603472.jpg        1   \n", "151  290   7008    4672  KAF22_CMA-L_20220827A-083738_M0603668.jpg        1   \n", "152  291   7008    4672  KAF22_CMA-L_20220827A-083720_M0603659.jpg        1   \n", "153  292   7008    4672  KAF22_CMA-L_20220827A-083734_M0603666.jpg        1   \n", "\n", "    date_captured  \n", "149    2022-09-23  \n", "150    2022-09-23  \n", "151    2022-09-23  \n", "152    2022-09-23  \n", "153    2022-09-23  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["A2leftposve.tail()"]}, {"cell_type": "code", "execution_count": 14, "id": "164fc814", "metadata": {}, "outputs": [], "source": ["A2leftsorted = A2leftposve.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 15, "id": "932d181b-abf7-45cb-b26d-c73c7d9163f0", "metadata": {}, "outputs": [], "source": ["A2leftsorted.to_csv(\"A2leftimagespos.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 16, "id": "7bc344e3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>134</th>\n", "      <td>273</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-063741_M0600082.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133</th>\n", "      <td>272</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-063743_M0600083.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>135</th>\n", "      <td>274</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-063753_M0600088.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>136</th>\n", "      <td>275</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-064158_M0600210.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>139</th>\n", "      <td>278</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-065302_M0600541.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "134  273   7008    4672  KAF22_CMA-L_20220827A-063741_M0600082.jpg        1   \n", "133  272   7008    4672  KAF22_CMA-L_20220827A-063743_M0600083.jpg        1   \n", "135  274   7008    4672  KAF22_CMA-L_20220827A-063753_M0600088.jpg        1   \n", "136  275   7008    4672  KAF22_CMA-L_20220827A-064158_M0600210.jpg        1   \n", "139  278   7008    4672  KAF22_CMA-L_20220827A-065302_M0600541.jpg        1   \n", "\n", "    date_captured  \n", "134    2022-09-23  \n", "133    2022-09-23  \n", "135    2022-09-23  \n", "136    2022-09-23  \n", "139    2022-09-23  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["A2leftsorted.head()"]}, {"cell_type": "code", "execution_count": 17, "id": "07d3b903", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A201-L</td>\n", "      <td>655b40de74f2c50044eba5b1</td>\n", "      <td>KAF22_MFZ-L_20220827A-065859_M0203242.jpg</td>\n", "      <td>655b167425ed2b004c91ca36</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A201-L</td>\n", "      <td>655b40de74f2c50044eba5b1</td>\n", "      <td>KAF22_MFZ-L_20220827A-065907_M0203246.jpg</td>\n", "      <td>655b167e25ed2b004c91ca3a</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A201-L</td>\n", "      <td>655b40de74f2c50044eba5b1</td>\n", "      <td>KAF22_MFZ-L_20220827A-065802_M0203214.jpg</td>\n", "      <td>655b16c625ed2b004c91ca55</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A201-L</td>\n", "      <td>655b40de74f2c50044eba5b1</td>\n", "      <td>KAF22_MFZ-L_20220827A-065638_M0203172.jpg</td>\n", "      <td>655b17d725ed2b004c91cabb</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A201-L</td>\n", "      <td>655b40de74f2c50044eba5b1</td>\n", "      <td>KAF22_MFZ-L_20220827A-065410_M0203098.jpg</td>\n", "      <td>655b17d925ed2b004c91cabc</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Task Name                   Task ID  \\\n", "0    A201-L  655b40de74f2c50044eba5b1   \n", "1    A201-L  655b40de74f2c50044eba5b1   \n", "2    A201-L  655b40de74f2c50044eba5b1   \n", "3    A201-L  655b40de74f2c50044eba5b1   \n", "4    A201-L  655b40de74f2c50044eba5b1   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KAF22_MFZ-L_20220827A-065859_M0203242.jpg  655b167425ed2b004c91ca36   \n", "1  KAF22_MFZ-L_20220827A-065907_M0203246.jpg  655b167e25ed2b004c91ca3a   \n", "2  KAF22_MFZ-L_20220827A-065802_M0203214.jpg  655b16c625ed2b004c91ca55   \n", "3  KAF22_MFZ-L_20220827A-065638_M0203172.jpg  655b17d725ed2b004c91cabb   \n", "4  KAF22_MFZ-L_20220827A-065410_M0203098.jpg  655b17d925ed2b004c91cabc   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                False   \n", "1          4672         7008             NaN                False   \n", "2          4672         7008             NaN                False   \n", "3          4672         7008             NaN                False   \n", "4          4672         7008             NaN                False   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["imgpath = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023/scoutexports/A2scout-export-images.csv\"\n", "a2imgs = pd.read_csv(imgpath)\n", "a2imgs.head()"]}, {"cell_type": "code", "execution_count": 18, "id": "87ff32c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(14774, 11)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["a2imgs.shape"]}, {"cell_type": "code", "execution_count": 19, "id": "fbff7df6", "metadata": {}, "outputs": [{"data": {"text/plain": ["14304"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["a2imgs[\"Image Filename\"].nunique()"]}, {"cell_type": "code", "execution_count": 20, "id": "6b578304", "metadata": {}, "outputs": [], "source": ["a2imgsunique = a2imgs[\"Image Filename\"].unique()"]}, {"cell_type": "code", "execution_count": 21, "id": "7675cd62", "metadata": {}, "outputs": [{"data": {"text/plain": ["14304"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["len(a2imgsunique)"]}, {"cell_type": "code", "execution_count": 22, "id": "c1e7f0f8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KAF22_MFZ-L_20220827A-065859_M0203242.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KAF22_MFZ-L_20220827A-065907_M0203246.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KAF22_MFZ-L_20220827A-065802_M0203214.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KAF22_MFZ-L_20220827A-065638_M0203172.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KAF22_MFZ-L_20220827A-065410_M0203098.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   file_name\n", "0  KAF22_MFZ-L_20220827A-065859_M0203242.jpg\n", "1  KAF22_MFZ-L_20220827A-065907_M0203246.jpg\n", "2  KAF22_MFZ-L_20220827A-065802_M0203214.jpg\n", "3  KAF22_MFZ-L_20220827A-065638_M0203172.jpg\n", "4  KAF22_MFZ-L_20220827A-065410_M0203098.jpg"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["a2imgsdf = pd.DataFrame(a2imgsunique,columns=[\"file_name\"])\n", "a2imgsdf.head()"]}, {"cell_type": "code", "execution_count": 23, "id": "b6debf9d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3548, 1)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["a2imgsuniqueleft = a2imgsdf[a2imgsdf['file_name'].str.contains(pattern1, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "a2imgsuniqueleft.shape"]}, {"cell_type": "code", "execution_count": 24, "id": "17ee6f90-2b9f-4a37-b554-f3c3992b7702", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3689, 1)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["a2imgsuniqueleft2 = a2imgsdf[a2imgsdf['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "a2imgsuniqueleft2.shape"]}, {"cell_type": "code", "execution_count": 25, "id": "9628d2b0-5d20-4568-b05c-0cd0fe7df8bf", "metadata": {}, "outputs": [], "source": ["a2imgsuniqueleftfin = pd.concat([a2imgsuniqueleft,a2imgsuniqueleft2],ignore_index=True)"]}, {"cell_type": "code", "execution_count": 26, "id": "929403df-0066-490d-b8ef-5c1435665e37", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KAF22_MFZ-L_20220827A-065859_M0203242.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KAF22_MFZ-L_20220827A-065907_M0203246.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KAF22_MFZ-L_20220827A-065802_M0203214.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KAF22_MFZ-L_20220827A-065638_M0203172.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KAF22_MFZ-L_20220827A-065410_M0203098.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   file_name\n", "0  KAF22_MFZ-L_20220827A-065859_M0203242.jpg\n", "1  KAF22_MFZ-L_20220827A-065907_M0203246.jpg\n", "2  KAF22_MFZ-L_20220827A-065802_M0203214.jpg\n", "3  KAF22_MFZ-L_20220827A-065638_M0203172.jpg\n", "4  KAF22_MFZ-L_20220827A-065410_M0203098.jpg"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["a2imgsuniqueleftfin.head()"]}, {"cell_type": "code", "execution_count": 27, "id": "d6ef5859-5519-42a5-b3da-d836cb433c7e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7232</th>\n", "      <td>KAF22_CMA-L_20220827A-084938_M0604027.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7233</th>\n", "      <td>KAF22_CMA-L_20220827A-085012_M0604044.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7234</th>\n", "      <td>KAF22_CMA-L_20220827A-084910_M0604013.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7235</th>\n", "      <td>KAF22_CMA-L_20220827A-084904_M0604010.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7236</th>\n", "      <td>KAF22_CMA-L_20220827A-084756_M0603976.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      file_name\n", "7232  KAF22_CMA-L_20220827A-084938_M0604027.jpg\n", "7233  KAF22_CMA-L_20220827A-085012_M0604044.jpg\n", "7234  KAF22_CMA-L_20220827A-084910_M0604013.jpg\n", "7235  KAF22_CMA-L_20220827A-084904_M0604010.jpg\n", "7236  KAF22_CMA-L_20220827A-084756_M0603976.jpg"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["a2imgsuniqueleftfin.tail()"]}, {"cell_type": "code", "execution_count": 28, "id": "247cec3a-dece-43d8-ab76-c7ff9b1a5244", "metadata": {}, "outputs": [], "source": ["A2leftimgsorted = a2imgsuniqueleftfin.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 29, "id": "6cc04368-35fb-4d16-a052-218249487290", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3562</th>\n", "      <td>KAF22_CMA-L_20220827A-061929_M0609537.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3548</th>\n", "      <td>KAF22_CMA-L_20220827A-061931_M0609538.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3683</th>\n", "      <td>KAF22_CMA-L_20220827A-061933_M0609539.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3677</th>\n", "      <td>KAF22_CMA-L_20220827A-061935_M0609540.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3615</th>\n", "      <td>KAF22_CMA-L_20220827A-061937_M0609541.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      file_name\n", "3562  KAF22_CMA-L_20220827A-061929_M0609537.jpg\n", "3548  KAF22_CMA-L_20220827A-061931_M0609538.jpg\n", "3683  KAF22_CMA-L_20220827A-061933_M0609539.jpg\n", "3677  KAF22_CMA-L_20220827A-061935_M0609540.jpg\n", "3615  KAF22_CMA-L_20220827A-061937_M0609541.jpg"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["A2leftimgsorted.head()"]}, {"cell_type": "code", "execution_count": 30, "id": "2ece45cc-d694-493b-9387-393d0162ccd4", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7237, 1)"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["A2leftimgsorted.shape"]}, {"cell_type": "code", "execution_count": 31, "id": "2b99b790-5318-4b36-a53a-16d3756b4037", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in A2leftsorted[\"file_name\"]:\n", "    if item in A2leftimgsorted[\"file_name\"].values:\n", "        index_in_pos = A2leftimgsorted[\"file_name\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(A2leftimgsorted[\"file_name\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(A2leftimgsorted[\"file_name\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 32, "id": "2be9136b-e16e-42d9-b00a-93ec347ddb84", "metadata": {}, "outputs": [{"data": {"text/plain": ["462"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 33, "id": "d7696c45-bfd9-436c-afda-4239c5007d50", "metadata": {}, "outputs": [{"data": {"text/plain": ["282"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 34, "id": "008a9c33-b2e3-4b6f-875e-1a9b32e5f81b", "metadata": {}, "outputs": [], "source": ["cvatimg = pd.DataFrame(fin,columns=[\"cvatfilename\"])"]}, {"cell_type": "code", "execution_count": 35, "id": "8a77236e-0e42-482c-892a-0504f417334f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>231</th>\n", "      <td>KAF22_CMA-L_20220827A-063739_M0600081.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>159</th>\n", "      <td>KAF22_CMA-L_20220827A-063741_M0600082.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>210</th>\n", "      <td>KAF22_CMA-L_20220827A-063743_M0600083.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>268</th>\n", "      <td>KAF22_CMA-L_20220827A-063745_M0600084.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>276</th>\n", "      <td>KAF22_CMA-L_20220827A-063751_M0600087.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  c<PERSON><PERSON><PERSON><PERSON>\n", "231  KAF22_CMA-L_20220827A-063739_M0600081.jpg\n", "159  KAF22_CMA-L_20220827A-063741_M0600082.jpg\n", "210  KAF22_CMA-L_20220827A-063743_M0600083.jpg\n", "268  KAF22_CMA-L_20220827A-063745_M0600084.jpg\n", "276  KAF22_CMA-L_20220827A-063751_M0600087.jpg"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin = cvatimg.sort_values(by=[\"cvatfilename\"],ascending=True)\n", "cvatfin.head()"]}, {"cell_type": "code", "execution_count": 36, "id": "ffdef2e9-a764-40b6-9132-94f29afa7e82", "metadata": {}, "outputs": [{"data": {"text/plain": ["(282, 1)"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin.shape"]}, {"cell_type": "code", "execution_count": 37, "id": "afc94256-61ff-436c-a8a0-976acc8ab884", "metadata": {}, "outputs": [], "source": ["cvatfin.to_csv(\"A2leftcvat.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "c2d70828-12c5-45b1-a911-9dc64154f5dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "ac85a34b-1a7e-40be-b824-ed9170a5fdb7", "metadata": {}, "source": ["### A2 Right"]}, {"cell_type": "code", "execution_count": 38, "id": "8ddae76b-c500-4d2d-958d-e20286f9ee1e", "metadata": {}, "outputs": [], "source": ["pattern3 = \"KAF22_MFZ-R_\""]}, {"cell_type": "code", "execution_count": 39, "id": "1ca074ad", "metadata": {}, "outputs": [], "source": ["pattern4 = \"KAF22_CMA-R_\""]}, {"cell_type": "code", "execution_count": 40, "id": "1e9aefc9", "metadata": {}, "outputs": [{"data": {"text/plain": ["(138, 6)"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "A2filtered_df3 = images_df[images_df['file_name'].str.contains(pattern3, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "A2filtered_df3.shape"]}, {"cell_type": "code", "execution_count": 41, "id": "5a54a980-84fb-4c8c-8860-dbbe60fb9c5a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(28, 6)"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "A2filtered_df4 = images_df[images_df['file_name'].str.contains(pattern4, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "A2filtered_df4.shape"]}, {"cell_type": "code", "execution_count": 42, "id": "155440d1-300d-414a-b581-030bb1e3d137", "metadata": {}, "outputs": [], "source": ["a2rightdf = pd.concat([A2filtered_df3,A2filtered_df4],ignore_index=True)"]}, {"cell_type": "code", "execution_count": 43, "id": "3ad7fa26", "metadata": {}, "outputs": [], "source": ["A2rightsorted = a2rightdf.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 44, "id": "786e514b-1138-4c25-a57f-d0dd97b46ca2", "metadata": {}, "outputs": [], "source": ["A2rightsorted.to_csv(\"A2rightimagespos.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 45, "id": "5fd9fbb4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>138</th>\n", "      <td>293</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-R_20220827A-063125_M0509980.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>139</th>\n", "      <td>294</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-R_20220827A-063654_M0500145.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>140</th>\n", "      <td>295</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-R_20220827A-064207_M0500301.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>141</th>\n", "      <td>296</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-R_20220827A-065520_M0500696.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>146</th>\n", "      <td>301</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-R_20220827A-065821_M0500786.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "138  293   7008    4672  KAF22_CMA-R_20220827A-063125_M0509980.jpg        1   \n", "139  294   7008    4672  KAF22_CMA-R_20220827A-063654_M0500145.jpg        1   \n", "140  295   7008    4672  KAF22_CMA-R_20220827A-064207_M0500301.jpg        1   \n", "141  296   7008    4672  KAF22_CMA-R_20220827A-065520_M0500696.jpg        1   \n", "146  301   7008    4672  KAF22_CMA-R_20220827A-065821_M0500786.jpg        1   \n", "\n", "    date_captured  \n", "138    2022-09-23  \n", "139    2022-09-23  \n", "140    2022-09-23  \n", "141    2022-09-23  \n", "146    2022-09-23  "]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["A2rightsorted.head()"]}, {"cell_type": "code", "execution_count": 46, "id": "03450b69-5e5d-4a6b-b810-50649fb1a63b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>130</th>\n", "      <td>264</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-R_20220827A-091744_M0105976.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>271</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-R_20220827A-091746_M0105977.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>135</th>\n", "      <td>269</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-R_20220827A-091802_M0105985.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>131</th>\n", "      <td>265</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-R_20220827A-091826_M0105997.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>136</th>\n", "      <td>270</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_MFZ-R_20220827A-091912_M0106020.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "130  264   7008    4672  KAF22_MFZ-R_20220827A-091744_M0105976.jpg        1   \n", "137  271   7008    4672  KAF22_MFZ-R_20220827A-091746_M0105977.jpg        1   \n", "135  269   7008    4672  KAF22_MFZ-R_20220827A-091802_M0105985.jpg        1   \n", "131  265   7008    4672  KAF22_MFZ-R_20220827A-091826_M0105997.jpg        1   \n", "136  270   7008    4672  KAF22_MFZ-R_20220827A-091912_M0106020.jpg        1   \n", "\n", "    date_captured  \n", "130    2022-09-23  \n", "137    2022-09-23  \n", "135    2022-09-23  \n", "131    2022-09-23  \n", "136    2022-09-23  "]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["A2rightsorted.tail()"]}, {"cell_type": "code", "execution_count": 47, "id": "4a5e0113-6fbd-40f5-98ae-e7927a9dbdd9", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3374, 1)"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern for positive and negative images\n", "A2imgright_df3 = a2imgsdf[a2imgsdf['file_name'].str.contains(pattern3, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "A2imgright_df3.shape"]}, {"cell_type": "code", "execution_count": 48, "id": "64d5e89c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(3693, 1)"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["a2imgrightdf4 = a2imgsdf[a2imgsdf['file_name'].str.contains(pattern4, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "a2imgrightdf4.shape"]}, {"cell_type": "code", "execution_count": 49, "id": "4ab870d2-1386-4c72-b16e-e64d41164614", "metadata": {}, "outputs": [], "source": ["a2imgrightdf = pd.concat([A2imgright_df3,a2imgrightdf4],ignore_index=True)"]}, {"cell_type": "code", "execution_count": 50, "id": "bccea1bf-8ba4-4731-b395-f9884d37d808", "metadata": {}, "outputs": [], "source": ["A2imgrightsorted = a2imgrightdf.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 51, "id": "557fa857-7da4-489d-9f5c-8db5342e9fac", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3374</th>\n", "      <td>KAF22_CMA-R_20220827A-061929_M0509623.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3376</th>\n", "      <td>KAF22_CMA-R_20220827A-061931_M0509624.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3485</th>\n", "      <td>KAF22_CMA-R_20220827A-061933_M0509625.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3466</th>\n", "      <td>KAF22_CMA-R_20220827A-061935_M0509626.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3375</th>\n", "      <td>KAF22_CMA-R_20220827A-061937_M0509627.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      file_name\n", "3374  KAF22_CMA-R_20220827A-061929_M0509623.jpg\n", "3376  KAF22_CMA-R_20220827A-061931_M0509624.jpg\n", "3485  KAF22_CMA-R_20220827A-061933_M0509625.jpg\n", "3466  KAF22_CMA-R_20220827A-061935_M0509626.jpg\n", "3375  KAF22_CMA-R_20220827A-061937_M0509627.jpg"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["A2imgrightsorted.head()"]}, {"cell_type": "code", "execution_count": 52, "id": "ca07d990-3f37-4f08-b294-485a4a12362d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7067, 1)"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["A2imgrightsorted.shape"]}, {"cell_type": "code", "execution_count": 66, "id": "b879b12b-d248-4bc7-baa7-f9119aee8f71", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in DANrightsorted[\"file_name\"]:\n", "    if item in A2imgrightsorted[\"file_name\"].values:\n", "        index_in_pos = A2imgrightsorted[\"file_name\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(A2imgrightsorted[\"file_name\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(A2imgrightsorted[\"file_name\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 67, "id": "db6f4e5d-fbb6-46a9-9bee-8476dbe5cbbb", "metadata": {}, "outputs": [{"data": {"text/plain": ["420"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 68, "id": "7badf276-6239-486d-b2ba-9cb2a2c3bcef", "metadata": {}, "outputs": [{"data": {"text/plain": ["304"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 69, "id": "82318eab-c30a-427e-96de-befaea605c12", "metadata": {}, "outputs": [], "source": ["cvatimg = pd.DataFrame(fin,columns=[\"cvatfilename\"])"]}, {"cell_type": "code", "execution_count": 70, "id": "efc73194-3fdb-436e-bcb9-09a25d0b2c1d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>KES22_IIM-R_20220906A-060247_M0409019.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>145</th>\n", "      <td>KES22_IIM-R_20220906A-060249_M0409020.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>289</th>\n", "      <td>KES22_IIM-R_20220906A-060251_M0409021.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>287</th>\n", "      <td>KES22_IIM-R_20220906A-060253_M0409022.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>293</th>\n", "      <td>KES22_IIM-R_20220906A-060255_M0409023.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  c<PERSON><PERSON><PERSON><PERSON>\n", "39   KES22_IIM-R_20220906A-060247_M0409019.jpg\n", "145  KES22_IIM-R_20220906A-060249_M0409020.jpg\n", "289  KES22_IIM-R_20220906A-060251_M0409021.jpg\n", "287  KES22_IIM-R_20220906A-060253_M0409022.jpg\n", "293  KES22_IIM-R_20220906A-060255_M0409023.jpg"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin = cvatimg.sort_values(by=[\"cvatfilename\"],ascending=True)\n", "cvatfin.head()"]}, {"cell_type": "code", "execution_count": 71, "id": "67455f80-c824-4529-b515-437b119701c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(304, 1)"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin.shape"]}, {"cell_type": "code", "execution_count": 72, "id": "dc41c311-5c01-4556-8eca-b38590211628", "metadata": {}, "outputs": [], "source": ["cvatfin.to_csv(\"DANrightcvat.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "a9973fe2-38f9-4102-8131-4cc4d2db4c4e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b16ac600-595c-47fe-ba9a-b7c7d29f7111", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5259e8b4-d426-487d-91fd-92baeb3e1556", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "cc806d4c-c697-48e4-89e3-129958131725", "metadata": {}, "source": ["Annotations"]}, {"cell_type": "code", "execution_count": 4, "id": "98589797", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "annotationsdf = pd.DataFrame(coco_data['annotations'])\n"]}, {"cell_type": "code", "execution_count": 5, "id": "9ab9d2b2-f241-4195-a1b0-99e7e1ff6688", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>8</td>\n", "      <td>[]</td>\n", "      <td>3927.458951</td>\n", "      <td>[5313.292417693709, 3129.800716115929, 60.7602...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>8</td>\n", "      <td>[]</td>\n", "      <td>4074.529754</td>\n", "      <td>[5508.500971239147, 3053.527175326652, -68.516...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>8</td>\n", "      <td>[]</td>\n", "      <td>2032.251100</td>\n", "      <td>[5211.163439348745, 3293.9827445945425, -49.12...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>1634.335055</td>\n", "      <td>[5244.643488509134, 189.27519151443724, -41.29...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>[]</td>\n", "      <td>2050.534674</td>\n", "      <td>[2319.895207537514, 4350.896252168685, 55.4599...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation         area  \\\n", "0   1         1            8           []  3927.458951   \n", "1   2         1            8           []  4074.529754   \n", "2   3         1            8           []  2032.251100   \n", "3   4         2           15           []  1634.335055   \n", "4   5         3           10           []  2050.534674   \n", "\n", "                                                bbox  iscrowd  \n", "0  [5313.292417693709, 3129.800716115929, 60.7602...        0  \n", "1  [5508.500971239147, 3053.527175326652, -68.516...        0  \n", "2  [5211.163439348745, 3293.9827445945425, -49.12...        0  \n", "3  [5244.643488509134, 189.27519151443724, -41.29...        0  \n", "4  [2319.895207537514, 4350.896252168685, 55.4599...        0  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 75, "id": "e4193fb0-f31a-4583-aee1-5466a6d9ff32", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>35</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060329_M0308659.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>34</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060331_M0308660.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>37</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060335_M0308662.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>130</th>\n", "      <td>131</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060339_M0308664.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>31</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060403_M0308676.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "34    35   7008    4672  KES22_IIM-L_20220906A-060329_M0308659.jpg        1   \n", "33    34   7008    4672  KES22_IIM-L_20220906A-060331_M0308660.jpg        1   \n", "36    37   7008    4672  KES22_IIM-L_20220906A-060335_M0308662.jpg        1   \n", "130  131   7008    4672  KES22_IIM-L_20220906A-060339_M0308664.jpg        1   \n", "30    31   7008    4672  KES22_IIM-L_20220906A-060403_M0308676.jpg        1   \n", "\n", "    date_captured  \n", "34     2022-09-06  \n", "33     2022-09-06  \n", "36     2022-09-06  \n", "130    2022-09-06  \n", "30     2022-09-06  "]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["a2leftsorted = pd.read_csv(\"\")"]}, {"cell_type": "code", "execution_count": 76, "id": "a8ad9f17-01f2-48aa-b76f-0f35d3009a2a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062738_M0309381.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-062756_M0309390.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>31</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-060403_M0308676.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>269</th>\n", "      <td>270</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065544_M0300222.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>270</th>\n", "      <td>271</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065835_M0300307.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>271</th>\n", "      <td>272</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065833_M0300306.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>272</th>\n", "      <td>273</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065943_M0300341.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>273</th>\n", "      <td>274</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220906A-065550_M0300225.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>134 rows × 6 columns</p>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "0      1   7008    4672  KES22_IIM-L_20220906A-062714_M0309369.jpg        1   \n", "1      2   7008    4672  KES22_IIM-L_20220906A-062716_M0309370.jpg        1   \n", "2      3   7008    4672  KES22_IIM-L_20220906A-062738_M0309381.jpg        1   \n", "3      4   7008    4672  KES22_IIM-L_20220906A-062756_M0309390.jpg        1   \n", "30    31   7008    4672  KES22_IIM-L_20220906A-060403_M0308676.jpg        1   \n", "..   ...    ...     ...                                        ...      ...   \n", "269  270   7008    4672  KES22_IIM-L_20220906A-065544_M0300222.jpg        1   \n", "270  271   7008    4672  KES22_IIM-L_20220906A-065835_M0300307.jpg        1   \n", "271  272   7008    4672  KES22_IIM-L_20220906A-065833_M0300306.jpg        1   \n", "272  273   7008    4672  KES22_IIM-L_20220906A-065943_M0300341.jpg        1   \n", "273  274   7008    4672  KES22_IIM-L_20220906A-065550_M0300225.jpg        1   \n", "\n", "    date_captured  \n", "0      2022-09-06  \n", "1      2022-09-06  \n", "2      2022-09-06  \n", "3      2022-09-06  \n", "30     2022-09-06  \n", "..            ...  \n", "269    2022-09-06  \n", "270    2022-09-06  \n", "271    2022-09-06  \n", "272    2022-09-06  \n", "273    2022-09-06  \n", "\n", "[134 rows x 6 columns]"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["DANleftsorted.sort_values(by=[\"id\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 78, "id": "dc2d2883-f90f-4b14-8e97-23bed48462ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1107, 7)"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values.shape"]}, {"cell_type": "code", "execution_count": 79, "id": "a013ab74-3c01-47b8-8c87-83c9ddd2ac7c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1839, 7)"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.shape"]}, {"cell_type": "code", "execution_count": 80, "id": "c57c8809-7d45-470d-8e25-4f583132f5b7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>2797.910556</td>\n", "      <td>[6351.043017, 1166.623453, 60.22392457, 46.458...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1497.501603</td>\n", "      <td>[6482.702368, 1211.366282, 41.64071594, 35.962...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1235.976203</td>\n", "      <td>[6102.257645, 1084.551374, 43.53347576, 28.391...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1053.266678</td>\n", "      <td>[6230.965313, 1126.19209, 39.74795613, 26.4986...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "      <td>[]</td>\n", "      <td>1203.733346</td>\n", "      <td>[6405.099216, 1175.403845, -39.74795613, 30.28...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation         area  \\\n", "0   1         1           16           []  2797.910556   \n", "1   2         1           16           []  1497.501603   \n", "2   3         1           16           []  1235.976203   \n", "3   4         1           16           []  1053.266678   \n", "4   5         1           16           []  1203.733346   \n", "\n", "                                                bbox  iscrowd  \n", "0  [6351.043017, 1166.623453, 60.22392457, 46.458...        0  \n", "1  [6482.702368, 1211.366282, 41.64071594, 35.962...        0  \n", "2  [6102.257645, 1084.551374, 43.53347576, 28.391...        0  \n", "3  [6230.965313, 1126.19209, 39.74795613, 26.4986...        0  \n", "4  [6405.099216, 1175.403845, -39.74795613, 30.28...        0  "]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values.head()"]}, {"cell_type": "code", "execution_count": 81, "id": "c8e87cea-2d9f-4ee4-b71f-1eaaced692da", "metadata": {}, "outputs": [], "source": ["result_values.to_csv(\"DANleftannotations.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "431bd5e3-19b8-4473-b5f2-07460645bd12", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 82, "id": "2e2b725d-2c17-4191-87d6-b76cd869ca62", "metadata": {}, "outputs": [], "source": ["# Compare image 'ID' columns and return annotations \n", "result_valuesright = annotationsdf[annotationsdf['image_id'].isin(DANrightsorted['id'])]"]}, {"cell_type": "code", "execution_count": 83, "id": "438e2ee3-45d1-4789-b428-c18564fd830f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(732, 7)"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["result_valuesright.shape"]}, {"cell_type": "code", "execution_count": 84, "id": "329c5336-6af2-4697-8242-d9471608b45f", "metadata": {}, "outputs": [], "source": ["result_valuesright.to_csv(\"DANrightannotations.csv\",index=False)"]}, {"cell_type": "markdown", "id": "368d46a6-a4bf-443e-b5ee-4e21fd1d5a89", "metadata": {}, "source": ["### A2 left COCO file"]}, {"cell_type": "code", "execution_count": 6, "id": "e844f260-cf39-4a71-9c92-22e761e93057", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>273</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-063741_M0600082.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>272</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-063743_M0600083.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>274</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-063753_M0600088.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>275</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-064158_M0600210.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>278</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-L_20220827A-065302_M0600541.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0  273   7008    4672  KAF22_CMA-L_20220827A-063741_M0600082.jpg        1   \n", "1  272   7008    4672  KAF22_CMA-L_20220827A-063743_M0600083.jpg        1   \n", "2  274   7008    4672  KAF22_CMA-L_20220827A-063753_M0600088.jpg        1   \n", "3  275   7008    4672  KAF22_CMA-L_20220827A-064158_M0600210.jpg        1   \n", "4  278   7008    4672  KAF22_CMA-L_20220827A-065302_M0600541.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["a2leftanno = pd.read_csv(\"A2leftimagespos.csv\")\n", "a2leftanno.head()"]}, {"cell_type": "code", "execution_count": 7, "id": "fd7c2b81-3bad-49b8-98c7-9bd405abc281", "metadata": {}, "outputs": [], "source": ["# Compare image 'ID' columns and return annotations \n", "result_values = annotationsdf[annotationsdf['image_id'].isin(a2leftanno['id'])]"]}, {"cell_type": "code", "execution_count": 8, "id": "263f9b71", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1926, 7)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values.shape"]}, {"cell_type": "code", "execution_count": 9, "id": "454d8063", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4101, 7)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.shape"]}, {"cell_type": "code", "execution_count": 10, "id": "143c5015", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>8</td>\n", "      <td>[]</td>\n", "      <td>3927.458951</td>\n", "      <td>[5313.292417693709, 3129.800716115929, 60.7602...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>8</td>\n", "      <td>[]</td>\n", "      <td>4074.529754</td>\n", "      <td>[5508.500971239147, 3053.527175326652, -68.516...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>8</td>\n", "      <td>[]</td>\n", "      <td>2032.251100</td>\n", "      <td>[5211.163439348745, 3293.9827445945425, -49.12...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>2</td>\n", "      <td>15</td>\n", "      <td>[]</td>\n", "      <td>1634.335055</td>\n", "      <td>[5244.643488509134, 189.27519151443724, -41.29...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>[]</td>\n", "      <td>2050.534674</td>\n", "      <td>[2319.895207537514, 4350.896252168685, 55.4599...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation         area  \\\n", "0   1         1            8           []  3927.458951   \n", "1   2         1            8           []  4074.529754   \n", "2   3         1            8           []  2032.251100   \n", "3   4         2           15           []  1634.335055   \n", "4   5         3           10           []  2050.534674   \n", "\n", "                                                bbox  iscrowd  \n", "0  [5313.292417693709, 3129.800716115929, 60.7602...        0  \n", "1  [5508.500971239147, 3053.527175326652, -68.516...        0  \n", "2  [5211.163439348745, 3293.9827445945425, -49.12...        0  \n", "3  [5244.643488509134, 189.27519151443724, -41.29...        0  \n", "4  [2319.895207537514, 4350.896252168685, 55.4599...        0  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["result_values.head()"]}, {"cell_type": "code", "execution_count": null, "id": "cc08233f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 11, "id": "bc5b969d-9351-4fad-83cb-27aec08851b4", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = a2leftanno.to_dict(orient='records')\n", "coco_dict['annotations'] = result_values.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'a2leftannotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": 32, "id": "25d27e8d-9b16-49d6-80ad-d0457b4526a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'id': 1,\n", "  'name': 'KAZA Elephant Survey',\n", "  'url': 'https://www.kavangozambezi.org/kaza-elephant-survey/'}]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["coco_data[\"licenses\"]"]}, {"cell_type": "code", "execution_count": 12, "id": "36226de4-c23f-45dd-8d40-92c76b860f57", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>293</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-R_20220827A-063125_M0509980.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>294</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-R_20220827A-063654_M0500145.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>295</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-R_20220827A-064207_M0500301.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>296</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-R_20220827A-065520_M0500696.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>301</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KAF22_CMA-R_20220827A-065821_M0500786.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  width  height                                  file_name  license  \\\n", "0  293   7008    4672  KAF22_CMA-R_20220827A-063125_M0509980.jpg        1   \n", "1  294   7008    4672  KAF22_CMA-R_20220827A-063654_M0500145.jpg        1   \n", "2  295   7008    4672  KAF22_CMA-R_20220827A-064207_M0500301.jpg        1   \n", "3  296   7008    4672  KAF22_CMA-R_20220827A-065520_M0500696.jpg        1   \n", "4  301   7008    4672  KAF22_CMA-R_20220827A-065821_M0500786.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["a2rightimage = pd.read_csv(\"A2rightimagespos.csv\")\n", "a2rightimage.head()"]}, {"cell_type": "code", "execution_count": 13, "id": "40cfba17", "metadata": {}, "outputs": [], "source": ["# Compare image 'ID' columns and return annotations \n", "a2rightanno = annotationsdf[annotationsdf['image_id'].isin(a2rightimage['id'])]"]}, {"cell_type": "code", "execution_count": 14, "id": "842cc249", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1635</th>\n", "      <td>1636</td>\n", "      <td>133</td>\n", "      <td>13</td>\n", "      <td>[]</td>\n", "      <td>4904.443891</td>\n", "      <td>[4698.923223, 281.83238548732857, 70.031734885...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1636</th>\n", "      <td>1637</td>\n", "      <td>133</td>\n", "      <td>13</td>\n", "      <td>[]</td>\n", "      <td>1182.225335</td>\n", "      <td>[4706.494221465611, 406.7538585261802, -18.927...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1637</th>\n", "      <td>1638</td>\n", "      <td>133</td>\n", "      <td>13</td>\n", "      <td>[]</td>\n", "      <td>2042.025579</td>\n", "      <td>[4329.837052757559, 262.90488957235107, 28.391...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1638</th>\n", "      <td>1639</td>\n", "      <td>133</td>\n", "      <td>13</td>\n", "      <td>[]</td>\n", "      <td>2310.713155</td>\n", "      <td>[4278.732813787119, 321.5801269087813, 28.3912...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1639</th>\n", "      <td>1640</td>\n", "      <td>133</td>\n", "      <td>13</td>\n", "      <td>[]</td>\n", "      <td>3224.250914</td>\n", "      <td>[5514.698297035151, 1105.1784388356505, -56.78...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        id  image_id  category_id segmentation         area  \\\n", "1635  1636       133           13           []  4904.443891   \n", "1636  1637       133           13           []  1182.225335   \n", "1637  1638       133           13           []  2042.025579   \n", "1638  1639       133           13           []  2310.713155   \n", "1639  1640       133           13           []  3224.250914   \n", "\n", "                                                   bbox  iscrowd  \n", "1635  [4698.923223, 281.83238548732857, 70.031734885...        0  \n", "1636  [4706.494221465611, 406.7538585261802, -18.927...        0  \n", "1637  [4329.837052757559, 262.90488957235107, 28.391...        0  \n", "1638  [4278.732813787119, 321.5801269087813, 28.3912...        0  \n", "1639  [5514.698297035151, 1105.1784388356505, -56.78...        0  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["a2rightanno.head()"]}, {"cell_type": "code", "execution_count": 15, "id": "9d7cf953", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2175, 7)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["a2rightanno.shape"]}, {"cell_type": "code", "execution_count": null, "id": "c923d12d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 92, "id": "6df4b37a-be3d-44d3-a10a-b92fe5a7ec73", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dictright = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dictright['images'] = danrightimages.to_dict(orient='records')\n", "coco_dictright['annotations'] = danrightanno.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'danrightannotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dictright, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "id": "254708b5-3db5-489a-bc35-918c449acee8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16dd420b-4f4d-42de-9aa8-1917a1d7a760", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "043cfaf2-580a-4a72-9871-0e8bbad31a7f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}