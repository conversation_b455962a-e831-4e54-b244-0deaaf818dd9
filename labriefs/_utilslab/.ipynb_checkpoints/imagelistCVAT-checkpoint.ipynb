{"cells": [{"cell_type": "code", "execution_count": 2, "id": "b4ddb430-6614-4eaa-ab27-339b39f0a1e3", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 6, "id": "be1ceb01-4d1c-44b6-8181-728d886fe035", "metadata": {}, "outputs": [], "source": ["homedir = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023\""]}, {"cell_type": "code", "execution_count": 20, "id": "d01e21f1-4b37-4945-95f9-24a2150a9557", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>Task ID</th>\n", "      <th>ImageFilename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060454_M0309398.jpg</td>\n", "      <td>653a4d3f4a2456004cd8a583</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060452_M0309397.jpg</td>\n", "      <td>653a4d414a2456004cd8a584</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060559_M0309430.jpg</td>\n", "      <td>653a4d444a2456004cd8a585</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060611_M0309436.jpg</td>\n", "      <td>653a4d474a2456004cd8a586</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060615_M0309438.jpg</td>\n", "      <td>653a4d494a2456004cd8a587</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    TaskName                   Task ID  \\\n", "0  SH01-L_85  653a60f36f30500045ef6b69   \n", "1  SH01-L_85  653a60f36f30500045ef6b69   \n", "2  SH01-L_85  653a60f36f30500045ef6b69   \n", "3  SH01-L_85  653a60f36f30500045ef6b69   \n", "4  SH01-L_85  653a60f36f30500045ef6b69   \n", "\n", "                               ImageFilename                  Image ID  \\\n", "0  KES22_IIM-L_20220904A-060454_M0309398.jpg  653a4d3f4a2456004cd8a583   \n", "1  KES22_IIM-L_20220904A-060452_M0309397.jpg  653a4d414a2456004cd8a584   \n", "2  KES22_IIM-L_20220904A-060559_M0309430.jpg  653a4d444a2456004cd8a585   \n", "3  KES22_IIM-L_20220904A-060611_M0309436.jpg  653a4d474a2456004cd8a586   \n", "4  KES22_IIM-L_20220904A-060615_M0309438.jpg  653a4d494a2456004cd8a587   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                False   \n", "1          4672         7008             NaN                False   \n", "2          4672         7008             NaN                False   \n", "3          4672         7008             NaN                False   \n", "4          4672         7008             NaN                False   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg = pd.read_csv(homedir+\"/scoutexports/SHscout-export-images.csv\")\n", "shimg.head()"]}, {"cell_type": "code", "execution_count": 21, "id": "734d87dc-01e9-42be-b4ec-84cf6ec05215", "metadata": {}, "outputs": [{"data": {"text/plain": ["(5581, 11)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg.shape"]}, {"cell_type": "code", "execution_count": 36, "id": "1cad88d3-b5ce-4090-86c8-e56904113a29", "metadata": {}, "outputs": [{"data": {"text/plain": ["5232"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg[\"ImageFilename\"].nunique()"]}, {"cell_type": "code", "execution_count": 39, "id": "f08781c5-39ee-4044-92b7-2a2bcce773f6", "metadata": {}, "outputs": [], "source": ["shimgunique =shimg[\"ImageFilename\"].unique()"]}, {"cell_type": "code", "execution_count": 40, "id": "e469493d-e90f-41a9-9ed3-73e9bd0b8179", "metadata": {}, "outputs": [], "source": ["shimguniquedf = pd.DataFrame(shimgunique,columns=[\"ImageFilename\"])"]}, {"cell_type": "code", "execution_count": 41, "id": "12827654-9fa7-4947-b216-de68cf1f0469", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220904A-060454_M0309398.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220904A-060452_M0309397.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220904A-060559_M0309430.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220904A-060611_M0309436.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220904A-060615_M0309438.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename\n", "0  KES22_IIM-L_20220904A-060454_M0309398.jpg\n", "1  KES22_IIM-L_20220904A-060452_M0309397.jpg\n", "2  KES22_IIM-L_20220904A-060559_M0309430.jpg\n", "3  KES22_IIM-L_20220904A-060611_M0309436.jpg\n", "4  KES22_IIM-L_20220904A-060615_M0309438.jpg"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["shimguniquedf.head()"]}, {"cell_type": "code", "execution_count": 22, "id": "9a906ecd-a5a5-49ee-9cf0-b8d66f5ef322", "metadata": {}, "outputs": [{"data": {"text/plain": ["72"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg[\"TaskName\"].nunique()"]}, {"cell_type": "code", "execution_count": 23, "id": "81965318-76ce-4e39-b9c5-c3f5dd06d1dd", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['SH01-L_85', 'SH01-L_159', 'SH02-L_100', 'SH02-L_196',\n", "       'SH02-L_062_36', 'SH03-L_126', 'SH03-L_206', 'SH03-L_36',\n", "       'SH03-L_63', 'SH04-L_135', 'SH04-L_224', 'SH04-L_064_58',\n", "       'SH05-L_138', 'SH05-L_218', 'SH05-L_38', 'SH05-L_65', 'SH06-L_155',\n", "       'SH06-L_235', 'SH06-L_37', 'SH07-L_148', 'SH07-L_248',\n", "       'SH08-L_343', 'SH08-L_428', 'SH09-L_513', 'SH09-L_586',\n", "       'SH10-L_74', 'SH10-L_138', 'SH11-L_219', 'SH11-L_76', 'SH12-L_157',\n", "       'SH12-L_206', 'SH12-L_32', 'SH13-L_118', 'SH13-L_191', 'SH01-R_85',\n", "       'SH01-R_159', 'SH02-R_100', 'SH02-L_197', 'SH02-R_062_37',\n", "       'SH03-R_126', 'SH03-R_208', 'SH03-R_63', 'SH04-R_149',\n", "       'SH04-R_225', 'SH04-R_064_58', 'SH05-R_138', 'SH05-R_219',\n", "       'SH05-R_65', 'SH06-R_155', 'SH06-R_235', 'SH06-R_37', 'SH07-R_149',\n", "       'SH07-R_249', 'SH08-R_343', 'SH08-R_430', 'SH09-R_515',\n", "       'SH09-R_589', 'SH10-R_75', 'SH10-R_139', 'SH11-R_221', 'SH11-R_76',\n", "       'SH12-R_157', 'SH12-R_207', 'SH12-R_32', 'SH13-R_118',\n", "       'SH13-R_192', 'SH14-R_80', 'SH14-R_151', 'SH15-R', 'SH14-L_80',\n", "       'SH14-L_151', 'SH15-L_197'], dtype=object)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg[\"TaskName\"].unique()"]}, {"cell_type": "code", "execution_count": 24, "id": "7dd8b6c9-967f-4b2a-9649-04b7dbdc4d31", "metadata": {}, "outputs": [], "source": ["shtasks = shimg[\"TaskName\"].unique()"]}, {"cell_type": "code", "execution_count": 25, "id": "83777c7d-1aa0-4cc2-bf91-44634f862a13", "metadata": {}, "outputs": [], "source": ["shtaskdf = pd.DataFrame(shtasks,columns=[\"TaskName\"])"]}, {"cell_type": "code", "execution_count": 26, "id": "3d864db6-202c-4c65-b529-c23452740dd3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SH01-L_85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SH01-L_159</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SH02-L_100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SH02-L_196</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SH02-L_062_36</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        TaskName\n", "0      SH01-L_85\n", "1     SH01-L_159\n", "2     SH02-L_100\n", "3     SH02-L_196\n", "4  SH02-L_062_36"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["shtaskdf.head()"]}, {"cell_type": "code", "execution_count": 27, "id": "42bb5619-625f-4c5b-a38d-e6c8545f95ec", "metadata": {}, "outputs": [{"data": {"text/plain": ["(72, 1)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["shtaskdf.shape"]}, {"cell_type": "code", "execution_count": 28, "id": "d107c8e6-183e-4da7-8d04-ecf82f8f49e9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>2992.284408</td>\n", "      <td>3582.150410</td>\n", "      <td>83.281342</td>\n", "      <td>108.265744</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>3119.288454</td>\n", "      <td>3278.173512</td>\n", "      <td>110.347778</td>\n", "      <td>127.004046</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>3221.308098</td>\n", "      <td>2886.751205</td>\n", "      <td>112.429812</td>\n", "      <td>189.465053</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>3106.796253</td>\n", "      <td>2818.044098</td>\n", "      <td>81.199308</td>\n", "      <td>106.183711</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>312.139451</td>\n", "      <td>3096.658157</td>\n", "      <td>77.035241</td>\n", "      <td>74.953208</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     TaskName                   TaskID   \\\n", "0  SH01-L_159  653a61286f30500045ef6b6b   \n", "1  SH01-L_159  653a61286f30500045ef6b6b   \n", "2  SH01-L_159  653a61286f30500045ef6b6b   \n", "3  SH01-L_159  653a61286f30500045ef6b6b   \n", "4  SH01-L_159  653a61286f30500045ef6b6b   \n", "\n", "                              ImageFilename                  ImageID    \\\n", "0  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "1  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "2  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "3  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "4  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "\n", "          BoxX         BoxY        BoxW        BoxH     Label  \\\n", "0  2992.284408  3582.150410   83.281342  108.265744  elephant   \n", "1  3119.288454  3278.173512  110.347778  127.004046  elephant   \n", "2  3221.308098  2886.751205  112.429812  189.465053  elephant   \n", "3  3106.796253  2818.044098   81.199308  106.183711  elephant   \n", "4   312.139451  3096.658157   77.035241   74.953208  elephant   \n", "\n", "   LabelConfidence Assignee      Timestamp  IsGroundTruth  ExcludedByLine  \n", "0              NaN  hgeorge  1698389707201          False             NaN  \n", "1              NaN  hgeorge  1698389707201          False             NaN  \n", "2              NaN  hgeorge  1698389707201          False             NaN  \n", "3              NaN  hgeorge  1698389707201          False             NaN  \n", "4              NaN  hgeorge  1698389707201          False             NaN  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["shannodf = pd.read_csv(homedir+\"/scoutexports/SHscout-export-annotations.csv\")\n", "shannodf.head()"]}, {"cell_type": "code", "execution_count": 29, "id": "cef93329-87c8-4a33-aa4e-e4132ab36a20", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['elephant', 'unknown mammal', 'giraffe', 'unknown antelope', 'ec4',\n", "       'impala', 'ec3', 'gazelle_thomsons', 'car', 'warthog',\n", "       'white_bones', 'zebra', 'buffalo', 'sable', 'bird', 'roan'],\n", "      dtype=object)"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["shannodf[\"Label\"].unique()"]}, {"cell_type": "code", "execution_count": 30, "id": "33f52840-fa1e-45f5-b39d-46ae06dca693", "metadata": {}, "outputs": [{"data": {"text/plain": ["16"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["shannodf[\"Label\"].nunique()"]}, {"cell_type": "code", "execution_count": 31, "id": "3c0acbc2-2914-4a06-a54a-c51e656e696c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1090, 14)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["shannodf.shape"]}, {"cell_type": "code", "execution_count": 35, "id": "faa2c612-e33c-490e-893d-38908f5785d1", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'ImageFilename'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m/opt/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/core/indexes/base.py:3361\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[0;34m(self, key, method, tolerance)\u001b[0m\n\u001b[1;32m   3360\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 3361\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3362\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[0;32m/opt/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/_libs/index.pyx:76\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32m/opt/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/_libs/index.pyx:108\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mpandas/_libs/hashtable_class_helper.pxi:5198\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mpandas/_libs/hashtable_class_helper.pxi:5206\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "\u001b[0;31m<PERSON>eyError\u001b[0m: 'ImageFilename'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn [35], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mshannodf\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mImageFilename\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\n", "File \u001b[0;32m/opt/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/core/frame.py:3458\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   3456\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m   3457\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[0;32m-> 3458\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3459\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[1;32m   3460\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[0;32m/opt/miniconda3/envs/ddata/lib/python3.9/site-packages/pandas/core/indexes/base.py:3363\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[0;34m(self, key, method, tolerance)\u001b[0m\n\u001b[1;32m   3361\u001b[0m         \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine\u001b[38;5;241m.\u001b[39mget_loc(casted_key)\n\u001b[1;32m   3362\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON>eyError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[0;32m-> 3363\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON>eyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m   3365\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_scalar(key) \u001b[38;5;129;01mand\u001b[39;00m isna(key) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhasnans:\n\u001b[1;32m   3366\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key)\n", "\u001b[0;31m<PERSON>eyError\u001b[0m: 'ImageFilename'"]}], "source": ["shannodf[\"ImageFilename\"]"]}, {"cell_type": "code", "execution_count": null, "id": "95bb3a0a-f271-47d0-b0c8-a1e7b2fbae17", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}