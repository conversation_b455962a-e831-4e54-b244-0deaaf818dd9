body {
    font-family: Arial, sans-serif;
    background-color: #e7f2fd;
}

.container {
    display: flex;
    height: 100vh;  /* Adjust this value as necessary */
}

.left-pane {
    flex: 0.3;
    background-color: #fff;
    padding: 1em;
    overflow: auto;
}

#middlePane {
    display: flex;
    flex-direction: column;
    align-items: center;  /* This centers items horizontally */
    justify-content: flex-start;  /* This aligns items to the top */
    overflow-y: auto;
    width: 40%;
    background-color: #e6e6e6;
    padding: 10px;
    border-right: 2px solid black;
}

.right-pane {
    flex: 2;
    background-color: off-white;
    padding: 1em;
}

.thumbnail {
    width: 100px;  /* Setting a fixed width for thumbnails */
    height: 100px; /* Setting a fixed height for thumbnails */
    background-size: cover;
    background-position: top;
    cursor: pointer;
    transition: transform 0.2s;
}

.thumbnail:hover {
    transform: scale(1.05);
}

#fullImage img {
    max-width: 100%;
    max-height: 100%;
}

ul {
    list-style-type: none;
  }