{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import xml.etree.ElementTree as ET\n", "\n", "# Read the csv file\n", "df = pd.read_csv('scout-export-annotations.csv')\n", "\n", "# Create the root of the xml file\n", "root = ET.Element('root')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Iterate over rows of the csv dataframe\n", "for index, row in df.iterrows():\n", "    image = ET.SubElement(root, 'image')\n", "\n", "    # Set image attributes\n", "    image.set('id', str(row['Image ID']))\n", "    image.set('name', row['Image Filename'])\n", "    # Note: Width and height are not provided in csv, so we'll set them as 'unknown'\n", "    image.set('width', 'unknown')\n", "    image.set('height', 'unknown')\n", "\n", "    box = ET.SubElement(image, 'box')\n", "    \n", "    # Set box attributes\n", "    box.set('label', row['Label'])\n", "    # Note: 'occluded' attribute is not provided in csv, so we'll set it as '0'\n", "    box.set('occluded', '0')\n", "    box.set('xtl', str(row['Box X']))\n", "    box.set('ytl', str(row['Box Y']))\n", "    box.set('xbr', str(row['Box X'] + row['Box W']))\n", "    box.set('ybr', str(row['Box Y'] + row['Box H']))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Write the xml to output.xml file\n", "tree = ET.<PERSON>(root)\n", "tree.write('scout-export-annotations.xml')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}, "vscode": {"interpreter": {"hash": "2bfcb952091461d90d6295f6179a2d2169454a55278ec4e780c6f933dd60253c"}}}, "nbformat": 4, "nbformat_minor": 2}