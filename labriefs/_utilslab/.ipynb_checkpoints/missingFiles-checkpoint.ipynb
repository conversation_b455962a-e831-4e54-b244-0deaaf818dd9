{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a408e247", "metadata": {}, "outputs": [], "source": ["# search for missing files\n", "import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 3, "id": "4f1139e4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6351.043017</td>\n", "      <td>1166.623453</td>\n", "      <td>60.223925</td>\n", "      <td>46.458456</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1697720924067</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6482.702368</td>\n", "      <td>1211.366282</td>\n", "      <td>41.640716</td>\n", "      <td>35.962436</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6102.257645</td>\n", "      <td>1084.551374</td>\n", "      <td>43.533476</td>\n", "      <td>28.391397</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6230.965313</td>\n", "      <td>1126.192090</td>\n", "      <td>39.747956</td>\n", "      <td>26.498637</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6405.099216</td>\n", "      <td>1175.403845</td>\n", "      <td>-39.747956</td>\n", "      <td>30.284157</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        TaskName                   TaskID   \\\n", "0    DAN05-L_174  6530de5f05667e0045cadc4e   \n", "1  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "2  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "3  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "4  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "\n", "                              ImageFilename                  ImageID    \\\n", "0  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "1  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "2  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "3  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "4  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "\n", "          BoxX         BoxY       BoxW       BoxH          Label  \\\n", "0  6351.043017  1166.623453  60.223925  46.458456  vervet monkey   \n", "1  6482.702368  1211.366282  41.640716  35.962436  vervet monkey   \n", "2  6102.257645  1084.551374  43.533476  28.391397  vervet monkey   \n", "3  6230.965313  1126.192090  39.747956  26.498637  vervet monkey   \n", "4  6405.099216  1175.403845 -39.747956  30.284157  vervet monkey   \n", "\n", "   LabelConfidence    Assignee      Timestamp  IsGroundTruth ExcludedByLine  \n", "0              NaN  annotator1  1697720924067          False          False  \n", "1              NaN     wmagesa  1697717018183          False          False  \n", "2              NaN     wmagesa  1697717018183          False          False  \n", "3              NaN     wmagesa  1697717018183          False          False  \n", "4              NaN     wmagesa  1697717018183          False          False  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["#load the csv file with annotations\n", "dandf = pd.read_csv(\"DANannotations.csv\")\n", "dandf.head()"]}, {"cell_type": "code", "execution_count": 18, "id": "9295b56a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1877, 14)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["dandf.shape"]}, {"cell_type": "code", "execution_count": 22, "id": "8199aec7", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>ImageFilename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062708_M0309366.jpg</td>\n", "      <td>6530d3400e8be9004ca314e2</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062718_M0309371.jpg</td>\n", "      <td>6530d3cb0e8be9004ca31510</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062712_M0309368.jpg</td>\n", "      <td>6530d3f80e8be9004ca3151d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "      <td>6530d42a0e8be9004ca3152d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_174</td>\n", "      <td>6530de5f05667e0045cadc4e</td>\n", "      <td>KES22_IIM-L_20220906A-062706_M0309365.jpg</td>\n", "      <td>6530d4af0e8be9004ca3155e</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Task Name                   Task ID  \\\n", "0  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "1  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "2  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "3  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "4  DAN05-L_174  6530de5f05667e0045cadc4e   \n", "\n", "                               ImageFilename                  Image ID  \\\n", "0  KES22_IIM-L_20220906A-062708_M0309366.jpg  6530d3400e8be9004ca314e2   \n", "1  KES22_IIM-L_20220906A-062718_M0309371.jpg  6530d3cb0e8be9004ca31510   \n", "2  KES22_IIM-L_20220906A-062712_M0309368.jpg  6530d3f80e8be9004ca3151d   \n", "3  KES22_IIM-L_20220906A-062716_M0309370.jpg  6530d42a0e8be9004ca3152d   \n", "4  KES22_IIM-L_20220906A-062706_M0309365.jpg  6530d4af0e8be9004ca3155e   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                 True   \n", "1          4672         7008             NaN                 True   \n", "2          4672         7008             NaN                 True   \n", "3          4672         7008             NaN                 True   \n", "4          4672         7008             NaN                 True   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["danimgdf = pd.read_csv(\"DANimages.csv\")\n", "danimgdf.head()"]}, {"cell_type": "code", "execution_count": 23, "id": "f9936294", "metadata": {}, "outputs": [{"data": {"text/plain": ["(13154, 11)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["danimgdf.shape"]}, {"cell_type": "code", "execution_count": 25, "id": "9a70ba83", "metadata": {}, "outputs": [{"data": {"text/plain": ["8534"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["danimgdf[\"ImageFilename\"].nunique()"]}, {"cell_type": "code", "execution_count": 36, "id": "1a407eb6", "metadata": {}, "outputs": [], "source": ["danimg = danimgdf[\"ImageFilename\"].unique()"]}, {"cell_type": "code", "execution_count": 37, "id": "7a917c16", "metadata": {}, "outputs": [{"data": {"text/plain": ["(8534,)"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["danimg.shape"]}, {"cell_type": "code", "execution_count": 38, "id": "f7fa4a7b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220906A-062708_M0309366.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220906A-062718_M0309371.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220906A-062712_M0309368.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220906A-062716_M0309370.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220906A-062706_M0309365.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename\n", "0  KES22_IIM-L_20220906A-062708_M0309366.jpg\n", "1  KES22_IIM-L_20220906A-062718_M0309371.jpg\n", "2  KES22_IIM-L_20220906A-062712_M0309368.jpg\n", "3  KES22_IIM-L_20220906A-062716_M0309370.jpg\n", "4  KES22_IIM-L_20220906A-062706_M0309365.jpg"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["danimg= pd.DataFrame(danimg,columns=[\"ImageFilename\"])\n", "danimg.head()"]}, {"cell_type": "code", "execution_count": 39, "id": "dad5186a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(8534, 1)"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["danimg.shape"]}, {"cell_type": "code", "execution_count": 51, "id": "f9973b23", "metadata": {"scrolled": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>ImageFilename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050240_M0301361.jpg</td>\n", "      <td>6540dfe8e1ace1004c5535db</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050302_M0301372.jpg</td>\n", "      <td>6540dfebe1ace1004c5535dc</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050248_M0301365.jpg</td>\n", "      <td>6540dfede1ace1004c5535dd</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050338_M0301390.jpg</td>\n", "      <td>6540dff0e1ace1004c5535de</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050318_M0301380.jpg</td>\n", "      <td>6540dff3e1ace1004c5535df</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Task Name                   Task ID  \\\n", "0  NG26-01-L_34  6540f9228a297200456fceee   \n", "1  NG26-01-L_34  6540f9228a297200456fceee   \n", "2  NG26-01-L_34  6540f9228a297200456fceee   \n", "3  NG26-01-L_34  6540f9228a297200456fceee   \n", "4  NG26-01-L_34  6540f9228a297200456fceee   \n", "\n", "                               ImageFilename                  Image ID  \\\n", "0  KES22_IIM-L_20220924A-050240_M0301361.jpg  6540dfe8e1ace1004c5535db   \n", "1  KES22_IIM-L_20220924A-050302_M0301372.jpg  6540dfebe1ace1004c5535dc   \n", "2  KES22_IIM-L_20220924A-050248_M0301365.jpg  6540dfede1ace1004c5535dd   \n", "3  KES22_IIM-L_20220924A-050338_M0301390.jpg  6540dff0e1ace1004c5535de   \n", "4  KES22_IIM-L_20220924A-050318_M0301380.jpg  6540dff3e1ace1004c5535df   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                 True   \n", "1          4672         7008             NaN                 True   \n", "2          4672         7008             NaN                 True   \n", "3          4672         7008             NaN                 True   \n", "4          4672         7008             NaN                 True   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["##NG26\n", "homedir =\"/home/<USER>/Dropbox/Conservation/MWSLab-2023/scoutexports\"\n", "ngimgdf = pd.read_csv(homedir+\"/NG26scout-export-images.csv\")\n", "ngimgdf.head()"]}, {"cell_type": "code", "execution_count": 52, "id": "d4d4dfa0", "metadata": {}, "outputs": [{"data": {"text/plain": ["(10493, 11)"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["ngimgdf.shape"]}, {"cell_type": "code", "execution_count": 53, "id": "238db280", "metadata": {}, "outputs": [], "source": ["ng26img = ngimgdf[\"ImageFilename\"].unique()  #unique images on tasks"]}, {"cell_type": "code", "execution_count": 56, "id": "c122627d", "metadata": {}, "outputs": [], "source": ["ng26img = pd.DataFrame(ng26img,columns=[\"ImageFilename\"])"]}, {"cell_type": "code", "execution_count": 61, "id": "9edfa82a", "metadata": {}, "outputs": [{"data": {"text/plain": ["9938"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["ngimgdf[\"ImageFilename\"].nunique()"]}, {"cell_type": "code", "execution_count": 48, "id": "5f1ea42d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(10096, 1)"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["##ingested files\n", "homedir =\"/home/<USER>/Dropbox/Conservation/MWSLab-2023\"\n", "ng26ingestdf = pd.read_csv(homedir+\"/labriefs/_media/ng26ingested.csv\")\n", "ng26ingestdf.shape"]}, {"cell_type": "code", "execution_count": 62, "id": "b4b91943", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ImageFilename    10096\n", "missimg_img1     10096\n", "missimg_img2      9936\n", "dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ImageFilename</th>\n", "      <th>missimg_img1</th>\n", "      <th>missimg_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220924A-050238_M0301360.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220924A-050240_M0301361.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220924A-050242_M0301362.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220924A-050244_M0301363.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220924A-050246_M0301364.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               ImageFilename missimg_img1 missimg_img2\n", "0  KES22_IIM-L_20220924A-050238_M0301360.jpg     imageid1     imageid2\n", "1  KES22_IIM-L_20220924A-050240_M0301361.jpg     imageid1     imageid2\n", "2  KES22_IIM-L_20220924A-050242_M0301362.jpg     imageid1     imageid2\n", "3  KES22_IIM-L_20220924A-050244_M0301363.jpg     imageid1     imageid2\n", "4  KES22_IIM-L_20220924A-050246_M0301364.jpg     imageid1     imageid2"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["#find the difference 10096 - 9938 =158 images\n", "ng26ingestdf['missimg'] = 'imageid1'\n", "ng26img['missimg'] ='imageid2'\n", "\n", "\n", "merged_df = ng26ingestdf.merge(ng26img, on='ImageFilename',how='left', suffixes=('_img1','_img2'))\n", "print(merged_df.count())\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": 60, "id": "169c3b63", "metadata": {}, "outputs": [{"data": {"text/plain": ["ImageFilename    160\n", "missimg_img1     160\n", "missimg_img2       0\n", "dtype: int64"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["rows_not_in_tasks = merged_df[merged_df['missimg_img2'].isna()]\n", "\n", "\n", "rows_not_in_tasks.count()"]}, {"cell_type": "code", "execution_count": 63, "id": "c57243ac", "metadata": {}, "outputs": [], "source": ["rows_not_in_tasks.to_csv(\"MissingfilesNG26.csv\")"]}, {"cell_type": "markdown", "id": "8187c2f5", "metadata": {}, "source": ["## MT Block"]}, {"cell_type": "code", "execution_count": 4, "id": "f11515be", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070352_M0801356.jpg</td>\n", "      <td>654b3d440ff5c4004c9b6b56</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070504_M0801392.jpg</td>\n", "      <td>654b3d8e0ff5c4004c9b6b72</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070500_M0801390.jpg</td>\n", "      <td>654b3dab0ff5c4004c9b6b7d</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070342_M0801351.jpg</td>\n", "      <td>654b3e2c0ff5c4004c9b6bae</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070354_M0801357.jpg</td>\n", "      <td>654b3e990ff5c4004c9b6bd7</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Task Name                   Task ID  \\\n", "0  MT02-L_46  654b524e47d0660045090326   \n", "1  MT02-L_46  654b524e47d0660045090326   \n", "2  MT02-L_46  654b524e47d0660045090326   \n", "3  MT02-L_46  654b524e47d0660045090326   \n", "4  MT02-L_46  654b524e47d0660045090326   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_WOT-L_20220909A-070352_M0801356.jpg  654b3d440ff5c4004c9b6b56   \n", "1  KES22_WOT-L_20220909A-070504_M0801392.jpg  654b3d8e0ff5c4004c9b6b72   \n", "2  KES22_WOT-L_20220909A-070500_M0801390.jpg  654b3dab0ff5c4004c9b6b7d   \n", "3  KES22_WOT-L_20220909A-070342_M0801351.jpg  654b3e2c0ff5c4004c9b6bae   \n", "4  KES22_WOT-L_20220909A-070354_M0801357.jpg  654b3e990ff5c4004c9b6bd7   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                False   \n", "1          4672         7008             NaN                False   \n", "2          4672         7008             NaN                False   \n", "3          4672         7008             NaN                False   \n", "4          4672         7008             NaN                False   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["homedir =\"/home/<USER>/Dropbox/Conservation/MWSLab-2023\"\n", "mtimg = pd.read_csv(homedir+\"/scoutexports/MTscout-export-images.csv\")\n", "mtimg.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "1299722a", "metadata": {}, "outputs": [{"data": {"text/plain": ["(9278, 11)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimg.shape"]}, {"cell_type": "code", "execution_count": 6, "id": "91b1e95e", "metadata": {}, "outputs": [{"data": {"text/plain": ["8677"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["mtimg[\"Image Filename\"].nunique()"]}, {"cell_type": "code", "execution_count": null, "id": "1f731f79", "metadata": {}, "outputs": [], "source": ["#ingested 8975, difference 8975-8677 = 298\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "id": "353683c6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060454_M0309398.jpg</td>\n", "      <td>653a4d3f4a2456004cd8a583</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060452_M0309397.jpg</td>\n", "      <td>653a4d414a2456004cd8a584</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060559_M0309430.jpg</td>\n", "      <td>653a4d444a2456004cd8a585</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060611_M0309436.jpg</td>\n", "      <td>653a4d474a2456004cd8a586</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SH01-L_85</td>\n", "      <td>653a60f36f30500045ef6b69</td>\n", "      <td>KES22_IIM-L_20220904A-060615_M0309438.jpg</td>\n", "      <td>653a4d494a2456004cd8a587</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Task Name                   Task ID  \\\n", "0  SH01-L_85  653a60f36f30500045ef6b69   \n", "1  SH01-L_85  653a60f36f30500045ef6b69   \n", "2  SH01-L_85  653a60f36f30500045ef6b69   \n", "3  SH01-L_85  653a60f36f30500045ef6b69   \n", "4  SH01-L_85  653a60f36f30500045ef6b69   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_IIM-L_20220904A-060454_M0309398.jpg  653a4d3f4a2456004cd8a583   \n", "1  KES22_IIM-L_20220904A-060452_M0309397.jpg  653a4d414a2456004cd8a584   \n", "2  KES22_IIM-L_20220904A-060559_M0309430.jpg  653a4d444a2456004cd8a585   \n", "3  KES22_IIM-L_20220904A-060611_M0309436.jpg  653a4d474a2456004cd8a586   \n", "4  KES22_IIM-L_20220904A-060615_M0309438.jpg  653a4d494a2456004cd8a587   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                False   \n", "1          4672         7008             NaN                False   \n", "2          4672         7008             NaN                False   \n", "3          4672         7008             NaN                False   \n", "4          4672         7008             NaN                False   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg = pd.read_csv(homedir+\"/scoutexports/SHscout-export-images.csv\")\n", "shimg.head()"]}, {"cell_type": "code", "execution_count": 9, "id": "12b4f764", "metadata": {}, "outputs": [{"data": {"text/plain": ["(5581, 11)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg.shape"]}, {"cell_type": "code", "execution_count": 10, "id": "74b9366b", "metadata": {}, "outputs": [{"data": {"text/plain": ["5232"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["shimg[\"Image Filename\"].nunique()  #difference 38 with ingested"]}, {"cell_type": "code", "execution_count": null, "id": "6950d573", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}