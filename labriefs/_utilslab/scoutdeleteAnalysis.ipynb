{"cells": [{"cell_type": "code", "execution_count": 1, "id": "44beacae", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "d9fa3b83", "metadata": {}, "outputs": [], "source": ["homedir=\"/home/<USER>/Dropbox/Conservation/MWSLab-2023\""]}, {"cell_type": "markdown", "id": "ec5fa45a", "metadata": {}, "source": ["1. The complete list of images in `/nas/.scout-hidden/images` that was exported to `filelistscout.csv`"]}, {"cell_type": "code", "execution_count": 5, "id": "dabbe268", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1692193522043.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1692193526742.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1692193531544.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1692193541212.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1692193545986.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            filename\n", "0  1692193522043.jpg\n", "1  1692193526742.jpg\n", "2  1692193531544.jpg\n", "3  1692193541212.jpg\n", "4  1692193545986.jpg"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["#list of all images \n", "imagelistdf = pd.read_csv(homedir+\"/labriefs/_media/filelistscout11Dec.csv\")\n", "imagelistdf.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "82c6e2c4", "metadata": {}, "outputs": [{"data": {"text/plain": ["filename    33665\n", "dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["#total no of images in scout-hidden\n", "imagelistdf.count()"]}, {"cell_type": "markdown", "id": "6d574461", "metadata": {}, "source": ["2. View the images list from Export CSV image data of all tasks from Scout\n", "    "]}, {"cell_type": "code", "execution_count": 5, "id": "01380143", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AllImages</td>\n", "      <td>6548a375492c290045f0b7b4</td>\n", "      <td>DSC03452.jpg</td>\n", "      <td>64dcd2f2e31598004c4c378d</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AllImages</td>\n", "      <td>6548a375492c290045f0b7b4</td>\n", "      <td>DSC03462.jpg</td>\n", "      <td>64dcd2f6e31598004c4c378e</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AllImages</td>\n", "      <td>6548a375492c290045f0b7b4</td>\n", "      <td>DSC03822.jpg</td>\n", "      <td>64dcd2fbe31598004c4c378f</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AllImages</td>\n", "      <td>6548a375492c290045f0b7b4</td>\n", "      <td>DSC03852.jpg</td>\n", "      <td>64dcd305e31598004c4c3791</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AllImages</td>\n", "      <td>6548a375492c290045f0b7b4</td>\n", "      <td>DSC03842.jpg</td>\n", "      <td>64dcd30ae31598004c4c3792</td>\n", "      <td>6336</td>\n", "      <td>9504</td>\n", "      <td>NaN</td>\n", "      <td>False</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Task Name                   Task ID Image Filename  \\\n", "0  AllImages  6548a375492c290045f0b7b4   DSC03452.jpg   \n", "1  AllImages  6548a375492c290045f0b7b4   DSC03462.jpg   \n", "2  AllImages  6548a375492c290045f0b7b4   DSC03822.jpg   \n", "3  AllImages  6548a375492c290045f0b7b4   DSC03852.jpg   \n", "4  AllImages  6548a375492c290045f0b7b4   DSC03842.jpg   \n", "\n", "                   Image ID  Image Height  Image Width  WIC Confidence  \\\n", "0  64dcd2f2e31598004c4c378d          6336         9504             NaN   \n", "1  64dcd2f6e31598004c4c378e          6336         9504             NaN   \n", "2  64dcd2fbe31598004c4c378f          6336         9504             NaN   \n", "3  64dcd305e31598004c4c3791          6336         9504             NaN   \n", "4  64dcd30ae31598004c4c3792          6336         9504             NaN   \n", "\n", "   Ground Truth Status Exclusion Side  Inclusion Top X Fraction  \\\n", "0                False          right                       NaN   \n", "1                False          right                       NaN   \n", "2                False          right                       NaN   \n", "3                False          right                       NaN   \n", "4                False          right                       NaN   \n", "\n", "   Inclusion Bottom X Fraction  \n", "0                          NaN  \n", "1                          NaN  \n", "2                          NaN  \n", "3                          NaN  \n", "4                          NaN  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# exported CSV image data\n", "scoutdatadf = pd.read_csv(homedir+ \"/_media/allscout-export-images.csv\")\n", "scoutdatadf.head()\n"]}, {"cell_type": "code", "execution_count": 6, "id": "d6e7cdc6", "metadata": {}, "outputs": [{"data": {"text/plain": ["Task Name                      31226\n", "Task ID                        31226\n", "Image Filename                 31226\n", "Image ID                       31226\n", "Image Height                   31226\n", "Image Width                    31226\n", "WIC Confidence                     0\n", "Ground Truth Status            31226\n", "Exclusion Side                 31226\n", "Inclusion Top X Fraction           0\n", "Inclusion Bottom X Fraction        0\n", "dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["#count of scoutdb images\n", "scoutdatadf.count()"]}, {"cell_type": "code", "execution_count": 8, "id": "e98e1503", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>originalFilename</th>\n", "      <th>filename</th>\n", "      <th>taskIds</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'$oid': '64dcd2f2e31598004c4c378d'}</td>\n", "      <td>DSC03452</td>\n", "      <td>1692193522043.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>{'$oid': '64dcd2f6e31598004c4c378e'}</td>\n", "      <td>DSC03462</td>\n", "      <td>1692193526742.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>{'$oid': '64dcd2fbe31598004c4c378f'}</td>\n", "      <td>DSC03822</td>\n", "      <td>1692193531544.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>{'$oid': '64dcd305e31598004c4c3791'}</td>\n", "      <td>DSC03852</td>\n", "      <td>1692193541212.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>{'$oid': '64dcd30ae31598004c4c3792'}</td>\n", "      <td>DSC03842</td>\n", "      <td>1692193545986.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                    _id originalFilename           filename  \\\n", "0  {'$oid': '64dcd2f2e31598004c4c378d'}         DSC03452  1692193522043.jpg   \n", "1  {'$oid': '64dcd2f6e31598004c4c378e'}         DSC03462  1692193526742.jpg   \n", "2  {'$oid': '64dcd2fbe31598004c4c378f'}         DSC03822  1692193531544.jpg   \n", "3  {'$oid': '64dcd305e31598004c4c3791'}         DSC03852  1692193541212.jpg   \n", "4  {'$oid': '64dcd30ae31598004c4c3792'}         DSC03842  1692193545986.jpg   \n", "\n", "                                             taskIds  \n", "0  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "1  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "2  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "3  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "4  [653d8463c6072200454e977f, 6540a9478a297200456...  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["#JSON mongodb data\n", "dbscoutdatadf = pd.read_json(homedir+\"/labriefs/_media/imagesdbdata11dec.json\")\n", "dbscoutdatadf.head()"]}, {"cell_type": "code", "execution_count": 9, "id": "d1c092c3", "metadata": {}, "outputs": [{"data": {"text/plain": ["_id                 17189\n", "originalFilename    17189\n", "filename            17189\n", "taskIds             17000\n", "dtype: int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["#count of scoutdb images\n", "dbscoutdatadf.count()"]}, {"cell_type": "code", "execution_count": null, "id": "17117e96", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "ce40ecbc", "metadata": {}, "source": ["\n", "3. Compare dbscoutdatadf and imagelistdf to filter of the images still associated with active Tasks in Scout\n", "- Filter images that are not associated to tasks"]}, {"cell_type": "code", "execution_count": 10, "id": "6e1e4391", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["filename            33665\n", "deleteimg_img1      33665\n", "_id                 17189\n", "originalFilename    17189\n", "taskIds             17000\n", "deleteimg_img2      17189\n", "dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "      <th>deleteimg_img1</th>\n", "      <th>_id</th>\n", "      <th>originalFilename</th>\n", "      <th>taskIds</th>\n", "      <th>deleteimg_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1692193522043.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd2f2e31598004c4c378d'}</td>\n", "      <td>DSC03452</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1692193526742.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd2f6e31598004c4c378e'}</td>\n", "      <td>DSC03462</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1692193531544.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd2fbe31598004c4c378f'}</td>\n", "      <td>DSC03822</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1692193541212.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd305e31598004c4c3791'}</td>\n", "      <td>DSC03852</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1692193545986.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd30ae31598004c4c3792'}</td>\n", "      <td>DSC03842</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            filename deleteimg_img1                                   _id  \\\n", "0  1692193522043.jpg       imageid1  {'$oid': '64dcd2f2e31598004c4c378d'}   \n", "1  1692193526742.jpg       imageid1  {'$oid': '64dcd2f6e31598004c4c378e'}   \n", "2  1692193531544.jpg       imageid1  {'$oid': '64dcd2fbe31598004c4c378f'}   \n", "3  1692193541212.jpg       imageid1  {'$oid': '64dcd305e31598004c4c3791'}   \n", "4  1692193545986.jpg       imageid1  {'$oid': '64dcd30ae31598004c4c3792'}   \n", "\n", "  originalFilename                                            taskIds  \\\n", "0         DSC03452  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "1         DSC03462  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "2         DSC03822  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "3         DSC03852  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "4         DSC03842  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "\n", "  deleteimg_img2  \n", "0       imageid2  \n", "1       imageid2  \n", "2       imageid2  \n", "3       imageid2  \n", "4       imageid2  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["#compare dataframes to find files to delete\n", "# 33665 (scout hidden) - 17189 (in tasks) = 16476\n", "\n", "imagelistdf['deleteimg'] ='imageid1'\n", "dbscoutdatadf['deleteimg'] = 'imageid2'\n", "\n", "merged_df = imagelistdf.merge(dbscoutdatadf, on='filename',how='left', suffixes=('_img1','_img2'))\n", "print(merged_df.count())\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": 11, "id": "bfe6c018", "metadata": {}, "outputs": [{"data": {"text/plain": ["filename            16476\n", "deleteimg_img1      16476\n", "_id                     0\n", "originalFilename        0\n", "taskIds                 0\n", "deleteimg_img2          0\n", "dtype: int64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["rows_not_in_tasks = merged_df[merged_df['deleteimg_img2'].isna()]\n", "\n", "\n", "rows_not_in_tasks.count()"]}, {"cell_type": "code", "execution_count": 12, "id": "9409ae67", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "      <th>deleteimg_img1</th>\n", "      <th>_id</th>\n", "      <th>originalFilename</th>\n", "      <th>taskIds</th>\n", "      <th>deleteimg_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1526</th>\n", "      <td>1697784603801.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1527</th>\n", "      <td>1697784639739.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1529</th>\n", "      <td>1697786419640.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1531</th>\n", "      <td>1697786449619.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1533</th>\n", "      <td>1697786895125.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               filename deleteimg_img1  _id originalFilename taskIds  \\\n", "1526  1697784603801.jpg       imageid1  NaN              NaN     NaN   \n", "1527  1697784639739.jpg       imageid1  NaN              NaN     NaN   \n", "1529  1697786419640.jpg       imageid1  NaN              NaN     NaN   \n", "1531  1697786449619.jpg       imageid1  NaN              NaN     NaN   \n", "1533  1697786895125.jpg       imageid1  NaN              NaN     NaN   \n", "\n", "     deleteimg_img2  \n", "1526            NaN  \n", "1527            NaN  \n", "1529            NaN  \n", "1531            NaN  \n", "1533            NaN  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["rows_not_in_tasks.head()"]}, {"cell_type": "code", "execution_count": 13, "id": "fbb3f104", "metadata": {}, "outputs": [], "source": ["#save the filenames to delete\n", "rows_not_in_tasks['filename'].to_csv(homedir+\"/labriefs/_media/filestodelete11Dec.csv\",header=None,index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "6d2337aa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "7f6b507a", "metadata": {}, "source": ["### Deleting DAN and SH-B Blocks 17/11/23"]}, {"cell_type": "code", "execution_count": 6, "id": "37a5eed8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1692193522043.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1692193526742.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1692193531544.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1692193541212.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1692193545986.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            filename\n", "0  1692193522043.jpg\n", "1  1692193526742.jpg\n", "2  1692193531544.jpg\n", "3  1692193541212.jpg\n", "4  1692193545986.jpg"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["#list of all images in scout-hidden\n", "imagelistdf = pd.read_csv(homedir+\"/labriefs/_media/filelistscout17nov.csv\")\n", "imagelistdf.head()"]}, {"cell_type": "code", "execution_count": 7, "id": "5b2a95b5", "metadata": {}, "outputs": [{"data": {"text/plain": ["(40278, 1)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["imagelistdf.shape  #40,278 images on scout-hidden"]}, {"cell_type": "code", "execution_count": 12, "id": "32168afe", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>originalFilename</th>\n", "      <th>filename</th>\n", "      <th>taskIds</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'$oid': '64dcd2f2e31598004c4c378d'}</td>\n", "      <td>DSC03452</td>\n", "      <td>1692193522043.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>{'$oid': '64dcd2f6e31598004c4c378e'}</td>\n", "      <td>DSC03462</td>\n", "      <td>1692193526742.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>{'$oid': '64dcd2fbe31598004c4c378f'}</td>\n", "      <td>DSC03822</td>\n", "      <td>1692193531544.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>{'$oid': '64dcd305e31598004c4c3791'}</td>\n", "      <td>DSC03852</td>\n", "      <td>1692193541212.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>{'$oid': '64dcd30ae31598004c4c3792'}</td>\n", "      <td>DSC03842</td>\n", "      <td>1692193545986.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                    _id originalFilename           filename  \\\n", "0  {'$oid': '64dcd2f2e31598004c4c378d'}         DSC03452  1692193522043.jpg   \n", "1  {'$oid': '64dcd2f6e31598004c4c378e'}         DSC03462  1692193526742.jpg   \n", "2  {'$oid': '64dcd2fbe31598004c4c378f'}         DSC03822  1692193531544.jpg   \n", "3  {'$oid': '64dcd305e31598004c4c3791'}         DSC03852  1692193541212.jpg   \n", "4  {'$oid': '64dcd30ae31598004c4c3792'}         DSC03842  1692193545986.jpg   \n", "\n", "                                             taskIds  \n", "0  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "1  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "2  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "3  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "4  [653d8463c6072200454e977f, 6540a9478a297200456...  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["#JSON mongodb data - images with tasks\n", "dbscoutdatadf = pd.read_json(homedir+\"/labriefs/_media/imagesdbdata17nov.json\")\n", "dbscoutdatadf.head()"]}, {"cell_type": "code", "execution_count": 13, "id": "d8b8cca7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(26746, 4)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["dbscoutdatadf.shape"]}, {"cell_type": "code", "execution_count": 14, "id": "5fbb6875", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["filename            40278\n", "deleteimg_img1      40278\n", "_id                 26746\n", "originalFilename    26746\n", "taskIds             26571\n", "deleteimg_img2      26746\n", "dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "      <th>deleteimg_img1</th>\n", "      <th>_id</th>\n", "      <th>originalFilename</th>\n", "      <th>taskIds</th>\n", "      <th>deleteimg_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1692193522043.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd2f2e31598004c4c378d'}</td>\n", "      <td>DSC03452</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1692193526742.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd2f6e31598004c4c378e'}</td>\n", "      <td>DSC03462</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1692193531544.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd2fbe31598004c4c378f'}</td>\n", "      <td>DSC03822</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1692193541212.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd305e31598004c4c3791'}</td>\n", "      <td>DSC03852</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1692193545986.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd30ae31598004c4c3792'}</td>\n", "      <td>DSC03842</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            filename deleteimg_img1                                   _id  \\\n", "0  1692193522043.jpg       imageid1  {'$oid': '64dcd2f2e31598004c4c378d'}   \n", "1  1692193526742.jpg       imageid1  {'$oid': '64dcd2f6e31598004c4c378e'}   \n", "2  1692193531544.jpg       imageid1  {'$oid': '64dcd2fbe31598004c4c378f'}   \n", "3  1692193541212.jpg       imageid1  {'$oid': '64dcd305e31598004c4c3791'}   \n", "4  1692193545986.jpg       imageid1  {'$oid': '64dcd30ae31598004c4c3792'}   \n", "\n", "  originalFilename                                            taskIds  \\\n", "0         DSC03452  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "1         DSC03462  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "2         DSC03822  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "3         DSC03852  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "4         DSC03842  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "\n", "  deleteimg_img2  \n", "0       imageid2  \n", "1       imageid2  \n", "2       imageid2  \n", "3       imageid2  \n", "4       imageid2  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["#compare dataframes to find files to delete\n", "# 40278 (scout hidden) - 26746 (in tasks) = 13532\n", "\n", "imagelistdf['deleteimg'] ='imageid1'\n", "dbscoutdatadf['deleteimg'] = 'imageid2'\n", "\n", "merged_df = imagelistdf.merge(dbscoutdatadf, on='filename',how='left', suffixes=('_img1','_img2'))\n", "print(merged_df.count())\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": 15, "id": "7a1fdc7e", "metadata": {}, "outputs": [{"data": {"text/plain": ["(13532, 6)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["rows_not_in_tasks = merged_df[merged_df['deleteimg_img2'].isna()]\n", "\n", "\n", "rows_not_in_tasks.shape"]}, {"cell_type": "code", "execution_count": 16, "id": "30604133", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "      <th>deleteimg_img1</th>\n", "      <th>_id</th>\n", "      <th>originalFilename</th>\n", "      <th>taskIds</th>\n", "      <th>deleteimg_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1526</th>\n", "      <td>1697698624702.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1527</th>\n", "      <td>1697698638160.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1528</th>\n", "      <td>1697698657473.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1529</th>\n", "      <td>1697698660140.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1530</th>\n", "      <td>1697698662851.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               filename deleteimg_img1  _id originalFilename taskIds  \\\n", "1526  1697698624702.jpg       imageid1  NaN              NaN     NaN   \n", "1527  1697698638160.jpg       imageid1  NaN              NaN     NaN   \n", "1528  1697698657473.jpg       imageid1  NaN              NaN     NaN   \n", "1529  1697698660140.jpg       imageid1  NaN              NaN     NaN   \n", "1530  1697698662851.jpg       imageid1  NaN              NaN     NaN   \n", "\n", "     deleteimg_img2  \n", "1526            NaN  \n", "1527            NaN  \n", "1528            NaN  \n", "1529            NaN  \n", "1530            NaN  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["rows_not_in_tasks.head()"]}, {"cell_type": "code", "execution_count": 17, "id": "ee8d0883", "metadata": {}, "outputs": [], "source": ["#save the filenames to delete\n", "rows_not_in_tasks['filename'].to_csv(homedir+\"/labriefs/_media/filestodelete17nov.csv\",header=None,index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "3ad4e5fd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "7e2768f0", "metadata": {}, "source": ["## Deleting MT Block"]}, {"cell_type": "code", "execution_count": 4, "id": "2a03bf1a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1692193522043.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1692193526742.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1692193531544.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1692193541212.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1692193545986.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            filename\n", "0  1692193522043.jpg\n", "1  1692193526742.jpg\n", "2  1692193531544.jpg\n", "3  1692193541212.jpg\n", "4  1692193545986.jpg"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["#list of all images \n", "imagelistdf = pd.read_csv(homedir+\"/labriefs/_media/filelistscout01Dec.csv\")\n", "imagelistdf.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "89749abe", "metadata": {}, "outputs": [{"data": {"text/plain": ["filename    41227\n", "dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["#total no of images in scout-hidden\n", "imagelistdf.count()"]}, {"cell_type": "code", "execution_count": 9, "id": "da63deae", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>originalFilename</th>\n", "      <th>filename</th>\n", "      <th>taskIds</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'$oid': '64dcd2f2e31598004c4c378d'}</td>\n", "      <td>DSC03452</td>\n", "      <td>1692193522043.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>{'$oid': '64dcd2f6e31598004c4c378e'}</td>\n", "      <td>DSC03462</td>\n", "      <td>1692193526742.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>{'$oid': '64dcd2fbe31598004c4c378f'}</td>\n", "      <td>DSC03822</td>\n", "      <td>1692193531544.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>{'$oid': '64dcd305e31598004c4c3791'}</td>\n", "      <td>DSC03852</td>\n", "      <td>1692193541212.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>{'$oid': '64dcd30ae31598004c4c3792'}</td>\n", "      <td>DSC03842</td>\n", "      <td>1692193545986.jpg</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                    _id originalFilename           filename  \\\n", "0  {'$oid': '64dcd2f2e31598004c4c378d'}         DSC03452  1692193522043.jpg   \n", "1  {'$oid': '64dcd2f6e31598004c4c378e'}         DSC03462  1692193526742.jpg   \n", "2  {'$oid': '64dcd2fbe31598004c4c378f'}         DSC03822  1692193531544.jpg   \n", "3  {'$oid': '64dcd305e31598004c4c3791'}         DSC03852  1692193541212.jpg   \n", "4  {'$oid': '64dcd30ae31598004c4c3792'}         DSC03842  1692193545986.jpg   \n", "\n", "                                             taskIds  \n", "0  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "1  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "2  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "3  [653d8463c6072200454e977f, 6540a9478a297200456...  \n", "4  [653d8463c6072200454e977f, 6540a9478a297200456...  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["#JSON mongodb data\n", "dbscoutdatadf = pd.read_json(homedir+\"/labriefs/_media/imagesdbdataDec1.json\")\n", "dbscoutdatadf.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "9e09c2e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["_id                 32487\n", "originalFilename    32487\n", "filename            32487\n", "taskIds             32135\n", "dtype: int64"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["#count of scoutdb images\n", "dbscoutdatadf.count()"]}, {"cell_type": "code", "execution_count": 11, "id": "5e7d0d3a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["filename            41227\n", "deleteimg_img1      41227\n", "_id                 32487\n", "originalFilename    32487\n", "taskIds             32135\n", "deleteimg_img2      32487\n", "dtype: int64\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "      <th>deleteimg_img1</th>\n", "      <th>_id</th>\n", "      <th>originalFilename</th>\n", "      <th>taskIds</th>\n", "      <th>deleteimg_img2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1692193522043.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd2f2e31598004c4c378d'}</td>\n", "      <td>DSC03452</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1692193526742.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd2f6e31598004c4c378e'}</td>\n", "      <td>DSC03462</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1692193531544.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd2fbe31598004c4c378f'}</td>\n", "      <td>DSC03822</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1692193541212.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd305e31598004c4c3791'}</td>\n", "      <td>DSC03852</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1692193545986.jpg</td>\n", "      <td>imageid1</td>\n", "      <td>{'$oid': '64dcd30ae31598004c4c3792'}</td>\n", "      <td>DSC03842</td>\n", "      <td>[653d8463c6072200454e977f, 6540a9478a297200456...</td>\n", "      <td>imageid2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            filename deleteimg_img1                                   _id  \\\n", "0  1692193522043.jpg       imageid1  {'$oid': '64dcd2f2e31598004c4c378d'}   \n", "1  1692193526742.jpg       imageid1  {'$oid': '64dcd2f6e31598004c4c378e'}   \n", "2  1692193531544.jpg       imageid1  {'$oid': '64dcd2fbe31598004c4c378f'}   \n", "3  1692193541212.jpg       imageid1  {'$oid': '64dcd305e31598004c4c3791'}   \n", "4  1692193545986.jpg       imageid1  {'$oid': '64dcd30ae31598004c4c3792'}   \n", "\n", "  originalFilename                                            taskIds  \\\n", "0         DSC03452  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "1         DSC03462  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "2         DSC03822  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "3         DSC03852  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "4         DSC03842  [653d8463c6072200454e977f, 6540a9478a297200456...   \n", "\n", "  deleteimg_img2  \n", "0       imageid2  \n", "1       imageid2  \n", "2       imageid2  \n", "3       imageid2  \n", "4       imageid2  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["#compare dataframes to find files to delete\n", "# 41227 (scout hidden) - 32487 (in tasks) = 8740\n", "\n", "imagelistdf['deleteimg'] ='imageid1'\n", "dbscoutdatadf['deleteimg'] = 'imageid2'\n", "\n", "merged_df = imagelistdf.merge(dbscoutdatadf, on='filename',how='left', suffixes=('_img1','_img2'))\n", "print(merged_df.count())\n", "merged_df.head()"]}, {"cell_type": "code", "execution_count": 12, "id": "221520f4", "metadata": {}, "outputs": [{"data": {"text/plain": ["filename            8740\n", "deleteimg_img1      8740\n", "_id                    0\n", "originalFilename       0\n", "taskIds                0\n", "deleteimg_img2         0\n", "dtype: int64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["rows_not_in_tasks = merged_df[merged_df['deleteimg_img2'].isna()]\n", "\n", "\n", "rows_not_in_tasks.count()"]}, {"cell_type": "code", "execution_count": 15, "id": "068d948d", "metadata": {}, "outputs": [], "source": ["#save the filenames to delete\n", "rows_not_in_tasks['filename'].to_csv(homedir+\"/labriefs/_media/filestodeleteDec1.csv\",header=None,index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "abc32989", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "013fecce", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "80413c9e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.15"}}, "nbformat": 4, "nbformat_minor": 5}