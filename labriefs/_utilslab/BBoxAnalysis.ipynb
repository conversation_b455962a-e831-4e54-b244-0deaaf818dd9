{"cells": [{"cell_type": "markdown", "id": "52a621fb", "metadata": {}, "source": ["### DAN transects 01 to 25 (L &R)\n", "#### Session 20220906A\n", "#### Bounding box analysis for each species"]}, {"cell_type": "code", "execution_count": 1, "id": "24d16aba", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "8424729e", "metadata": {}, "outputs": [], "source": ["homedir = \"/home/<USER>/Dropbox/Conservation/MWSLab-2023\""]}, {"cell_type": "code", "execution_count": 2, "id": "00a1b890-2fc1-4e5d-92da-0d5bb5e2cd92", "metadata": {}, "outputs": [], "source": ["homedir = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023\""]}, {"cell_type": "code", "execution_count": 19, "id": "a0022119", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DAN05-L_174</th>\n", "      <th>6530de5f05667e0045cadc4e</th>\n", "      <th>KES22_IIM-L_20220906A-062714_M0309369.jpg</th>\n", "      <th>6530d51b0e8be9004ca31586</th>\n", "      <th>6351.04301708898</th>\n", "      <th>1166.6234531526222</th>\n", "      <th>60.22392457277549</th>\n", "      <th>46.458456098998234</th>\n", "      <th>vervet monkey</th>\n", "      <th>Unnamed: 9</th>\n", "      <th>annotator1</th>\n", "      <th>1697720924067</th>\n", "      <th>false</th>\n", "      <th>FALSE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6482.702368</td>\n", "      <td>1211.366282</td>\n", "      <td>41.640716</td>\n", "      <td>35.962436</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6102.257645</td>\n", "      <td>1084.551374</td>\n", "      <td>43.533476</td>\n", "      <td>28.391397</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6230.965313</td>\n", "      <td>1126.192090</td>\n", "      <td>39.747956</td>\n", "      <td>26.498637</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6405.099216</td>\n", "      <td>1175.403845</td>\n", "      <td>-39.747956</td>\n", "      <td>30.284157</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>3742.007804</td>\n", "      <td>702.179957</td>\n", "      <td>32.707261</td>\n", "      <td>21.330823</td>\n", "      <td>baboon</td>\n", "      <td>NaN</td>\n", "      <td>pendogt</td>\n", "      <td>1699018262428</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     DAN05-L_174  6530de5f05667e0045cadc4e  \\\n", "0  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "1  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "2  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "3  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "4  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "\n", "   KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586  \\\n", "0  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "1  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "2  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "3  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "4  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "\n", "   6351.04301708898  1166.6234531526222  60.22392457277549  \\\n", "0       6482.702368         1211.366282          41.640716   \n", "1       6102.257645         1084.551374          43.533476   \n", "2       6230.965313         1126.192090          39.747956   \n", "3       6405.099216         1175.403845         -39.747956   \n", "4       3742.007804          702.179957          32.707261   \n", "\n", "   46.458456098998234  vervet monkey  Unnamed: 9 annotator1  1697720924067  \\\n", "0           35.962436  vervet monkey         NaN    wmagesa  1697717018183   \n", "1           28.391397  vervet monkey         NaN    wmagesa  1697717018183   \n", "2           26.498637  vervet monkey         NaN    wmagesa  1697717018183   \n", "3           30.284157  vervet monkey         NaN    wmagesa  1697717018183   \n", "4           21.330823         baboon         NaN    pendogt  1699018262428   \n", "\n", "   false  FALSE  \n", "0  False  False  \n", "1  False  False  \n", "2  False  False  \n", "3  False  False  \n", "4   True  False  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["dandf = pd.read_csv(homedir+\"/scoutexports/DANscout-export-annotations.csv\")\n", "dandf.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "ceb82f1b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1838, 14)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["dandf.shape"]}, {"cell_type": "code", "execution_count": 20, "id": "53b9717b", "metadata": {}, "outputs": [], "source": ["dandf.columns=['TaskName', 'TaskID', 'ImageFilename', 'ImageID', 'BoxX', 'BoxY',\n", "       'BoxW', 'BoxH', 'Label', 'LabelConfidence', 'Assignee', 'Timestamp',\n", "       'IsGroundTruth', 'ExcludedByLine']"]}, {"cell_type": "code", "execution_count": 21, "id": "be1eea4c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6482.702368</td>\n", "      <td>1211.366282</td>\n", "      <td>41.640716</td>\n", "      <td>35.962436</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6102.257645</td>\n", "      <td>1084.551374</td>\n", "      <td>43.533476</td>\n", "      <td>28.391397</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6230.965313</td>\n", "      <td>1126.192090</td>\n", "      <td>39.747956</td>\n", "      <td>26.498637</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6405.099216</td>\n", "      <td>1175.403845</td>\n", "      <td>-39.747956</td>\n", "      <td>30.284157</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>3742.007804</td>\n", "      <td>702.179957</td>\n", "      <td>32.707261</td>\n", "      <td>21.330823</td>\n", "      <td>baboon</td>\n", "      <td>NaN</td>\n", "      <td>pendogt</td>\n", "      <td>1699018262428</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        TaskName                    TaskID  \\\n", "0  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "1  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "2  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "3  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "4  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "\n", "                               ImageFilename                   ImageID  \\\n", "0  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "1  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "2  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "3  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "4  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "\n", "          BoxX         BoxY       BoxW       BoxH          Label  \\\n", "0  6482.702368  1211.366282  41.640716  35.962436  vervet monkey   \n", "1  6102.257645  1084.551374  43.533476  28.391397  vervet monkey   \n", "2  6230.965313  1126.192090  39.747956  26.498637  vervet monkey   \n", "3  6405.099216  1175.403845 -39.747956  30.284157  vervet monkey   \n", "4  3742.007804   702.179957  32.707261  21.330823         baboon   \n", "\n", "   LabelConfidence Assignee      Timestamp  IsGroundTruth ExcludedByLine  \n", "0              NaN  wmagesa  1697717018183          False          False  \n", "1              NaN  wmagesa  1697717018183          False          False  \n", "2              NaN  wmagesa  1697717018183          False          False  \n", "3              NaN  wmagesa  1697717018183          False          False  \n", "4              NaN  pendogt  1699018262428           True          False  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["dandf.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "5b4e470b", "metadata": {}, "outputs": [], "source": ["dantasks=dandf['TaskName'].unique()"]}, {"cell_type": "code", "execution_count": 11, "id": "9433d4a7", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["array(['DAN05-L_174_2', 'DAN05-L_201_2', 'DAN05-R_58', 'DAN05-R_58_2',\n", "       'DAN05-R_116', 'DAN05-R_116_2', 'DAN05-R_174', 'DAN05-R_174_2',\n", "       'DAN05-R_201', 'DAN05-R_201_2', 'DAN05-L_100', 'DAN05-L_200',\n", "       'DAN05-L_600', 'DAN05-L_700', 'DAN05-L_900', 'DAN05-L_100_2',\n", "       'DAN05-L_200_2', 'DAN05-L_600_2', 'DAN05-L_700_2', 'DAN05-L_900_2',\n", "       'DAN15-L_128', 'DAN16-L_256', 'DAN17-L_120', 'DAN17-L_241',\n", "       'DAN18-L_228', 'DAN19-L_123', 'DAN19-L_246', 'DAN16-L_256_2',\n", "       'DAN17-L_120_2', 'DAN17-L_241_2', 'DAN18-L_114_2', 'DAN15-L_128_L',\n", "       'DAN19-L_123_2', 'DAN19-L_246_2', 'DAN20-L', 'DAN20-L_2',\n", "       'DAN21-L_091_2', 'DAN10-L_070', 'DAN10-L_071_140',\n", "       'DAN10-L_071_280', 'DAN11-L_072_124', 'DAN11-L_072_248',\n", "       'DAN12-L_073_101', 'DAN12-L_073_221', 'DAN12-L_074_149',\n", "       'DAN13-L_074_240', 'DAN13-L_075_227', 'DAN01-L', 'DAN02-L',\n", "       'DAN03-L_060_120', 'DAN03-L_061_72', 'DAN06-L_115', 'DAN06-L_230',\n", "       'DAN07-L_128', 'DAN09-L_174', 'DAN11-L_072_124_2',\n", "       'DAN12-L_073_101_2', 'DAN12-L_073_221_2', 'DAN12-L_074_149_2',\n", "       'DAN06-L_115_2', 'DAN10-L_070_2', 'DAN10-L_071_140_2',\n", "       'DAN10-L_071_280_2', 'DAN03-R_061_72', 'DAN04-R', 'DAN06-R_115',\n", "       'DAN06-R_230', 'DAN07-R_128', 'DAN07-R_255', 'DAN08-R',\n", "       'DAN09-R_148', 'DAN09-R_174', 'DAN10-R_070', 'DAN10-R_071_140',\n", "       'DAN10-R_071_280', 'DAN11-R_072_124', 'DAN12-R_073_172',\n", "       'DAN12-R_074_149', 'DAN13-R_074_240', 'DAN14-R_253', 'DAN05-R_100',\n", "       'DAN01-R', 'DAN02-R', 'DAN16-R_133', 'DAN17-R_126', 'DAN17-R_242',\n", "       'DAN18-R_085_50', 'DAN18-R_085_90', 'DAN19-R_085_173',\n", "       'DAN20-R_09_71', 'DAN20-R_09_121', 'DAN25-R_09_441', 'DAN08-L',\n", "       'DAN09-L_148'], dtype=object)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["dantasks"]}, {"cell_type": "code", "execution_count": 12, "id": "aff51fb5", "metadata": {}, "outputs": [], "source": ["dandftasks =pd.DataFrame(dantasks)"]}, {"cell_type": "code", "execution_count": 13, "id": "dc03c6fc", "metadata": {}, "outputs": [], "source": ["dandftasks.to_csv(homedir+\"/labriefs/_media/DANTasks.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "fbbdb6e0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "id": "1363fb08", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-R_58_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-R_116_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-R_174_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-R_201_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_100_2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        TaskName\n", "0   DAN05-R_58_2\n", "1  DAN05-R_116_2\n", "2  DAN05-R_174_2\n", "3  DAN05-R_201_2\n", "4  DAN05-L_100_2"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["#the identified repeated tasks\n", "deletetasksdf = pd.read_csv(homedir+\"/labriefs/_media/deleterepeattasks.txt\")\n", "deletetasksdf.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "634a8d09", "metadata": {}, "outputs": [{"data": {"text/plain": ["(24, 1)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["deletetasksdf.shape"]}, {"cell_type": "code", "execution_count": null, "id": "497df9c8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 22, "id": "a2ca8b86", "metadata": {}, "outputs": [], "source": ["#filtering the repeated tasks from all DAN tasks\n", "\n", "# Create a new DataFrame without the records meeting the condition\n", "filteredtasks_df = dandf[~dandf[\"TaskName\"].isin(deletetasksdf[\"TaskName\"])]\n"]}, {"cell_type": "code", "execution_count": 23, "id": "f2b9dd81", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1401, 14)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["filteredtasks_df.shape  #deleted 1838-1401=437 records"]}, {"cell_type": "code", "execution_count": 24, "id": "e4bd0889", "metadata": {}, "outputs": [], "source": ["filteredtasks_df.to_csv(homedir+\"/labriefs/_media/uniqueDANtasks.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 25, "id": "5243f37a", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6482.702368</td>\n", "      <td>1211.366282</td>\n", "      <td>41.640716</td>\n", "      <td>35.962436</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6102.257645</td>\n", "      <td>1084.551374</td>\n", "      <td>43.533476</td>\n", "      <td>28.391397</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6230.965313</td>\n", "      <td>1126.192090</td>\n", "      <td>39.747956</td>\n", "      <td>26.498637</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6405.099216</td>\n", "      <td>1175.403845</td>\n", "      <td>-39.747956</td>\n", "      <td>30.284157</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>3742.007804</td>\n", "      <td>702.179957</td>\n", "      <td>32.707261</td>\n", "      <td>21.330823</td>\n", "      <td>baboon</td>\n", "      <td>NaN</td>\n", "      <td>pendogt</td>\n", "      <td>1699018262428</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        TaskName                    TaskID  \\\n", "0  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "1  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "2  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "3  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "4  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "\n", "                               ImageFilename                   ImageID  \\\n", "0  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "1  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "2  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "3  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "4  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "\n", "          BoxX         BoxY       BoxW       BoxH          Label  \\\n", "0  6482.702368  1211.366282  41.640716  35.962436  vervet monkey   \n", "1  6102.257645  1084.551374  43.533476  28.391397  vervet monkey   \n", "2  6230.965313  1126.192090  39.747956  26.498637  vervet monkey   \n", "3  6405.099216  1175.403845 -39.747956  30.284157  vervet monkey   \n", "4  3742.007804   702.179957  32.707261  21.330823         baboon   \n", "\n", "   LabelConfidence Assignee      Timestamp  IsGroundTruth ExcludedByLine  \n", "0              NaN  wmagesa  1697717018183          False          False  \n", "1              NaN  wmagesa  1697717018183          False          False  \n", "2              NaN  wmagesa  1697717018183          False          False  \n", "3              NaN  wmagesa  1697717018183          False          False  \n", "4              NaN  pendogt  1699018262428           True          False  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["bboxdf = pd.read_csv(homedir+\"/labriefs/_media/uniqueDANtasks.csv\")\n", "bboxdf.head()"]}, {"cell_type": "code", "execution_count": 26, "id": "1c5ba84b", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["elephant            1202\n", "white_bones           72\n", "baboon                20\n", "zebra                 20\n", "giraffe               14\n", "unknown mammal        14\n", "bird                  12\n", "impala                11\n", "buffalo               10\n", "vervet monkey          7\n", "kudu                   7\n", "unknown antelope       4\n", "gazelle_grants         3\n", "duiker                 2\n", "gazelle_thomsons       2\n", "steenbok               1\n", "Name: Label, dtype: int64"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["bboxdf['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 27, "id": "c8569baa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>297</th>\n", "      <td>DAN17-L_120</td>\n", "      <td>65360a7b5aee9b00457dae28</td>\n", "      <td>KES22_IIM-L_20220906A-083326_M0303143.jpg</td>\n", "      <td>653232db8cfd62004c69aae5</td>\n", "      <td>1894.420736</td>\n", "      <td>1492.546185</td>\n", "      <td>122.839614</td>\n", "      <td>131.167724</td>\n", "      <td>gazelle_grants</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1698055384082</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>729</th>\n", "      <td>DAN09-L_174</td>\n", "      <td>653780b65aee9b00457daefb</td>\n", "      <td>KES22_IIM-L_20220906A-070101_M0300380.jpg</td>\n", "      <td>65323b998cfd62004c69ae2a</td>\n", "      <td>5181.429686</td>\n", "      <td>3874.437318</td>\n", "      <td>78.859085</td>\n", "      <td>125.398872</td>\n", "      <td>gazelle_grants</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1698235551907</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1082</th>\n", "      <td>DAN07-R_255</td>\n", "      <td>6537b7d65aee9b00457daf4b</td>\n", "      <td>KES22_IIM-R_20220906A-064951_M0400427.jpg</td>\n", "      <td>6537a77726599d004cfc8cd1</td>\n", "      <td>804.104108</td>\n", "      <td>4478.161785</td>\n", "      <td>-122.813328</td>\n", "      <td>85.322944</td>\n", "      <td>gazelle_grants</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1698226691580</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         TaskName                    TaskID  \\\n", "297   DAN17-L_120  65360a7b5aee9b00457dae28   \n", "729   DAN09-L_174  653780b65aee9b00457daefb   \n", "1082  DAN07-R_255  6537b7d65aee9b00457daf4b   \n", "\n", "                                  ImageFilename                   ImageID  \\\n", "297   KES22_IIM-L_20220906A-083326_M0303143.jpg  653232db8cfd62004c69aae5   \n", "729   KES22_IIM-L_20220906A-070101_M0300380.jpg  65323b998cfd62004c69ae2a   \n", "1082  KES22_IIM-R_20220906A-064951_M0400427.jpg  6537a77726599d004cfc8cd1   \n", "\n", "             BoxX         BoxY        BoxW        BoxH           Label  \\\n", "297   1894.420736  1492.546185  122.839614  131.167724  gazelle_grants   \n", "729   5181.429686  3874.437318   78.859085  125.398872  gazelle_grants   \n", "1082   804.104108  4478.161785 -122.813328   85.322944  gazelle_grants   \n", "\n", "      LabelConfidence Assignee      Timestamp  IsGroundTruth ExcludedByLine  \n", "297               NaN  wmagesa  1698055384082          False            NaN  \n", "729               NaN  wmagesa  1698235551907          False            NaN  \n", "1082              NaN  wmagesa  1698226691580          False            NaN  "]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["bboxdf[bboxdf['Label']=='gazelle_grants']"]}, {"cell_type": "code", "execution_count": 28, "id": "1a51c6ec", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>299</th>\n", "      <td>DAN17-L_120</td>\n", "      <td>65360a7b5aee9b00457dae28</td>\n", "      <td>KES22_IIM-L_20220906A-083326_M0303143.jpg</td>\n", "      <td>653232db8cfd62004c69aae5</td>\n", "      <td>3774.954195</td>\n", "      <td>1815.723113</td>\n", "      <td>102.019341</td>\n", "      <td>135.331779</td>\n", "      <td>gazelle_thomsons</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1698055384082</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1070</th>\n", "      <td>DAN07-R_255</td>\n", "      <td>6537b7d65aee9b00457daf4b</td>\n", "      <td>KES22_IIM-R_20220906A-064646_M0400335.jpg</td>\n", "      <td>6537a4df26599d004cfc8bdf</td>\n", "      <td>6469.030476</td>\n", "      <td>226.235079</td>\n", "      <td>51.710875</td>\n", "      <td>42.661472</td>\n", "      <td>gazelle_thomsons</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1698223586777</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         TaskName                    TaskID  \\\n", "299   DAN17-L_120  65360a7b5aee9b00457dae28   \n", "1070  DAN07-R_255  6537b7d65aee9b00457daf4b   \n", "\n", "                                  ImageFilename                   ImageID  \\\n", "299   KES22_IIM-L_20220906A-083326_M0303143.jpg  653232db8cfd62004c69aae5   \n", "1070  KES22_IIM-R_20220906A-064646_M0400335.jpg  6537a4df26599d004cfc8bdf   \n", "\n", "             BoxX         BoxY        BoxW        BoxH             Label  \\\n", "299   3774.954195  1815.723113  102.019341  135.331779  gazelle_thomsons   \n", "1070  6469.030476   226.235079   51.710875   42.661472  gazelle_thomsons   \n", "\n", "      LabelConfidence Assignee      Timestamp  IsGroundTruth ExcludedByLine  \n", "299               NaN  wmagesa  1698055384082          False            NaN  \n", "1070              NaN  wmagesa  1698223586777          False            NaN  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["bboxdf[bboxdf['Label']=='gazelle_thomsons']"]}, {"cell_type": "code", "execution_count": null, "id": "c20a2366", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "4e032570", "metadata": {}, "source": ["### DAN GT - 29 TASKs"]}, {"cell_type": "code", "execution_count": 3, "id": "0fe1b143", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>DAN05-L_174</th>\n", "      <th>6530de5f05667e0045cadc4e</th>\n", "      <th>KES22_IIM-L_20220906A-062714_M0309369.jpg</th>\n", "      <th>6530d51b0e8be9004ca31586</th>\n", "      <th>6351.04301708898</th>\n", "      <th>1166.6234531526222</th>\n", "      <th>60.22392457277549</th>\n", "      <th>46.458456098998234</th>\n", "      <th>vervet monkey</th>\n", "      <th>Unnamed: 9</th>\n", "      <th>annotator1</th>\n", "      <th>1697720924067</th>\n", "      <th>false</th>\n", "      <th>FALSE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6482.702368</td>\n", "      <td>1211.366282</td>\n", "      <td>41.640716</td>\n", "      <td>35.962436</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6102.257645</td>\n", "      <td>1084.551374</td>\n", "      <td>43.533476</td>\n", "      <td>28.391397</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6230.965313</td>\n", "      <td>1126.192090</td>\n", "      <td>39.747956</td>\n", "      <td>26.498637</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>6405.099216</td>\n", "      <td>1175.403845</td>\n", "      <td>-39.747956</td>\n", "      <td>30.284157</td>\n", "      <td>vervet monkey</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1697717018183</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>DAN05-L_174_2</td>\n", "      <td>6530de9405667e0045cadc52</td>\n", "      <td>KES22_IIM-L_20220906A-062714_M0309369.jpg</td>\n", "      <td>6530d51b0e8be9004ca31586</td>\n", "      <td>3742.007804</td>\n", "      <td>702.179957</td>\n", "      <td>32.707261</td>\n", "      <td>21.330823</td>\n", "      <td>baboon</td>\n", "      <td>NaN</td>\n", "      <td>pendogt</td>\n", "      <td>1699018262428</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     DAN05-L_174  6530de5f05667e0045cadc4e  \\\n", "0  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "1  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "2  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "3  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "4  DAN05-L_174_2  6530de9405667e0045cadc52   \n", "\n", "   KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586  \\\n", "0  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "1  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "2  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "3  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "4  KES22_IIM-L_20220906A-062714_M0309369.jpg  6530d51b0e8be9004ca31586   \n", "\n", "   6351.04301708898  1166.6234531526222  60.22392457277549  \\\n", "0       6482.702368         1211.366282          41.640716   \n", "1       6102.257645         1084.551374          43.533476   \n", "2       6230.965313         1126.192090          39.747956   \n", "3       6405.099216         1175.403845         -39.747956   \n", "4       3742.007804          702.179957          32.707261   \n", "\n", "   46.458456098998234  vervet monkey  Unnamed: 9 annotator1  1697720924067  \\\n", "0           35.962436  vervet monkey         NaN    wmagesa  1697717018183   \n", "1           28.391397  vervet monkey         NaN    wmagesa  1697717018183   \n", "2           26.498637  vervet monkey         NaN    wmagesa  1697717018183   \n", "3           30.284157  vervet monkey         NaN    wmagesa  1697717018183   \n", "4           21.330823         baboon         NaN    pendogt  1699018262428   \n", "\n", "   false  FALSE  \n", "0  False  False  \n", "1  False  False  \n", "2  False  False  \n", "3  False  False  \n", "4   True  False  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["dangtdf = pd.read_csv(homedir+\"/scoutexports/DANgtscout-export-annotations.csv\")\n", "dangtdf.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "b0416e50", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1876, 14)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["dangtdf.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "451d5acc", "metadata": {}, "outputs": [], "source": ["dangtdf.columns=['TaskName', 'TaskID', 'ImageFilename', 'ImageID', 'BoxX', 'BoxY',\n", "       'BoxW', 'BoxH', 'Label', 'LabelConfidence', 'Assignee', 'Timestamp',\n", "       'IsGroundTruth', 'ExcludedByLine']"]}, {"cell_type": "code", "execution_count": 9, "id": "0ee9181f", "metadata": {}, "outputs": [], "source": ["#filtering the repeated tasks from all DAN tasks\n", "\n", "# Create a new DataFrame without the records meeting the condition\n", "filteredgttasks_df = dangtdf[~dangtdf[\"TaskName\"].isin(deletetasksdf[\"TaskName\"])]"]}, {"cell_type": "code", "execution_count": 10, "id": "c2dab7cc", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1439, 14)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["filteredgttasks_df.shape"]}, {"cell_type": "code", "execution_count": 11, "id": "9767ef81", "metadata": {}, "outputs": [{"data": {"text/plain": ["elephant            1226\n", "white_bones           82\n", "zebra                 20\n", "baboon                20\n", "unknown mammal        14\n", "giraffe               14\n", "bird                  12\n", "impala                11\n", "buffalo               10\n", "kudu                   9\n", "vervet monkey          7\n", "unknown antelope       4\n", "gazelle_grants         3\n", "sable                  2\n", "gazelle_thomsons       2\n", "duiker                 2\n", "steenbok               1\n", "Name: Label, dtype: int64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["filteredgttasks_df['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 12, "id": "e037a798", "metadata": {}, "outputs": [{"data": {"text/plain": ["1439"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["filteredgttasks_df['Label'].value_counts().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "b4a9468a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "53fa0b74", "metadata": {}, "source": ["### NG25 transects 01 to 05 (L &R)\n", "#### Session 20220923B\n", "#### Bounding box analysis for each species"]}, {"cell_type": "code", "execution_count": 61, "id": "e3d5c15c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145132_M0804846.jpg</td>\n", "      <td>6525067833c806004cc2d6b7</td>\n", "      <td>6360.960245</td>\n", "      <td>625.165623</td>\n", "      <td>54.652005</td>\n", "      <td>25.326539</td>\n", "      <td>crocodile</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696929902940</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>652506a333c806004cc2d6c7</td>\n", "      <td>193.916465</td>\n", "      <td>397.234940</td>\n", "      <td>39.958544</td>\n", "      <td>63.463570</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696932493527</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>652506a333c806004cc2d6c7</td>\n", "      <td>26.163200</td>\n", "      <td>457.388800</td>\n", "      <td>-25.228800</td>\n", "      <td>46.720000</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696932493527</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>6525065633c806004cc2d6aa</td>\n", "      <td>594.675064</td>\n", "      <td>2266.229102</td>\n", "      <td>135.741047</td>\n", "      <td>56.881963</td>\n", "      <td>hippo</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696938797992</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>6525065633c806004cc2d6aa</td>\n", "      <td>771.784811</td>\n", "      <td>2336.038783</td>\n", "      <td>127.984416</td>\n", "      <td>120.227785</td>\n", "      <td>hippo</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696938797992</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Task Name                   Task ID  \\\n", "0  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "1  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "2  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "3  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "4  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_WOT-L_20220923B-145132_M0804846.jpg  6525067833c806004cc2d6b7   \n", "1  KES22_WOT-L_20220923B-145454_M0804947.jpg  652506a333c806004cc2d6c7   \n", "2  KES22_WOT-L_20220923B-145454_M0804947.jpg  652506a333c806004cc2d6c7   \n", "3  KES22_WOT-L_20220923B-145256_M0804888.jpg  6525065633c806004cc2d6aa   \n", "4  KES22_WOT-L_20220923B-145256_M0804888.jpg  6525065633c806004cc2d6aa   \n", "\n", "         Box X        Box Y       Box W       Box H       Label  \\\n", "0  6360.960245   625.165623   54.652005   25.326539   crocodile   \n", "1   193.916465   397.234940   39.958544   63.463570  red lechwe   \n", "2    26.163200   457.388800  -25.228800   46.720000  red lechwe   \n", "3   594.675064  2266.229102  135.741047   56.881963       hippo   \n", "4   771.784811  2336.038783  127.984416  120.227785       hippo   \n", "\n", "   Label Confidence Assignee      Timestamp  Is Ground Truth  Excluded By Line  \n", "0               NaN  wmagesa  1696929902940            False               NaN  \n", "1               NaN  wmagesa  1696932493527            False               NaN  \n", "2               NaN  wmagesa  1696932493527            False               NaN  \n", "3               NaN  wmagesa  1696938797992            False               NaN  \n", "4               NaN  wmagesa  1696938797992            False               NaN  "]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25df = pd.read_csv(\"scoutexports/NG25Tasksannotations.csv\")\n", "ng25df.head()"]}, {"cell_type": "code", "execution_count": 62, "id": "22b51e0b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7142, 14)"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25df.shape"]}, {"cell_type": "code", "execution_count": 64, "id": "4ea01d3d", "metadata": {}, "outputs": [], "source": ["ng25df.columns=['TaskName', 'TaskID', 'ImageFilename', 'ImageID', 'BoxX', 'BoxY',\n", "       'BoxW', 'BoxH', 'Label', 'LabelConfidence', 'Assignee', 'Timestamp',\n", "       'IsGroundTruth', 'ExcludedByLine']"]}, {"cell_type": "code", "execution_count": 65, "id": "c1d61928", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145132_M0804846.jpg</td>\n", "      <td>6525067833c806004cc2d6b7</td>\n", "      <td>6360.960245</td>\n", "      <td>625.165623</td>\n", "      <td>54.652005</td>\n", "      <td>25.326539</td>\n", "      <td>crocodile</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696929902940</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>652506a333c806004cc2d6c7</td>\n", "      <td>193.916465</td>\n", "      <td>397.234940</td>\n", "      <td>39.958544</td>\n", "      <td>63.463570</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696932493527</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>652506a333c806004cc2d6c7</td>\n", "      <td>26.163200</td>\n", "      <td>457.388800</td>\n", "      <td>-25.228800</td>\n", "      <td>46.720000</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696932493527</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>6525065633c806004cc2d6aa</td>\n", "      <td>594.675064</td>\n", "      <td>2266.229102</td>\n", "      <td>135.741047</td>\n", "      <td>56.881963</td>\n", "      <td>hippo</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696938797992</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>6525065633c806004cc2d6aa</td>\n", "      <td>771.784811</td>\n", "      <td>2336.038783</td>\n", "      <td>127.984416</td>\n", "      <td>120.227785</td>\n", "      <td>hippo</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696938797992</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          TaskName                    TaskID  \\\n", "0  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "1  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "2  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "3  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "4  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "\n", "                               ImageFilename                   ImageID  \\\n", "0  KES22_WOT-L_20220923B-145132_M0804846.jpg  6525067833c806004cc2d6b7   \n", "1  KES22_WOT-L_20220923B-145454_M0804947.jpg  652506a333c806004cc2d6c7   \n", "2  KES22_WOT-L_20220923B-145454_M0804947.jpg  652506a333c806004cc2d6c7   \n", "3  KES22_WOT-L_20220923B-145256_M0804888.jpg  6525065633c806004cc2d6aa   \n", "4  KES22_WOT-L_20220923B-145256_M0804888.jpg  6525065633c806004cc2d6aa   \n", "\n", "          BoxX         BoxY        BoxW        BoxH       Label  \\\n", "0  6360.960245   625.165623   54.652005   25.326539   crocodile   \n", "1   193.916465   397.234940   39.958544   63.463570  red lechwe   \n", "2    26.163200   457.388800  -25.228800   46.720000  red lechwe   \n", "3   594.675064  2266.229102  135.741047   56.881963       hippo   \n", "4   771.784811  2336.038783  127.984416  120.227785       hippo   \n", "\n", "   LabelConfidence Assignee      Timestamp  IsGroundTruth  ExcludedByLine  \n", "0              NaN  wmagesa  1696929902940          False             NaN  \n", "1              NaN  wmagesa  1696932493527          False             NaN  \n", "2              NaN  wmagesa  1696932493527          False             NaN  \n", "3              NaN  wmagesa  1696938797992          False             NaN  \n", "4              NaN  wmagesa  1696938797992          False             NaN  "]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25df.head()"]}, {"cell_type": "code", "execution_count": 66, "id": "4aa71527", "metadata": {}, "outputs": [], "source": ["ng25tasks=ng25df['TaskName'].unique()"]}, {"cell_type": "code", "execution_count": 67, "id": "f1e2e70b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(122, 1)"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["ng25tasksdf = pd.DataFrame(ng25tasks)\n", "ng25tasksdf.shape"]}, {"cell_type": "code", "execution_count": 70, "id": "84f7b1b0", "metadata": {}, "outputs": [], "source": ["ng25tasksdf.to_csv(\"NG25Tasks.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "7a6632a4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 71, "id": "9905e765", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-03-L_109ML</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-05-L_1-150_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-05-L_116-265_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-05-R_1-150_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-05-R_116-195_2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              TaskName\n", "0      NG25-03-L_109ML\n", "1    NG25-05-L_1-150_2\n", "2  NG25-05-L_116-265_2\n", "3    NG25-05-R_1-150_2\n", "4  NG25-05-R_116-195_2"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["#the identified repeated tasks\n", "deleteNG25tasksdf = pd.read_csv(\"scoutexports/deleteNG25repeattasks.txt\")\n", "deleteNG25tasksdf.head()"]}, {"cell_type": "code", "execution_count": 72, "id": "34a98542", "metadata": {}, "outputs": [{"data": {"text/plain": ["(23, 1)"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["deleteNG25tasksdf.shape"]}, {"cell_type": "code", "execution_count": 73, "id": "abd1bec7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(5713, 14)"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["#filtering the repeated tasks from all DAN tasks\n", "\n", "# Create a new DataFrame without the records meeting the condition\n", "filteredng25tasks_df = ng25df[~ng25df['TaskName'].isin(deleteNG25tasksdf['TaskName'])]\n", "filteredng25tasks_df.shape  # deleted 7142-5713 = 1,429 records"]}, {"cell_type": "code", "execution_count": 74, "id": "5b9c430d", "metadata": {}, "outputs": [], "source": ["filteredng25tasks_df.to_csv(\"uniqueNG25tasks.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 76, "id": "ea0d0f9f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145132_M0804846.jpg</td>\n", "      <td>6525067833c806004cc2d6b7</td>\n", "      <td>6360.960245</td>\n", "      <td>625.165623</td>\n", "      <td>54.652005</td>\n", "      <td>25.326539</td>\n", "      <td>crocodile</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696929902940</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>652506a333c806004cc2d6c7</td>\n", "      <td>193.916465</td>\n", "      <td>397.234940</td>\n", "      <td>39.958544</td>\n", "      <td>63.463570</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696932493527</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145454_M0804947.jpg</td>\n", "      <td>652506a333c806004cc2d6c7</td>\n", "      <td>26.163200</td>\n", "      <td>457.388800</td>\n", "      <td>-25.228800</td>\n", "      <td>46.720000</td>\n", "      <td>red lechwe</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696932493527</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>6525065633c806004cc2d6aa</td>\n", "      <td>594.675064</td>\n", "      <td>2266.229102</td>\n", "      <td>135.741047</td>\n", "      <td>56.881963</td>\n", "      <td>hippo</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696938797992</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG25-05-L_1-150</td>\n", "      <td>65250f6462e3240045e45647</td>\n", "      <td>KES22_WOT-L_20220923B-145256_M0804888.jpg</td>\n", "      <td>6525065633c806004cc2d6aa</td>\n", "      <td>771.784811</td>\n", "      <td>2336.038783</td>\n", "      <td>127.984416</td>\n", "      <td>120.227785</td>\n", "      <td>hippo</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1696938797992</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          TaskName                    TaskID  \\\n", "0  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "1  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "2  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "3  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "4  NG25-05-L_1-150  65250f6462e3240045e45647   \n", "\n", "                               ImageFilename                   ImageID  \\\n", "0  KES22_WOT-L_20220923B-145132_M0804846.jpg  6525067833c806004cc2d6b7   \n", "1  KES22_WOT-L_20220923B-145454_M0804947.jpg  652506a333c806004cc2d6c7   \n", "2  KES22_WOT-L_20220923B-145454_M0804947.jpg  652506a333c806004cc2d6c7   \n", "3  KES22_WOT-L_20220923B-145256_M0804888.jpg  6525065633c806004cc2d6aa   \n", "4  KES22_WOT-L_20220923B-145256_M0804888.jpg  6525065633c806004cc2d6aa   \n", "\n", "          BoxX         BoxY        BoxW        BoxH       Label  \\\n", "0  6360.960245   625.165623   54.652005   25.326539   crocodile   \n", "1   193.916465   397.234940   39.958544   63.463570  red lechwe   \n", "2    26.163200   457.388800  -25.228800   46.720000  red lechwe   \n", "3   594.675064  2266.229102  135.741047   56.881963       hippo   \n", "4   771.784811  2336.038783  127.984416  120.227785       hippo   \n", "\n", "   LabelConfidence Assignee      Timestamp  IsGroundTruth  ExcludedByLine  \n", "0              NaN  wmagesa  1696929902940          False             NaN  \n", "1              NaN  wmagesa  1696932493527          False             NaN  \n", "2              NaN  wmagesa  1696932493527          False             NaN  \n", "3              NaN  wmagesa  1696938797992          False             NaN  \n", "4              NaN  wmagesa  1696938797992          False             NaN  "]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["bboxng25df = pd.read_csv(\"uniqueNG25tasks.csv\")\n", "bboxng25df.head()"]}, {"cell_type": "code", "execution_count": 77, "id": "83b5f2e0", "metadata": {}, "outputs": [{"data": {"text/plain": ["red lechwe          2980\n", "bird                1413\n", "zebra                247\n", "elephant             211\n", "buffalo              196\n", "impala               101\n", "white_bones           73\n", "cow                   70\n", "warthog               56\n", "hippo                 51\n", "giraffe               51\n", "crocodile             42\n", "sitatunga             41\n", "ostrich               36\n", "elephant bull         21\n", "unknown animal        20\n", "donkey                19\n", "reedbuck              19\n", "roof_mabati           17\n", "human                  9\n", "vervet monkey          7\n", "roan                   7\n", "canoe                  4\n", "unknown mammal         4\n", "eland                  2\n", "waterbuck              2\n", "bushpig                2\n", "puku                   2\n", "wildebeest             2\n", "lion                   2\n", "gazelle_thomsons       1\n", "bushback               1\n", "sheep                  1\n", "oribi                  1\n", "roof_grass             1\n", "goat                   1\n", "Name: Label, dtype: int64"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["bboxng25df['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": null, "id": "95b71284", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "6bf69c47", "metadata": {}, "source": ["### SH Block \n", "#### Session 20220904A (SH) \n", "#### Bounding box analysis for each species"]}, {"cell_type": "code", "execution_count": 3, "id": "9f3b89ad", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>2992.284408</td>\n", "      <td>3582.150410</td>\n", "      <td>83.281342</td>\n", "      <td>108.265744</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>3119.288454</td>\n", "      <td>3278.173512</td>\n", "      <td>110.347778</td>\n", "      <td>127.004046</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>3221.308098</td>\n", "      <td>2886.751205</td>\n", "      <td>112.429812</td>\n", "      <td>189.465053</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>3106.796253</td>\n", "      <td>2818.044098</td>\n", "      <td>81.199308</td>\n", "      <td>106.183711</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SH01-L_159</td>\n", "      <td>653a61286f30500045ef6b6b</td>\n", "      <td>KES22_IIM-L_20220904A-060827_M0309504.jpg</td>\n", "      <td>653a4e074a2456004cd8a5ce</td>\n", "      <td>312.139451</td>\n", "      <td>3096.658157</td>\n", "      <td>77.035241</td>\n", "      <td>74.953208</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1698389707201</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     TaskName                   TaskID   \\\n", "0  SH01-L_159  653a61286f30500045ef6b6b   \n", "1  SH01-L_159  653a61286f30500045ef6b6b   \n", "2  SH01-L_159  653a61286f30500045ef6b6b   \n", "3  SH01-L_159  653a61286f30500045ef6b6b   \n", "4  SH01-L_159  653a61286f30500045ef6b6b   \n", "\n", "                              ImageFilename                  ImageID    \\\n", "0  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "1  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "2  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "3  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "4  KES22_IIM-L_20220904A-060827_M0309504.jpg  653a4e074a2456004cd8a5ce   \n", "\n", "          BoxX         BoxY        BoxW        BoxH     Label  \\\n", "0  2992.284408  3582.150410   83.281342  108.265744  elephant   \n", "1  3119.288454  3278.173512  110.347778  127.004046  elephant   \n", "2  3221.308098  2886.751205  112.429812  189.465053  elephant   \n", "3  3106.796253  2818.044098   81.199308  106.183711  elephant   \n", "4   312.139451  3096.658157   77.035241   74.953208  elephant   \n", "\n", "   LabelConfidence Assignee      Timestamp  IsGroundTruth  ExcludedByLine  \n", "0              NaN  hgeorge  1698389707201          False             NaN  \n", "1              NaN  hgeorge  1698389707201          False             NaN  \n", "2              NaN  hgeorge  1698389707201          False             NaN  \n", "3              NaN  hgeorge  1698389707201          False             NaN  \n", "4              NaN  hgeorge  1698389707201          False             NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["shdf = pd.read_csv(homedir+\"/scoutexports/SHscout-export-annotations.csv\")\n", "shdf.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "b9290cb0", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1090, 14)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["shdf.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "eb1176d6", "metadata": {}, "outputs": [], "source": ["shdf.columns=['TaskName', 'TaskID', 'ImageFilename', 'ImageID', 'BoxX', 'BoxY',\n", "       'BoxW', 'BoxH', 'Label', 'LabelConfidence', 'Assignee', 'Timestamp',\n", "       'IsGroundTruth', 'ExcludedByLine']"]}, {"cell_type": "code", "execution_count": 6, "id": "6ba3873f", "metadata": {}, "outputs": [{"data": {"text/plain": ["53"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["shdf['TaskName'].nunique()"]}, {"cell_type": "code", "execution_count": 7, "id": "85b10e6e", "metadata": {}, "outputs": [{"data": {"text/plain": ["elephant            965\n", "sable                34\n", "impala               21\n", "giraffe              14\n", "ec4                  11\n", "ec3                   9\n", "unknown mammal        8\n", "roan                  6\n", "unknown antelope      5\n", "gazelle_thomsons      4\n", "warthog               4\n", "white_bones           4\n", "bird                  2\n", "car                   1\n", "zebra                 1\n", "buffalo               1\n", "Name: Label, dtype: int64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["shdf['Label'].value_counts()"]}, {"cell_type": "markdown", "id": "6b32c0cd", "metadata": {}, "source": ["### NG26 Block \n", "#### Session IIM20220924A  \n", "#### Bounding box analysis for each species"]}, {"cell_type": "code", "execution_count": 3, "id": "80720af4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>NG26-02-L_110</th>\n", "      <th>6540fc188a297200456fcef0</th>\n", "      <th>KES22_IIM-L_20220924A-051009_M0301585.jpg</th>\n", "      <th>6540eb50e1ace1004c5539f7</th>\n", "      <th>1347.2952268709487</th>\n", "      <th>653.8597525044196</th>\n", "      <th>-55.06187389510902</th>\n", "      <th>75.7100766057749</th>\n", "      <th>buffalo</th>\n", "      <th>Unnamed: 9</th>\n", "      <th>annotator1</th>\n", "      <th>1698910133325</th>\n", "      <th>false</th>\n", "      <th>Unnamed: 13</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG26-02-L_110</td>\n", "      <td>6540fc188a297200456fcef0</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>6540eb50e1ace1004c5539f7</td>\n", "      <td>1787.790218</td>\n", "      <td>333.812610</td>\n", "      <td>-82.592811</td>\n", "      <td>58.503241</td>\n", "      <td>buffalo</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1698910133325</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG26-02-L_110</td>\n", "      <td>6540fc188a297200456fcef0</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>6540eb50e1ace1004c5539f7</td>\n", "      <td>1720.683559</td>\n", "      <td>397.477902</td>\n", "      <td>-73.989393</td>\n", "      <td>65.385975</td>\n", "      <td>buffalo</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1698910133325</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG26-02-L_110</td>\n", "      <td>6540fc188a297200456fcef0</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>6540eb50e1ace1004c5539f7</td>\n", "      <td>1662.180318</td>\n", "      <td>376.829699</td>\n", "      <td>-77.430760</td>\n", "      <td>58.503241</td>\n", "      <td>buffalo</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1698910133325</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG26-02-L_110</td>\n", "      <td>6540fc188a297200456fcef0</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>6540eb50e1ace1004c5539f7</td>\n", "      <td>1543.453153</td>\n", "      <td>347.578079</td>\n", "      <td>75.710077</td>\n", "      <td>63.665292</td>\n", "      <td>buffalo</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1698910133325</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG26-02-L_110</td>\n", "      <td>6540fc188a297200456fcef0</td>\n", "      <td>KES22_IIM-L_20220924A-051033_M0301597.jpg</td>\n", "      <td>6540e97ce1ace1004c55394f</td>\n", "      <td>1179.181758</td>\n", "      <td>114.787605</td>\n", "      <td>-33.713842</td>\n", "      <td>33.713842</td>\n", "      <td>impala</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1698911066051</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   NG26-02-L_110  6540fc188a297200456fcef0  \\\n", "0  NG26-02-L_110  6540fc188a297200456fcef0   \n", "1  NG26-02-L_110  6540fc188a297200456fcef0   \n", "2  NG26-02-L_110  6540fc188a297200456fcef0   \n", "3  NG26-02-L_110  6540fc188a297200456fcef0   \n", "4  NG26-02-L_110  6540fc188a297200456fcef0   \n", "\n", "   KES22_IIM-L_20220924A-051009_M0301585.jpg  6540eb50e1ace1004c5539f7  \\\n", "0  KES22_IIM-L_20220924A-051009_M0301585.jpg  6540eb50e1ace1004c5539f7   \n", "1  KES22_IIM-L_20220924A-051009_M0301585.jpg  6540eb50e1ace1004c5539f7   \n", "2  KES22_IIM-L_20220924A-051009_M0301585.jpg  6540eb50e1ace1004c5539f7   \n", "3  KES22_IIM-L_20220924A-051009_M0301585.jpg  6540eb50e1ace1004c5539f7   \n", "4  KES22_IIM-L_20220924A-051033_M0301597.jpg  6540e97ce1ace1004c55394f   \n", "\n", "   1347.2952268709487  653.8597525044196  -55.06187389510902  \\\n", "0         1787.790218         333.812610          -82.592811   \n", "1         1720.683559         397.477902          -73.989393   \n", "2         1662.180318         376.829699          -77.430760   \n", "3         1543.453153         347.578079           75.710077   \n", "4         1179.181758         114.787605          -33.713842   \n", "\n", "   75.7100766057749  buffalo  Unnamed: 9  annotator1  1698910133325  false  \\\n", "0         58.503241  buffalo         NaN  annotator1  1698910133325  False   \n", "1         65.385975  buffalo         NaN  annotator1  1698910133325  False   \n", "2         58.503241  buffalo         NaN  annotator1  1698910133325  False   \n", "3         63.665292  buffalo         NaN  annotator1  1698910133325  False   \n", "4         33.713842   impala         NaN  annotator1  1698911066051  False   \n", "\n", "   Unnamed: 13  \n", "0          NaN  \n", "1          NaN  \n", "2          NaN  \n", "3          NaN  \n", "4          NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26df = pd.read_csv(homedir+\"/scoutexports/NG26scout-export-annotations.csv\")\n", "ng26df.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "95e81fe0", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4526, 14)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26df.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "edc5a608", "metadata": {}, "outputs": [], "source": ["ng26df.columns=['TaskName', 'TaskID', 'ImageFilename', 'ImageID', 'BoxX', 'BoxY',\n", "       'BoxW', 'BoxH', 'Label', 'LabelConfidence', 'Assignee', 'Timestamp',\n", "       'IsGroundTruth', 'ExcludedByLine']"]}, {"cell_type": "code", "execution_count": 6, "id": "61d5cd32", "metadata": {}, "outputs": [{"data": {"text/plain": ["74"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26df['TaskName'].nunique()"]}, {"cell_type": "code", "execution_count": 7, "id": "037ae925", "metadata": {}, "outputs": [{"data": {"text/plain": ["red lechwe          1284\n", "elephant             760\n", "buffalo              531\n", "impala               457\n", "wildebeest           403\n", "zebra                363\n", "unknown mammal       162\n", "reedbuck              88\n", "giraffe               78\n", "baboon                78\n", "topi                  75\n", "kudu                  72\n", "unknown antelope      39\n", "warthog               33\n", "unknown carcass       23\n", "ostrich               19\n", "bird                  18\n", "crocodile             18\n", "hippo                  6\n", "waterbuck              5\n", "white_bones            4\n", "wild dog               4\n", "ec3                    2\n", "steenbok               2\n", "gazelle_thomsons       1\n", "ec4                    1\n", "Name: Label, dtype: int64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26df['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 8, "id": "51dc271c", "metadata": {}, "outputs": [{"data": {"text/plain": ["4526"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26df['Label'].value_counts().sum()"]}, {"cell_type": "markdown", "id": "be326d8e-3f0a-4310-853d-7ed8232667a6", "metadata": {}, "source": ["### NG26 GT"]}, {"cell_type": "code", "execution_count": 3, "id": "c9757267-92b1-4f28-aa39-0a3070ae2c9e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG26-02-L_110</td>\n", "      <td>6540fc188a297200456fcef0</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>6540eb50e1ace1004c5539f7</td>\n", "      <td>1347.295227</td>\n", "      <td>653.859753</td>\n", "      <td>-55.061874</td>\n", "      <td>75.710077</td>\n", "      <td>buffalo</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1698910133325</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG26-02-L_110</td>\n", "      <td>6540fc188a297200456fcef0</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>6540eb50e1ace1004c5539f7</td>\n", "      <td>1787.790218</td>\n", "      <td>333.812610</td>\n", "      <td>-82.592811</td>\n", "      <td>58.503241</td>\n", "      <td>buffalo</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1698910133325</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG26-02-L_110</td>\n", "      <td>6540fc188a297200456fcef0</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>6540eb50e1ace1004c5539f7</td>\n", "      <td>1720.683559</td>\n", "      <td>397.477902</td>\n", "      <td>-73.989393</td>\n", "      <td>65.385975</td>\n", "      <td>buffalo</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1698910133325</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG26-02-L_110</td>\n", "      <td>6540fc188a297200456fcef0</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>6540eb50e1ace1004c5539f7</td>\n", "      <td>1662.180318</td>\n", "      <td>376.829699</td>\n", "      <td>-77.430760</td>\n", "      <td>58.503241</td>\n", "      <td>buffalo</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1698910133325</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG26-02-L_110</td>\n", "      <td>6540fc188a297200456fcef0</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>6540eb50e1ace1004c5539f7</td>\n", "      <td>1543.453153</td>\n", "      <td>347.578079</td>\n", "      <td>75.710077</td>\n", "      <td>63.665292</td>\n", "      <td>buffalo</td>\n", "      <td>NaN</td>\n", "      <td>annotator1</td>\n", "      <td>1698910133325</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        TaskName                    TaskID  \\\n", "0  NG26-02-L_110  6540fc188a297200456fcef0   \n", "1  NG26-02-L_110  6540fc188a297200456fcef0   \n", "2  NG26-02-L_110  6540fc188a297200456fcef0   \n", "3  NG26-02-L_110  6540fc188a297200456fcef0   \n", "4  NG26-02-L_110  6540fc188a297200456fcef0   \n", "\n", "                               ImageFilename                   ImageID  \\\n", "0  KES22_IIM-L_20220924A-051009_M0301585.jpg  6540eb50e1ace1004c5539f7   \n", "1  KES22_IIM-L_20220924A-051009_M0301585.jpg  6540eb50e1ace1004c5539f7   \n", "2  KES22_IIM-L_20220924A-051009_M0301585.jpg  6540eb50e1ace1004c5539f7   \n", "3  KES22_IIM-L_20220924A-051009_M0301585.jpg  6540eb50e1ace1004c5539f7   \n", "4  KES22_IIM-L_20220924A-051009_M0301585.jpg  6540eb50e1ace1004c5539f7   \n", "\n", "          BoxX        BoxY       BoxW       BoxH    Label  LabelConfidence  \\\n", "0  1347.295227  653.859753 -55.061874  75.710077  buffalo              NaN   \n", "1  1787.790218  333.812610 -82.592811  58.503241  buffalo              NaN   \n", "2  1720.683559  397.477902 -73.989393  65.385975  buffalo              NaN   \n", "3  1662.180318  376.829699 -77.430760  58.503241  buffalo              NaN   \n", "4  1543.453153  347.578079  75.710077  63.665292  buffalo              NaN   \n", "\n", "     Assignee      Timestamp  IsGroundTruth ExcludedByLine  \n", "0  annotator1  1698910133325          False            NaN  \n", "1  annotator1  1698910133325          False            NaN  \n", "2  annotator1  1698910133325          False            NaN  \n", "3  annotator1  1698910133325          False            NaN  \n", "4  annotator1  1698910133325          False            NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26gtdf = pd.read_csv(homedir+\"/scoutexports/NG26GTscout-export-annotations.csv\")\n", "ng26gtdf.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "cfcf5fd9-258b-4246-bbda-65aa914279d1", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7656, 14)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26gtdf.shape"]}, {"cell_type": "code", "execution_count": 6, "id": "f9e0f836-9f09-4e75-83c5-a516925b66c6", "metadata": {}, "outputs": [], "source": ["ng26gtdf.columns=['TaskName', 'TaskID', 'ImageFilename', 'ImageID', 'BoxX', 'BoxY',\n", "       'BoxW', 'BoxH', 'Label', 'LabelConfidence', 'Assignee', 'Timestamp',\n", "       'IsGroundTruth', 'ExcludedByLine']"]}, {"cell_type": "code", "execution_count": 7, "id": "09c9824c-0cbe-4451-bc9f-532338417859", "metadata": {}, "outputs": [{"data": {"text/plain": ["red lechwe          2326\n", "elephant            1206\n", "impala               819\n", "buffalo              728\n", "wildebeest           688\n", "zebra                670\n", "unknown mammal       174\n", "baboon               172\n", "topi                 172\n", "reedbuck             147\n", "giraffe              119\n", "kudu                 113\n", "unknown antelope      66\n", "warthog               65\n", "unknown carcass       33\n", "crocodile             32\n", "white_bones           31\n", "ostrich               30\n", "bird                  19\n", "waterbuck             15\n", "hippo                 10\n", "wild dog               8\n", "steenbok               4\n", "ec3                    3\n", "gazelle_thomsons       2\n", "ec4                    2\n", "sitatunga              1\n", "hyena                  1\n", "Name: Label, dtype: int64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26gtdf['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 12, "id": "3c1dc281-d5c2-44d3-81d1-f543010aa7d9", "metadata": {}, "outputs": [{"data": {"text/plain": ["7656"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26gtdf['Label'].value_counts().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "e20c42a5-1bd4-4335-afd0-f5260b25c844", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0989e191-c2c2-4d47-bc58-2d8d65fa7531", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "eebe4182-cb70-4f7a-9fb4-725d808dc587", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "d15e4139", "metadata": {}, "source": ["### MT Block \n", "#### Session WOT20220909A  \n", "#### Bounding box analysis for each species"]}, {"cell_type": "code", "execution_count": 3, "id": "149a143a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070510_M0801395.jpg</td>\n", "      <td>654b44880ff5c4004c9b6e12</td>\n", "      <td>3136.466802</td>\n", "      <td>1525.141416</td>\n", "      <td>-60.966168</td>\n", "      <td>57.917859</td>\n", "      <td>impala</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1699450385985</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070510_M0801395.jpg</td>\n", "      <td>654b44880ff5c4004c9b6e12</td>\n", "      <td>3136.466802</td>\n", "      <td>1576.962658</td>\n", "      <td>67.062784</td>\n", "      <td>79.256018</td>\n", "      <td>impala</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1699450385985</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MT03-L_140</td>\n", "      <td>654b52ac47d0660045090329</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>654b2ceefa9745004c6853ee</td>\n", "      <td>1456.643317</td>\n", "      <td>1710.858308</td>\n", "      <td>115.457725</td>\n", "      <td>-107.886727</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1699594355830</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>MT03-L_140</td>\n", "      <td>654b52ac47d0660045090329</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>654b2ceefa9745004c6853ee</td>\n", "      <td>1346.863841</td>\n", "      <td>1915.275264</td>\n", "      <td>149.527218</td>\n", "      <td>-136.277971</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1699594355830</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MT03-L_140</td>\n", "      <td>654b52ac47d0660045090329</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>654b2ceefa9745004c6853ee</td>\n", "      <td>1519.104054</td>\n", "      <td>1877.420272</td>\n", "      <td>52.996989</td>\n", "      <td>-83.280982</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1699594355830</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Task Name                   Task ID  \\\n", "0   MT02-L_46  654b524e47d0660045090326   \n", "1   MT02-L_46  654b524e47d0660045090326   \n", "2  MT03-L_140  654b52ac47d0660045090329   \n", "3  MT03-L_140  654b52ac47d0660045090329   \n", "4  MT03-L_140  654b52ac47d0660045090329   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_WOT-L_20220909A-070510_M0801395.jpg  654b44880ff5c4004c9b6e12   \n", "1  KES22_WOT-L_20220909A-070510_M0801395.jpg  654b44880ff5c4004c9b6e12   \n", "2  KES22_WOT-L_20220909A-053748_M0808782.jpg  654b2ceefa9745004c6853ee   \n", "3  KES22_WOT-L_20220909A-053748_M0808782.jpg  654b2ceefa9745004c6853ee   \n", "4  KES22_WOT-L_20220909A-053748_M0808782.jpg  654b2ceefa9745004c6853ee   \n", "\n", "         Box X        Box Y       Box W       Box H     Label  \\\n", "0  3136.466802  1525.141416  -60.966168   57.917859    impala   \n", "1  3136.466802  1576.962658   67.062784   79.256018    impala   \n", "2  1456.643317  1710.858308  115.457725 -107.886727  elephant   \n", "3  1346.863841  1915.275264  149.527218 -136.277971  elephant   \n", "4  1519.104054  1877.420272   52.996989  -83.280982  elephant   \n", "\n", "   Label Confidence Assignee      Timestamp  Is Ground Truth  Excluded By Line  \n", "0               NaN  hgeorge  1699450385985            False               NaN  \n", "1               NaN  hgeorge  1699450385985            False               NaN  \n", "2               NaN   zrahim  1699594355830            False               NaN  \n", "3               NaN   zrahim  1699594355830            False               NaN  \n", "4               NaN   zrahim  1699594355830            False               NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["mtdf = pd.read_csv(homedir+\"/scoutexports/MTscout-export-annotations.csv\")\n", "mtdf.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "43c011f5", "metadata": {}, "outputs": [{"data": {"text/plain": ["(642, 14)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["mtdf.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "7adbe81d", "metadata": {}, "outputs": [], "source": ["mtdf.columns=['TaskName', 'TaskID', 'ImageFilename', 'ImageID', 'BoxX', 'BoxY',\n", "       'BoxW', 'BoxH', 'Label', 'LabelConfidence', 'Assignee', 'Timestamp',\n", "       'IsGroundTruth', 'ExcludedByLine']"]}, {"cell_type": "code", "execution_count": 6, "id": "e8b65144", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["elephant            340\n", "kudu                 90\n", "zebra                64\n", "impala               57\n", "red lechwe           35\n", "waterbuck            18\n", "unknown mammal        8\n", "roan                  7\n", "unknown antelope      6\n", "giraffe               4\n", "duiker                3\n", "warthog               2\n", "car                   2\n", "gazelle_thomsons      1\n", "ec4                   1\n", "unknown carcass       1\n", "ostrich               1\n", "human                 1\n", "bushpig               1\n", "Name: Label, dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["mtdf['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 7, "id": "f5c35ae1", "metadata": {}, "outputs": [{"data": {"text/plain": ["642"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["mtdf['Label'].value_counts().sum()"]}, {"cell_type": "markdown", "id": "69b5a5fb", "metadata": {}, "source": ["### MT Block \n", "#### Session WOT20220909A  and LJB20220908A\n", "#### Bounding box analysis for each species"]}, {"cell_type": "code", "execution_count": 3, "id": "272e7c52", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Box X</th>\n", "      <th>Box Y</th>\n", "      <th>Box W</th>\n", "      <th>Box H</th>\n", "      <th>Label</th>\n", "      <th>Label Confidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>Is Ground Truth</th>\n", "      <th>Excluded By Line</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070510_M0801395.jpg</td>\n", "      <td>654b44880ff5c4004c9b6e12</td>\n", "      <td>3136.466802</td>\n", "      <td>1525.141416</td>\n", "      <td>-60.966168</td>\n", "      <td>57.917859</td>\n", "      <td>impala</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1699450385985</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>MT02-L_46</td>\n", "      <td>654b524e47d0660045090326</td>\n", "      <td>KES22_WOT-L_20220909A-070510_M0801395.jpg</td>\n", "      <td>654b44880ff5c4004c9b6e12</td>\n", "      <td>3136.466802</td>\n", "      <td>1576.962658</td>\n", "      <td>67.062784</td>\n", "      <td>79.256018</td>\n", "      <td>impala</td>\n", "      <td>NaN</td>\n", "      <td>hgeorge</td>\n", "      <td>1699450385985</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MT03-L_140</td>\n", "      <td>654b52ac47d0660045090329</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>654b2ceefa9745004c6853ee</td>\n", "      <td>1456.643317</td>\n", "      <td>1710.858308</td>\n", "      <td>115.457725</td>\n", "      <td>-107.886727</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1699594355830</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>MT03-L_140</td>\n", "      <td>654b52ac47d0660045090329</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>654b2ceefa9745004c6853ee</td>\n", "      <td>1346.863841</td>\n", "      <td>1915.275264</td>\n", "      <td>149.527218</td>\n", "      <td>-136.277971</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1699594355830</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MT03-L_140</td>\n", "      <td>654b52ac47d0660045090329</td>\n", "      <td>KES22_WOT-L_20220909A-053748_M0808782.jpg</td>\n", "      <td>654b2ceefa9745004c6853ee</td>\n", "      <td>1519.104054</td>\n", "      <td>1877.420272</td>\n", "      <td>52.996989</td>\n", "      <td>-83.280982</td>\n", "      <td>elephant</td>\n", "      <td>NaN</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1699594355830</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Task Name                   Task ID  \\\n", "0   MT02-L_46  654b524e47d0660045090326   \n", "1   MT02-L_46  654b524e47d0660045090326   \n", "2  MT03-L_140  654b52ac47d0660045090329   \n", "3  MT03-L_140  654b52ac47d0660045090329   \n", "4  MT03-L_140  654b52ac47d0660045090329   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_WOT-L_20220909A-070510_M0801395.jpg  654b44880ff5c4004c9b6e12   \n", "1  KES22_WOT-L_20220909A-070510_M0801395.jpg  654b44880ff5c4004c9b6e12   \n", "2  KES22_WOT-L_20220909A-053748_M0808782.jpg  654b2ceefa9745004c6853ee   \n", "3  KES22_WOT-L_20220909A-053748_M0808782.jpg  654b2ceefa9745004c6853ee   \n", "4  KES22_WOT-L_20220909A-053748_M0808782.jpg  654b2ceefa9745004c6853ee   \n", "\n", "         Box X        Box Y       Box W       Box H     Label  \\\n", "0  3136.466802  1525.141416  -60.966168   57.917859    impala   \n", "1  3136.466802  1576.962658   67.062784   79.256018    impala   \n", "2  1456.643317  1710.858308  115.457725 -107.886727  elephant   \n", "3  1346.863841  1915.275264  149.527218 -136.277971  elephant   \n", "4  1519.104054  1877.420272   52.996989  -83.280982  elephant   \n", "\n", "   Label Confidence Assignee      Timestamp  Is Ground Truth  Excluded By Line  \n", "0               NaN  hgeorge  1699450385985            False               NaN  \n", "1               NaN  hgeorge  1699450385985            False               NaN  \n", "2               NaN   zrahim  1699594355830            False               NaN  \n", "3               NaN   zrahim  1699594355830            False               NaN  \n", "4               NaN   zrahim  1699594355830            False               NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["mtdf = pd.read_csv(homedir+\"/scoutexports/MTscout-export-annotations.csv\")\n", "mtdf.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "0c5006ce", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2414, 14)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["mtdf.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "61a70c9c", "metadata": {}, "outputs": [{"data": {"text/plain": ["elephant            1198\n", "buffalo              353\n", "impala               246\n", "kudu                 171\n", "zebra                112\n", "giraffe               47\n", "unknown mammal        45\n", "waterbuck             45\n", "unknown antelope      43\n", "red lechwe            38\n", "ec4                   30\n", "reedbuck              17\n", "warthog               15\n", "baboon                11\n", "roan                   9\n", "duiker                 9\n", "unknown carcass        4\n", "crocodile              4\n", "lion                   4\n", "car                    3\n", "sable                  2\n", "eland                  2\n", "bushpig                1\n", "human                  1\n", "ostrich                1\n", "gazelle_thomsons       1\n", "kob                    1\n", "hartebeest             1\n", "Name: Label, dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["mtdf['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 6, "id": "bd3f54f0", "metadata": {}, "outputs": [{"data": {"text/plain": ["2414"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["mtdf['Label'].value_counts().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "a0083651", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "e0569ab7-ddbe-43ce-bad0-ec5c1a92921b", "metadata": {}, "source": ["### A2 block"]}, {"cell_type": "code", "execution_count": 8, "id": "5c392168-fb0e-4752-b1f0-ef0736329976", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TaskName</th>\n", "      <th>TaskID</th>\n", "      <th>ImageFilename</th>\n", "      <th>ImageID</th>\n", "      <th>BoxX</th>\n", "      <th>BoxY</th>\n", "      <th>BoxW</th>\n", "      <th>BoxH</th>\n", "      <th>Label</th>\n", "      <th>LabelConfidence</th>\n", "      <th>Assignee</th>\n", "      <th>Timestamp</th>\n", "      <th>IsGroundTruth</th>\n", "      <th>ExcludedByLine</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A202-L_362</td>\n", "      <td>655b425a74f2c50044eba5b7</td>\n", "      <td>KAF22_MFZ-L_20220827A-071157_M0203630.jpg</td>\n", "      <td>655b384125ed2b004c91d6ed</td>\n", "      <td>5313.292418</td>\n", "      <td>3129.800716</td>\n", "      <td>60.760278</td>\n", "      <td>64.638594</td>\n", "      <td>impala</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1.700660e+12</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A202-L_362</td>\n", "      <td>655b425a74f2c50044eba5b7</td>\n", "      <td>KAF22_MFZ-L_20220827A-071157_M0203630.jpg</td>\n", "      <td>655b384125ed2b004c91d6ed</td>\n", "      <td>5508.500971</td>\n", "      <td>3053.527175</td>\n", "      <td>-68.516910</td>\n", "      <td>59.467506</td>\n", "      <td>impala</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1.700660e+12</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A202-L_362</td>\n", "      <td>655b425a74f2c50044eba5b7</td>\n", "      <td>KAF22_MFZ-L_20220827A-071157_M0203630.jpg</td>\n", "      <td>655b384125ed2b004c91d6ed</td>\n", "      <td>5211.163439</td>\n", "      <td>3293.982745</td>\n", "      <td>-49.125331</td>\n", "      <td>41.368700</td>\n", "      <td>impala</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1.700660e+12</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A202-L_362</td>\n", "      <td>655b425a74f2c50044eba5b7</td>\n", "      <td>KAF22_MFZ-L_20220827A-070941_M0203562.jpg</td>\n", "      <td>655b1aaf25ed2b004c91cbcc</td>\n", "      <td>5244.643489</td>\n", "      <td>189.275192</td>\n", "      <td>-41.296405</td>\n", "      <td>39.575722</td>\n", "      <td>unknown antelope</td>\n", "      <td>NaN</td>\n", "      <td>wmagesa</td>\n", "      <td>1.700660e+12</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A202-L_531</td>\n", "      <td>655b42a774f2c50044eba5b9</td>\n", "      <td>KAF22_MFZ-L_20220827A-071514_M0203728.jpg</td>\n", "      <td>655b29a225ed2b004c91d16d</td>\n", "      <td>2319.895208</td>\n", "      <td>4350.896252</td>\n", "      <td>55.459914</td>\n", "      <td>36.973276</td>\n", "      <td>puku</td>\n", "      <td>NaN</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>1.701070e+12</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     TaskName                    TaskID  \\\n", "0  A202-L_362  655b425a74f2c50044eba5b7   \n", "1  A202-L_362  655b425a74f2c50044eba5b7   \n", "2  A202-L_362  655b425a74f2c50044eba5b7   \n", "3  A202-L_362  655b425a74f2c50044eba5b7   \n", "4  A202-L_531  655b42a774f2c50044eba5b9   \n", "\n", "                               ImageFilename                   ImageID  \\\n", "0  KAF22_MFZ-L_20220827A-071157_M0203630.jpg  655b384125ed2b004c91d6ed   \n", "1  KAF22_MFZ-L_20220827A-071157_M0203630.jpg  655b384125ed2b004c91d6ed   \n", "2  KAF22_MFZ-L_20220827A-071157_M0203630.jpg  655b384125ed2b004c91d6ed   \n", "3  KAF22_MFZ-L_20220827A-070941_M0203562.jpg  655b1aaf25ed2b004c91cbcc   \n", "4  KAF22_MFZ-L_20220827A-071514_M0203728.jpg  655b29a225ed2b004c91d16d   \n", "\n", "          BoxX         BoxY       BoxW       BoxH             Label  \\\n", "0  5313.292418  3129.800716  60.760278  64.638594            impala   \n", "1  5508.500971  3053.527175 -68.516910  59.467506            impala   \n", "2  5211.163439  3293.982745 -49.125331  41.368700            impala   \n", "3  5244.643489   189.275192 -41.296405  39.575722  unknown antelope   \n", "4  2319.895208  4350.896252  55.459914  36.973276              puku   \n", "\n", "   LabelConfidence  Assignee     Timestamp  IsGroundTruth  ExcludedByLine  \n", "0              NaN   wmagesa  1.700660e+12          False             NaN  \n", "1              NaN   wmagesa  1.700660e+12          False             NaN  \n", "2              NaN   wmagesa  1.700660e+12          False             NaN  \n", "3              NaN   wmagesa  1.700660e+12          False             NaN  \n", "4              NaN  mshirima  1.701070e+12          False             NaN  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["a2df = pd.read_csv(homedir+\"/scoutexports/A2scout-export-annotations.csv\")\n", "a2df.head()"]}, {"cell_type": "code", "execution_count": 9, "id": "e1238d50-ef58-4738-9431-a3ef9a9a68b8", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4101, 14)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["a2df.shape"]}, {"cell_type": "code", "execution_count": 10, "id": "651fcfc4-8806-4288-a40b-8caf9a35372a", "metadata": {}, "outputs": [], "source": ["a2df.columns=['TaskName', 'TaskID', 'ImageFilename', 'ImageID', 'BoxX', 'BoxY',\n", "       'BoxW', 'BoxH', 'Label', 'LabelConfidence', 'Assignee', 'Timestamp',\n", "       'IsGroundTruth', 'ExcludedByLine']"]}, {"cell_type": "code", "execution_count": 11, "id": "5522b52f-4c74-4eed-8964-9f7c3f7d9c3a", "metadata": {}, "outputs": [{"data": {"text/plain": ["red lechwe          3249\n", "wildebeest           167\n", "hartebeest           146\n", "sable                111\n", "puku                  91\n", "roan                  70\n", "unknown antelope      59\n", "impala                41\n", "reedbuck              41\n", "hippo                 39\n", "unknown mammal        28\n", "elephant              14\n", "warthog               12\n", "kudu                  10\n", "baboon                 9\n", "crocodile              4\n", "unknown carcass        3\n", "buffalo                3\n", "bushpig                2\n", "vervet monkey          1\n", "waterbuck              1\n", "Name: Label, dtype: int64"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["a2df['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 13, "id": "677d604c-f7b6-4be1-920c-57207956a553", "metadata": {}, "outputs": [{"data": {"text/plain": ["4101"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["a2df['Label'].value_counts().sum()"]}, {"cell_type": "code", "execution_count": null, "id": "42a544dc-e7ad-47d2-8036-aa14d2c266eb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8a322824-a803-4463-8887-b6de<PERSON><PERSON>dd53", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "97d07eb2-2991-4082-a86d-ed8432f4f004", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9fefb32c-ba2a-4dac-b89d-ee15bec1a6d5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cced44b7-4fcf-4f1a-bd4a-442ba7b9117b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "38c071b6-4548-4028-9675-8b87807a7015", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "67b2b8da-7672-49f6-ae73-476dc3246cd6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}