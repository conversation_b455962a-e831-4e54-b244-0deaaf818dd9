"""
<PERSON><PERSON><PERSON> to draw bounding boxes on images and collect segment ratings.
Takes CSV file with bbox coordinates and image directory as input.
"""

import argparse
import cv2
import numpy as np
import pandas as pd

# Create the parser
parser = argparse.ArgumentParser(description='Process some integers.')

# Add the arguments
parser.add_argument('csv_file', type=str, help='The path to the CSV file')
parser.add_argument('image_dir', type=str, help='The directory of the images')
parser.add_argument('tile_factor', type = int, help = 'The factor to divide image in X and Y', default=2)

# Parse the arguments
args = parser.parse_args()

csv_file = args.csv_file
image_dir = args.image_dir
dfact = args.tile_factor

def draw_bounding_boxes(csv_file, image_dir):
    """
    Draw bounding boxes on images and collect ratings for image segments.
    
    Args:
        csv_file (str): Path to CSV file containing bounding box coordinates and labels
        image_dir (str): Directory containing the images
    
    Returns:
        dict: Dictionary containing ratings for image segments
    """
    # Read the CSV file
    df = pd.read_csv(csv_file)

    # Define a color map for labels
    unique_labels = df['Label'].unique()
    colors = {label: tuple(np.random.randint(210, 256, 3).tolist()) for label in unique_labels}

    ratings = {}

    # Iterate over each unique image in the CSV file
    for image_filename in df['Image Filename'].unique():
        print(image_filename)
        image_path = f"{image_dir}/{image_filename}"
        image = cv2.imread(image_path)

        # Filter rows for the current image
        image_rows = df[df['Image Filename'] == image_filename]

        # Draw bounding boxes
        for _, row in image_rows.iterrows():
            x, y, w, h = int(row['Box X']), int(row['Box Y']), int(row['Box W']), int(row['Box H'])
            label = row['Label']
            color = colors[label]
            # Draw inner white rectangle
            cv2.rectangle(image, (x+1, y+1), (x+w-1, y+h-1), (255, 255, 255), 2)
        # Draw outer colored rectangle
            cv2.rectangle(image, (x-1, y-1), (x+w+1, y+h+1), color, 2)
            # cv2.rectangle(image, (x, y), (x+w, y+h), color, 5)
            # cv2.putText(image, label, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 1, color, 3)

        # Display the images
        height, width, _ = image.shape
        for i in range(dfact):
            for j in range(dfact):
                tmpimg = image[i * height // dfact : (i+1) * height // dfact, 
                   j * width // dfact : (j+1) * width // dfact]
                cv2.imshow(f"{image_filename} - {i+1}/{dfact ** 2}", tmpimg)
                
                key = cv2.waitKey(0) & 0xFF
                cv2.destroyAllWindows()
                # Check for ratings (keys 0-9)
                if 48 <= key <= 57:  # ASCII values for 0-9
                    rating = key - 48
                    ratings[image_filename, str(i)+'-'+str(j)] = rating

        cv2.destroyAllWindows()
    print(ratings)

draw_bounding_boxes(csv_file, image_dir)
