{"cells": [{"cell_type": "markdown", "id": "ff9ed367", "metadata": {}, "source": ["Divide stratum to left and right"]}, {"cell_type": "code", "execution_count": 1, "id": "8daf5dd7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "id": "b489dc6c-8845-4df1-9680-2fd060786fc4", "metadata": {}, "outputs": [], "source": ["coco_file_path = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023/utils/CVATdata/NG26stratum/annotationsNG26.json\""]}, {"cell_type": "code", "execution_count": 3, "id": "23b685d4", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "images_df = pd.DataFrame(coco_data['images'])\n"]}, {"cell_type": "code", "execution_count": 4, "id": "4a1f8fd3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(859, 6)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "8e9230e2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051033_M0301597.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051007_M0301584.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051031_M0301596.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051354_M0301697.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   1   7008    4672  KES22_IIM-L_20220924A-051009_M0301585.jpg        1   \n", "1   2   7008    4672  KES22_IIM-L_20220924A-051033_M0301597.jpg        1   \n", "2   3   7008    4672  KES22_IIM-L_20220924A-051007_M0301584.jpg        1   \n", "3   4   7008    4672  KES22_IIM-L_20220924A-051031_M0301596.jpg        1   \n", "4   5   7008    4672  KES22_IIM-L_20220924A-051354_M0301697.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["images_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "60719a18", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 9, "id": "28706da0", "metadata": {}, "outputs": [], "source": ["pattern1 = \"KES22_IIM-L_\""]}, {"cell_type": "code", "execution_count": 8, "id": "fa81bb81-35e1-41cc-b57f-e08360748e87", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 10, "id": "4e4444a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(431, 6)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "ng26leftposve_df = images_df[images_df['file_name'].str.contains(pattern1, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "ng26leftposve_df.shape"]}, {"cell_type": "code", "execution_count": 11, "id": "b3990d59-9f89-48d7-874a-8150256b6b6e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051033_M0301597.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051007_M0301584.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051031_M0301596.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051354_M0301697.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   1   7008    4672  KES22_IIM-L_20220924A-051009_M0301585.jpg        1   \n", "1   2   7008    4672  KES22_IIM-L_20220924A-051033_M0301597.jpg        1   \n", "2   3   7008    4672  KES22_IIM-L_20220924A-051007_M0301584.jpg        1   \n", "3   4   7008    4672  KES22_IIM-L_20220924A-051031_M0301596.jpg        1   \n", "4   5   7008    4672  KES22_IIM-L_20220924A-051354_M0301697.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26leftposve_df.head()"]}, {"cell_type": "code", "execution_count": 12, "id": "594ad7b9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>813</th>\n", "      <td>814</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-080243_M0306744.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>814</th>\n", "      <td>815</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-080241_M0306743.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>815</th>\n", "      <td>816</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-080247_M0306746.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>816</th>\n", "      <td>817</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-080249_M0306747.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>817</th>\n", "      <td>818</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-080207_M0306726.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "813  814   7008    4672  KES22_IIM-L_20220924A-080243_M0306744.jpg        1   \n", "814  815   7008    4672  KES22_IIM-L_20220924A-080241_M0306743.jpg        1   \n", "815  816   7008    4672  KES22_IIM-L_20220924A-080247_M0306746.jpg        1   \n", "816  817   7008    4672  KES22_IIM-L_20220924A-080249_M0306747.jpg        1   \n", "817  818   7008    4672  KES22_IIM-L_20220924A-080207_M0306726.jpg        1   \n", "\n", "    date_captured  \n", "813    2022-09-23  \n", "814    2022-09-23  \n", "815    2022-09-23  \n", "816    2022-09-23  \n", "817    2022-09-23  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26leftposve_df.tail()"]}, {"cell_type": "code", "execution_count": 13, "id": "164fc814", "metadata": {}, "outputs": [], "source": ["ng26leftposve_dfsorted = ng26leftposve_df.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 14, "id": "932d181b-abf7-45cb-b26d-c73c7d9163f0", "metadata": {}, "outputs": [], "source": ["ng26leftposve_dfsorted.to_csv(\"ng26leftimgpos.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 12, "id": "7bc344e3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051007_M0301584.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051031_M0301596.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051033_M0301597.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051156_M0301638.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "2   3   7008    4672  KES22_IIM-L_20220924A-051007_M0301584.jpg        1   \n", "0   1   7008    4672  KES22_IIM-L_20220924A-051009_M0301585.jpg        1   \n", "3   4   7008    4672  KES22_IIM-L_20220924A-051031_M0301596.jpg        1   \n", "1   2   7008    4672  KES22_IIM-L_20220924A-051033_M0301597.jpg        1   \n", "6   7   7008    4672  KES22_IIM-L_20220924A-051156_M0301638.jpg        1   \n", "\n", "  date_captured  \n", "2    2022-09-23  \n", "0    2022-09-23  \n", "3    2022-09-23  \n", "1    2022-09-23  \n", "6    2022-09-23  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26leftposve_dfsorted.head()"]}, {"cell_type": "code", "execution_count": 14, "id": "07d3b903", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Name</th>\n", "      <th>Task ID</th>\n", "      <th>Image Filename</th>\n", "      <th>Image ID</th>\n", "      <th>Image Height</th>\n", "      <th>Image Width</th>\n", "      <th>WIC Confidence</th>\n", "      <th>Ground Truth Status</th>\n", "      <th>Exclusion Side</th>\n", "      <th>Inclusion Top X Fraction</th>\n", "      <th>Inclusion Bottom X Fraction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050240_M0301361.jpg</td>\n", "      <td>6540dfe8e1ace1004c5535db</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050302_M0301372.jpg</td>\n", "      <td>6540dfebe1ace1004c5535dc</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050248_M0301365.jpg</td>\n", "      <td>6540dfede1ace1004c5535dd</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050338_M0301390.jpg</td>\n", "      <td>6540dff0e1ace1004c5535de</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NG26-01-L_34</td>\n", "      <td>6540f9228a297200456fceee</td>\n", "      <td>KES22_IIM-L_20220924A-050318_M0301380.jpg</td>\n", "      <td>6540dff3e1ace1004c5535df</td>\n", "      <td>4672</td>\n", "      <td>7008</td>\n", "      <td>NaN</td>\n", "      <td>True</td>\n", "      <td>right</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Task Name                   Task ID  \\\n", "0  NG26-01-L_34  6540f9228a297200456fceee   \n", "1  NG26-01-L_34  6540f9228a297200456fceee   \n", "2  NG26-01-L_34  6540f9228a297200456fceee   \n", "3  NG26-01-L_34  6540f9228a297200456fceee   \n", "4  NG26-01-L_34  6540f9228a297200456fceee   \n", "\n", "                              Image Filename                  Image ID  \\\n", "0  KES22_IIM-L_20220924A-050240_M0301361.jpg  6540dfe8e1ace1004c5535db   \n", "1  KES22_IIM-L_20220924A-050302_M0301372.jpg  6540dfebe1ace1004c5535dc   \n", "2  KES22_IIM-L_20220924A-050248_M0301365.jpg  6540dfede1ace1004c5535dd   \n", "3  KES22_IIM-L_20220924A-050338_M0301390.jpg  6540dff0e1ace1004c5535de   \n", "4  KES22_IIM-L_20220924A-050318_M0301380.jpg  6540dff3e1ace1004c5535df   \n", "\n", "   Image Height  Image Width  WIC Confidence  Ground Truth Status  \\\n", "0          4672         7008             NaN                 True   \n", "1          4672         7008             NaN                 True   \n", "2          4672         7008             NaN                 True   \n", "3          4672         7008             NaN                 True   \n", "4          4672         7008             NaN                 True   \n", "\n", "  Exclusion Side  Inclusion Top X Fraction  Inclusion Bottom X Fraction  \n", "0          right                       NaN                          NaN  \n", "1          right                       NaN                          NaN  \n", "2          right                       NaN                          NaN  \n", "3          right                       NaN                          NaN  \n", "4          right                       NaN                          NaN  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["imgpath = \"/home/<USER>/Dropbox/Conservation/MWSLab-2023/scoutexports/NG26GTscout-export-images.csv\"\n", "ng26imgs = pd.read_csv(imgpath)\n", "ng26imgs.head()"]}, {"cell_type": "code", "execution_count": 15, "id": "87ff32c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(10982, 11)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26imgs.shape"]}, {"cell_type": "code", "execution_count": 16, "id": "fbff7df6", "metadata": {}, "outputs": [{"data": {"text/plain": ["10098"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26imgs[\"Image Filename\"].nunique()"]}, {"cell_type": "code", "execution_count": 17, "id": "6b578304", "metadata": {}, "outputs": [], "source": ["ng26imgsunique = ng26imgs[\"Image Filename\"].unique()"]}, {"cell_type": "code", "execution_count": 18, "id": "7675cd62", "metadata": {}, "outputs": [{"data": {"text/plain": ["10098"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["len(ng26imgsunique)"]}, {"cell_type": "code", "execution_count": 19, "id": "c1e7f0f8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220924A-050240_M0301361.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220924A-050302_M0301372.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220924A-050248_M0301365.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220924A-050338_M0301390.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220924A-050318_M0301380.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   file_name\n", "0  KES22_IIM-L_20220924A-050240_M0301361.jpg\n", "1  KES22_IIM-L_20220924A-050302_M0301372.jpg\n", "2  KES22_IIM-L_20220924A-050248_M0301365.jpg\n", "3  KES22_IIM-L_20220924A-050338_M0301390.jpg\n", "4  KES22_IIM-L_20220924A-050318_M0301380.jpg"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26imgsdf = pd.DataFrame(ng26imgsunique,columns=[\"file_name\"])\n", "ng26imgsdf.head()"]}, {"cell_type": "code", "execution_count": 20, "id": "b6debf9d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(5050, 1)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26imgsuniqueleft = ng26imgsdf[ng26imgsdf['file_name'].str.contains(pattern1, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "ng26imgsuniqueleft.shape"]}, {"cell_type": "code", "execution_count": 21, "id": "247cec3a-dece-43d8-ab76-c7ff9b1a5244", "metadata": {}, "outputs": [], "source": ["ng26leftimgsorted = ng26imgsuniqueleft.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 22, "id": "6cc04368-35fb-4d16-a052-218249487290", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>KES22_IIM-L_20220924A-050238_M0301360.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220924A-050240_M0301361.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>KES22_IIM-L_20220924A-050242_M0301362.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>KES22_IIM-L_20220924A-050244_M0301363.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>KES22_IIM-L_20220924A-050246_M0301364.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                    file_name\n", "11  KES22_IIM-L_20220924A-050238_M0301360.jpg\n", "0   KES22_IIM-L_20220924A-050240_M0301361.jpg\n", "15  KES22_IIM-L_20220924A-050242_M0301362.jpg\n", "31  KES22_IIM-L_20220924A-050244_M0301363.jpg\n", "26  KES22_IIM-L_20220924A-050246_M0301364.jpg"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26leftimgsorted.head()"]}, {"cell_type": "code", "execution_count": 23, "id": "2ece45cc-d694-493b-9387-393d0162ccd4", "metadata": {}, "outputs": [{"data": {"text/plain": ["(5050, 1)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26leftimgsorted.shape"]}, {"cell_type": "code", "execution_count": 24, "id": "2b99b790-5318-4b36-a53a-16d3756b4037", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in ng26leftposve_dfsorted[\"file_name\"]:\n", "    if item in ng26leftimgsorted[\"file_name\"].values:\n", "        index_in_pos = ng26leftimgsorted[\"file_name\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(ng26leftimgsorted[\"file_name\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(ng26leftimgsorted[\"file_name\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 25, "id": "2be9136b-e16e-42d9-b00a-93ec347ddb84", "metadata": {}, "outputs": [{"data": {"text/plain": ["1293"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 26, "id": "d7696c45-bfd9-436c-afda-4239c5007d50", "metadata": {}, "outputs": [{"data": {"text/plain": ["887"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 27, "id": "008a9c33-b2e3-4b6f-875e-1a9b32e5f81b", "metadata": {}, "outputs": [], "source": ["cvatimg = pd.DataFrame(fin,columns=[\"cvatfilename\"])"]}, {"cell_type": "code", "execution_count": 28, "id": "8a77236e-0e42-482c-892a-0504f417334f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>115</th>\n", "      <td>KES22_IIM-L_20220924A-051005_M0301583.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>479</th>\n", "      <td>KES22_IIM-L_20220924A-051007_M0301584.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>118</th>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>799</th>\n", "      <td>KES22_IIM-L_20220924A-051011_M0301586.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>451</th>\n", "      <td>KES22_IIM-L_20220924A-051029_M0301595.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  c<PERSON><PERSON><PERSON><PERSON>\n", "115  KES22_IIM-L_20220924A-051005_M0301583.jpg\n", "479  KES22_IIM-L_20220924A-051007_M0301584.jpg\n", "118  KES22_IIM-L_20220924A-051009_M0301585.jpg\n", "799  KES22_IIM-L_20220924A-051011_M0301586.jpg\n", "451  KES22_IIM-L_20220924A-051029_M0301595.jpg"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin = cvatimg.sort_values(by=[\"cvatfilename\"],ascending=True)\n", "cvatfin.head()"]}, {"cell_type": "code", "execution_count": 29, "id": "ffdef2e9-a764-40b6-9132-94f29afa7e82", "metadata": {}, "outputs": [{"data": {"text/plain": ["(887, 1)"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin.shape"]}, {"cell_type": "code", "execution_count": 30, "id": "afc94256-61ff-436c-a8a0-976acc8ab884", "metadata": {}, "outputs": [], "source": ["cvatfin.to_csv(\"ng26leftcvat.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "c2d70828-12c5-45b1-a911-9dc64154f5dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "ac85a34b-1a7e-40be-b824-ed9170a5fdb7", "metadata": {}, "source": ["### NG26 Right"]}, {"cell_type": "code", "execution_count": 15, "id": "8ddae76b-c500-4d2d-958d-e20286f9ee1e", "metadata": {}, "outputs": [], "source": ["pattern2 = \"KES22_IIM-R_\""]}, {"cell_type": "code", "execution_count": 16, "id": "1e9aefc9", "metadata": {}, "outputs": [{"data": {"text/plain": ["(428, 6)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter the DataFrame based on the pattern\n", "ng26right = images_df[images_df['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "ng26right.shape"]}, {"cell_type": "code", "execution_count": 17, "id": "3ad7fa26", "metadata": {}, "outputs": [], "source": ["ng26rightsorted = ng26right.sort_values(by=[\"file_name\"],ascending=True)"]}, {"cell_type": "code", "execution_count": 18, "id": "786e514b-1138-4c25-a57f-d0dd97b46ca2", "metadata": {}, "outputs": [], "source": ["ng26rightsorted.to_csv(\"ng26rightimgpos.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 19, "id": "5fd9fbb4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>349</th>\n", "      <td>350</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-050250_M0407922.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>792</th>\n", "      <td>793</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-050958_M0408135.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>797</th>\n", "      <td>798</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-051012_M0408142.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>798</th>\n", "      <td>799</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-051052_M0408162.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>803</th>\n", "      <td>804</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-051054_M0408163.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "349  350   7008    4672  KES22_IIM-R_20220924A-050250_M0407922.jpg        1   \n", "792  793   7008    4672  KES22_IIM-R_20220924A-050958_M0408135.jpg        1   \n", "797  798   7008    4672  KES22_IIM-R_20220924A-051012_M0408142.jpg        1   \n", "798  799   7008    4672  KES22_IIM-R_20220924A-051052_M0408162.jpg        1   \n", "803  804   7008    4672  KES22_IIM-R_20220924A-051054_M0408163.jpg        1   \n", "\n", "    date_captured  \n", "349    2022-09-23  \n", "792    2022-09-23  \n", "797    2022-09-23  \n", "798    2022-09-23  \n", "803    2022-09-23  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26rightsorted.head()"]}, {"cell_type": "code", "execution_count": 35, "id": "03450b69-5e5d-4a6b-b810-50649fb1a63b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>818</th>\n", "      <td>819</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-083824_M0404368.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>821</th>\n", "      <td>822</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-083826_M0404369.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>825</th>\n", "      <td>826</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-083828_M0404370.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>824</th>\n", "      <td>825</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-083842_M0404377.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>827</th>\n", "      <td>828</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-R_20220924A-083858_M0404385.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "818  819   7008    4672  KES22_IIM-R_20220924A-083824_M0404368.jpg        1   \n", "821  822   7008    4672  KES22_IIM-R_20220924A-083826_M0404369.jpg        1   \n", "825  826   7008    4672  KES22_IIM-R_20220924A-083828_M0404370.jpg        1   \n", "824  825   7008    4672  KES22_IIM-R_20220924A-083842_M0404377.jpg        1   \n", "827  828   7008    4672  KES22_IIM-R_20220924A-083858_M0404385.jpg        1   \n", "\n", "    date_captured  \n", "818    2022-09-23  \n", "821    2022-09-23  \n", "825    2022-09-23  \n", "824    2022-09-23  \n", "827    2022-09-23  "]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26rightsorted.tail()"]}, {"cell_type": "code", "execution_count": 36, "id": "ca07d990-3f37-4f08-b294-485a4a12362d", "metadata": {}, "outputs": [{"data": {"text/plain": ["(428, 6)"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26rightsorted.shape"]}, {"cell_type": "code", "execution_count": 37, "id": "b38c015b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(5048, 1)"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26imgsuniqueright = ng26imgsdf[ng26imgsdf['file_name'].str.contains(pattern2, case=False, na=False, regex=True)]\n", "\n", "# Print the resulting filtered DataFrame\n", "ng26imgsuniqueright.shape"]}, {"cell_type": "code", "execution_count": 38, "id": "b6a573b1", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4089</th>\n", "      <td>KES22_IIM-R_20220924A-050238_M0407916.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4119</th>\n", "      <td>KES22_IIM-R_20220924A-050240_M0407917.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4100</th>\n", "      <td>KES22_IIM-R_20220924A-050242_M0407918.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4099</th>\n", "      <td>KES22_IIM-R_20220924A-050244_M0407919.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4094</th>\n", "      <td>KES22_IIM-R_20220924A-050246_M0407920.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                      file_name\n", "4089  KES22_IIM-R_20220924A-050238_M0407916.jpg\n", "4119  KES22_IIM-R_20220924A-050240_M0407917.jpg\n", "4100  KES22_IIM-R_20220924A-050242_M0407918.jpg\n", "4099  KES22_IIM-R_20220924A-050244_M0407919.jpg\n", "4094  KES22_IIM-R_20220924A-050246_M0407920.jpg"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26rightimgsorted = ng26imgsuniqueright.sort_values(by=[\"file_name\"],ascending=True)\n", "ng26rightimgsorted.head()"]}, {"cell_type": "code", "execution_count": 39, "id": "b879b12b-d248-4bc7-baa7-f9119aee8f71", "metadata": {}, "outputs": [], "source": ["# Create a new list based on the specified condition\n", "new_list = []\n", "\n", "for item in ng26rightsorted[\"file_name\"]:\n", "    if item in ng26rightimgsorted[\"file_name\"].values:\n", "        index_in_pos = ng26rightimgsorted[\"file_name\"].values.tolist().index(item)\n", "        \n", "        #add the image before\n", "        new_list.append(ng26rightimgsorted[\"file_name\"].values[index_in_pos -1])\n", "        \n", "        #add the positive image\n", "        new_list.append(item)\n", "        \n", "        #add the image after\n", "        new_list.append(ng26rightimgsorted[\"file_name\"].values[index_in_pos + 1])"]}, {"cell_type": "code", "execution_count": 40, "id": "db6f4e5d-fbb6-46a9-9bee-8476dbe5cbbb", "metadata": {}, "outputs": [{"data": {"text/plain": ["1284"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_list)"]}, {"cell_type": "code", "execution_count": 41, "id": "7badf276-6239-486d-b2ba-9cb2a2c3bcef", "metadata": {}, "outputs": [{"data": {"text/plain": ["888"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["fin= set(new_list) #remove duplicates\n", "len(fin)"]}, {"cell_type": "code", "execution_count": 42, "id": "82318eab-c30a-427e-96de-befaea605c12", "metadata": {}, "outputs": [], "source": ["cvatimg = pd.DataFrame(fin,columns=[\"cvatfilename\"])"]}, {"cell_type": "code", "execution_count": 43, "id": "efc73194-3fdb-436e-bcb9-09a25d0b2c1d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>494</th>\n", "      <td>KES22_IIM-R_20220924A-050248_M0407921.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170</th>\n", "      <td>KES22_IIM-R_20220924A-050250_M0407922.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>709</th>\n", "      <td>KES22_IIM-R_20220924A-050252_M0407923.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>539</th>\n", "      <td>KES22_IIM-R_20220924A-050956_M0408134.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>KES22_IIM-R_20220924A-050958_M0408135.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  c<PERSON><PERSON><PERSON><PERSON>\n", "494  KES22_IIM-R_20220924A-050248_M0407921.jpg\n", "170  KES22_IIM-R_20220924A-050250_M0407922.jpg\n", "709  KES22_IIM-R_20220924A-050252_M0407923.jpg\n", "539  KES22_IIM-R_20220924A-050956_M0408134.jpg\n", "39   KES22_IIM-R_20220924A-050958_M0408135.jpg"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin = cvatimg.sort_values(by=[\"cvatfilename\"],ascending=True)\n", "cvatfin.head()"]}, {"cell_type": "code", "execution_count": 44, "id": "67455f80-c824-4529-b515-437b119701c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(888, 1)"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["cvatfin.shape"]}, {"cell_type": "code", "execution_count": 45, "id": "dc41c311-5c01-4556-8eca-b38590211628", "metadata": {}, "outputs": [], "source": ["cvatfin.to_csv(\"NG26rightcvat.csv\",index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "3509c90d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "204ee061", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6ea86789", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2f90db59", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a9973fe2-38f9-4102-8131-4cc4d2db4c4e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b16ac600-595c-47fe-ba9a-b7c7d29f7111", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5259e8b4-d426-487d-91fd-92baeb3e1556", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "cc806d4c-c697-48e4-89e3-129958131725", "metadata": {}, "source": ["### Annotations"]}, {"cell_type": "code", "execution_count": 4, "id": "98589797", "metadata": {}, "outputs": [], "source": ["# Read the JSON file into a Pandas DataFrame\n", "with open(coco_file_path, 'r') as file:\n", "    coco_data = json.load(file)\n", "\n", "# Assuming you want to convert the 'images' section to a DataFrame\n", "annotationsdf = pd.DataFrame(coco_data['annotations'])\n"]}, {"cell_type": "code", "execution_count": 11, "id": "09b91e6f", "metadata": {}, "outputs": [], "source": ["homedir = \"/Users/<USER>/Dropbox/Conservation/MWSLab-2023/utils/CVATdata/NG26stratum\""]}, {"cell_type": "code", "execution_count": 6, "id": "9ab9d2b2-f241-4195-a1b0-99e7e1ff6688", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4168.738691</td>\n", "      <td>[1347.29522687095, 653.85975250442, -55.061873...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4831.947119</td>\n", "      <td>[1787.79021803182, 333.812610489098, -82.59281...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4837.868623</td>\n", "      <td>[1720.68355922216, 397.477902180318, -73.98939...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4529.950424</td>\n", "      <td>[1662.1803182086, 376.829699469652, -77.430760...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4820.104111</td>\n", "      <td>[1543.45315262227, 347.578078962876, 75.710076...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation         area  \\\n", "0   1         1            3           []  4168.738691   \n", "1   2         1            3           []  4831.947119   \n", "2   3         1            3           []  4837.868623   \n", "3   4         1            3           []  4529.950424   \n", "4   5         1            3           []  4820.104111   \n", "\n", "                                                bbox  iscrowd  \n", "0  [1347.29522687095, 653.85975250442, -55.061873...        0  \n", "1  [1787.79021803182, 333.812610489098, -82.59281...        0  \n", "2  [1720.68355922216, 397.477902180318, -73.98939...        0  \n", "3  [1662.1803182086, 376.829699469652, -77.430760...        0  \n", "4  [1543.45315262227, 347.578078962876, 75.710076...        0  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 7, "id": "1782ab7f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4993, 7)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["#change bbox dimensions to positive\n", "greater_than_zero_mask = [all(item > 0 for item in row) for row in annotationsdf['bbox']]\n", "annotationsdf[greater_than_zero_mask].shape"]}, {"cell_type": "code", "execution_count": 8, "id": "bff4c1e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2663, 7)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["filterpositive = annotationsdf[greater_than_zero_mask]['bbox'].tolist()\n", "less_than_zero_mask = annotationsdf[~annotationsdf['bbox'].isin(filterpositive)]\n", "less_than_zero_mask.shape"]}, {"cell_type": "code", "execution_count": 9, "id": "924bf19a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4168.738691</td>\n", "      <td>[1347.29522687095, 653.85975250442, -55.061873...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4831.947119</td>\n", "      <td>[1787.79021803182, 333.812610489098, -82.59281...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4837.868623</td>\n", "      <td>[1720.68355922216, 397.477902180318, -73.98939...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4529.950424</td>\n", "      <td>[1662.1803182086, 376.829699469652, -77.430760...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td>2</td>\n", "      <td>12</td>\n", "      <td>[]</td>\n", "      <td>1136.623139</td>\n", "      <td>[1179.18175757867, 114.787604720048, -33.71384...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation         area  \\\n", "0   1         1            3           []  4168.738691   \n", "1   2         1            3           []  4831.947119   \n", "2   3         1            3           []  4837.868623   \n", "3   4         1            3           []  4529.950424   \n", "5   6         2           12           []  1136.623139   \n", "\n", "                                                bbox  iscrowd  \n", "0  [1347.29522687095, 653.85975250442, -55.061873...        0  \n", "1  [1787.79021803182, 333.812610489098, -82.59281...        0  \n", "2  [1720.68355922216, 397.477902180318, -73.98939...        0  \n", "3  [1662.1803182086, 376.829699469652, -77.430760...        0  \n", "5  [1179.18175757867, 114.787604720048, -33.71384...        0  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["less_than_zero_mask.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "c0e5f2b4", "metadata": {}, "outputs": [], "source": ["for item in less_than_zero_mask['bbox']:\n", "    \n", "    if((item[-2]<0) and (item[-1]<0)):\n", "        item[-2] = abs(item[-2])\n", "        item[0]  = item[0] + int(abs(item[-2]))\n", "        item[-1] = abs(item[-1])\n", "        item[1] = item[1] + int(abs(item[-1]))\n", "    \n", "    elif((item[0]<0) and (item[1]<0)):\n", "        item[0] = abs(item[0])\n", "        item[-2] = item[-2] + int(abs(item[0]))\n", "        item[1] = abs(item[1])\n", "        item[-1] = item[-1] + int(abs(item[1]))\n", "        \n", "    elif(item[-2]<0):\n", "        item[-2] = abs(item[-2])\n", "        item[0]  = item[0] + int(abs(item[-2]))\n", "    \n", "    elif(item[0]<0):\n", "        item[0] = abs(item[0])\n", "        item[-2] = item[-2] + int(abs(item[0]))\n", "           \n", "    elif(item[1]<0):\n", "        item[1] = abs(item[1])\n", "        item[-1] = item[-1] + int(abs(item[1]))\n", "            \n", "    elif(item[-1]<0):\n", "        item[-1] = abs(item[-1])\n", "        item[1] = item[1] + int(abs(item[-1]))\n", "    "]}, {"cell_type": "code", "execution_count": 11, "id": "c41629b4", "metadata": {}, "outputs": [], "source": ["annotationsdf.update(less_than_zero_mask)"]}, {"cell_type": "code", "execution_count": 12, "id": "a06717ce", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7609, 7)"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["#check if all are positives after\n", "greater_than_zero_mask = [all(item > 0 for item in row) for row in annotationsdf['bbox']]\n", "annotationsdf[greater_than_zero_mask].shape"]}, {"cell_type": "code", "execution_count": 13, "id": "db6cd851", "metadata": {}, "outputs": [{"data": {"text/plain": ["(7656, 7)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.shape"]}, {"cell_type": "code", "execution_count": 16, "id": "e8df4453", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4168.738691</td>\n", "      <td>[1402.29522687095, 653.85975250442, 55.0618738...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4831.947119</td>\n", "      <td>[1869.79021803182, 333.812610489098, 82.592810...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4837.868623</td>\n", "      <td>[1793.68355922216, 397.477902180318, 73.989393...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4529.950424</td>\n", "      <td>[1739.1803182086, 376.829699469652, 77.4307601...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4820.104111</td>\n", "      <td>[1543.45315262227, 347.578078962876, 75.710076...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  image_id  category_id segmentation         area  \\\n", "0  1.0       1.0          3.0           []  4168.738691   \n", "1  2.0       1.0          3.0           []  4831.947119   \n", "2  3.0       1.0          3.0           []  4837.868623   \n", "3  4.0       1.0          3.0           []  4529.950424   \n", "4  5.0       1.0          3.0           []  4820.104111   \n", "\n", "                                                bbox  iscrowd  \n", "0  [1402.29522687095, 653.85975250442, 55.0618738...      0.0  \n", "1  [1869.79021803182, 333.812610489098, 82.592810...      0.0  \n", "2  [1793.68355922216, 397.477902180318, 73.989393...      0.0  \n", "3  [1739.1803182086, 376.829699469652, 77.4307601...      0.0  \n", "4  [1543.45315262227, 347.578078962876, 75.710076...      0.0  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 32, "id": "68ac5921", "metadata": {}, "outputs": [], "source": ["annotationsdf.to_csv(\"ng26leftpositivebbx.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 5, "id": "ef2cb834-0fb5-4a17-865a-9322536ded26", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4168.738691</td>\n", "      <td>[1402.29522687095, 653.85975250442, 55.0618738...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4831.947119</td>\n", "      <td>[1869.79021803182, 333.812610489098, 82.592810...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4837.868623</td>\n", "      <td>[1793.68355922216, 397.477902180318, 73.989393...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4529.950424</td>\n", "      <td>[1739.1803182086, 376.829699469652, 77.4307601...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>[]</td>\n", "      <td>4820.104111</td>\n", "      <td>[1543.45315262227, 347.578078962876, 75.710076...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    id  image_id  category_id segmentation         area  \\\n", "0  1.0       1.0          3.0           []  4168.738691   \n", "1  2.0       1.0          3.0           []  4831.947119   \n", "2  3.0       1.0          3.0           []  4837.868623   \n", "3  4.0       1.0          3.0           []  4529.950424   \n", "4  5.0       1.0          3.0           []  4820.104111   \n", "\n", "                                                bbox  iscrowd  \n", "0  [1402.29522687095, 653.85975250442, 55.0618738...      0.0  \n", "1  [1869.79021803182, 333.812610489098, 82.592810...      0.0  \n", "2  [1793.68355922216, 397.477902180318, 73.989393...      0.0  \n", "3  [1739.1803182086, 376.829699469652, 77.4307601...      0.0  \n", "4  [1543.45315262227, 347.578078962876, 75.710076...      0.0  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["annotationsdf = pd.read_csv(\"ng26leftpositivebbx.csv\")\n", "annotationsdf.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "665e1fe1-ae74-4788-a99a-608980ee5527", "metadata": {}, "outputs": [], "source": ["annotationsdf['id']=annotationsdf['id'].astype(int)"]}, {"cell_type": "code", "execution_count": 7, "id": "2a3bf0c3-5948-408c-9a7c-c27ee1dcc5f5", "metadata": {}, "outputs": [], "source": ["annotationsdf['image_id']=annotationsdf['image_id'].astype(int)"]}, {"cell_type": "code", "execution_count": 8, "id": "ef5588fe-0c5b-4a39-9d5d-930500f47c81", "metadata": {}, "outputs": [], "source": ["annotationsdf['category_id']=annotationsdf['category_id'].astype(int)"]}, {"cell_type": "code", "execution_count": 9, "id": "bff37894-86ee-4715-a7e5-b63d7a53fa6b", "metadata": {}, "outputs": [], "source": ["annotationsdf['iscrowd']=annotationsdf['iscrowd'].astype(int)"]}, {"cell_type": "code", "execution_count": 12, "id": "e4193fb0-f31a-4583-aee1-5466a6d9ff32", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051007_M0301584.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051031_M0301596.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051033_M0301597.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>7</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051156_M0301638.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   3   7008    4672  KES22_IIM-L_20220924A-051007_M0301584.jpg        1   \n", "1   1   7008    4672  KES22_IIM-L_20220924A-051009_M0301585.jpg        1   \n", "2   4   7008    4672  KES22_IIM-L_20220924A-051031_M0301596.jpg        1   \n", "3   2   7008    4672  KES22_IIM-L_20220924A-051033_M0301597.jpg        1   \n", "4   7   7008    4672  KES22_IIM-L_20220924A-051156_M0301638.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26leftposimg = pd.read_csv(homedir+\"/ng26leftimgpos.csv\")\n", "ng26leftposimg.head()"]}, {"cell_type": "code", "execution_count": 14, "id": "67ab79c1", "metadata": {}, "outputs": [{"data": {"text/plain": ["(431, 6)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26leftposimg.shape"]}, {"cell_type": "code", "execution_count": 15, "id": "3f83efee", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220924A-051005_M0301583.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220924A-051007_M0301584.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220924A-051011_M0301586.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220924A-051029_M0301595.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                c<PERSON><PERSON><PERSON><PERSON>\n", "0  KES22_IIM-L_20220924A-051005_M0301583.jpg\n", "1  KES22_IIM-L_20220924A-051007_M0301584.jpg\n", "2  KES22_IIM-L_20220924A-051009_M0301585.jpg\n", "3  KES22_IIM-L_20220924A-051011_M0301586.jpg\n", "4  KES22_IIM-L_20220924A-051029_M0301595.jpg"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26left1_img = pd.read_csv(homedir+\"/ng26left1cvat.csv\")\n", "ng26left1_img.head()"]}, {"cell_type": "code", "execution_count": 16, "id": "b4fe278b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(135, 6)"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return ng26left1 positive images only \n", "ng26left1_posimg = ng26leftposimg[ng26leftposimg['file_name'].isin(ng26left1_img['cvatfilename'])]\n", "ng26left1_posimg.shape"]}, {"cell_type": "code", "execution_count": 17, "id": "8824033c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051007_M0301584.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051009_M0301585.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051031_M0301596.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051033_M0301597.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>7</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-051156_M0301638.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  width  height                                  file_name  license  \\\n", "0   3   7008    4672  KES22_IIM-L_20220924A-051007_M0301584.jpg        1   \n", "1   1   7008    4672  KES22_IIM-L_20220924A-051009_M0301585.jpg        1   \n", "2   4   7008    4672  KES22_IIM-L_20220924A-051031_M0301596.jpg        1   \n", "3   2   7008    4672  KES22_IIM-L_20220924A-051033_M0301597.jpg        1   \n", "4   7   7008    4672  KES22_IIM-L_20220924A-051156_M0301638.jpg        1   \n", "\n", "  date_captured  \n", "0    2022-09-23  \n", "1    2022-09-23  \n", "2    2022-09-23  \n", "3    2022-09-23  \n", "4    2022-09-23  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26left1_posimg.head()"]}, {"cell_type": "code", "execution_count": 18, "id": "24eb1c57", "metadata": {}, "outputs": [{"data": {"text/plain": ["(916, 7)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return annotations \n", "\n", "ng26left1_annot = annotationsdf[annotationsdf['image_id'].isin(ng26left1_posimg['id'])]\n", "ng26left1_annot.shape"]}, {"cell_type": "code", "execution_count": 19, "id": "ada5501d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4168.738691</td>\n", "      <td>[1402.29522687095, 653.85975250442, 55.0618738...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4831.947119</td>\n", "      <td>[1869.79021803182, 333.812610489098, 82.592810...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4837.868623</td>\n", "      <td>[1793.68355922216, 397.477902180318, 73.989393...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4529.950424</td>\n", "      <td>[1739.1803182086, 376.829699469652, 77.4307601...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4820.104111</td>\n", "      <td>[1543.45315262227, 347.578078962876, 75.710076...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation         area  \\\n", "0   1         1            3           []  4168.738691   \n", "1   2         1            3           []  4831.947119   \n", "2   3         1            3           []  4837.868623   \n", "3   4         1            3           []  4529.950424   \n", "4   5         1            3           []  4820.104111   \n", "\n", "                                                bbox  iscrowd  \n", "0  [1402.29522687095, 653.85975250442, 55.0618738...        0  \n", "1  [1869.79021803182, 333.812610489098, 82.592810...        0  \n", "2  [1793.68355922216, 397.477902180318, 73.989393...        0  \n", "3  [1739.1803182086, 376.829699469652, 77.4307601...        0  \n", "4  [1543.45315262227, 347.578078962876, 75.710076...        0  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26left1_annot.head()"]}, {"cell_type": "code", "execution_count": 78, "id": "b295d634", "metadata": {}, "outputs": [], "source": ["ng26left1_annotdf2[\"id\"]= ng26left1_annotdf2[\"id\"].astype(int)"]}, {"cell_type": "code", "execution_count": 82, "id": "e5d0681a", "metadata": {}, "outputs": [], "source": ["ng26left1_annotdf2[\"category_id\"]= ng26left1_annotdf2[\"category_id\"].astype(int)"]}, {"cell_type": "code", "execution_count": 86, "id": "64def512", "metadata": {}, "outputs": [], "source": ["ng26left1_annotdf2[\"iscrowd\"]= ng26left1_annotdf2[\"iscrowd\"].astype(int)"]}, {"cell_type": "code", "execution_count": 87, "id": "cf250fa0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4168.738691</td>\n", "      <td>[1402.29522687095, 653.85975250442, 55.0618738...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4831.947119</td>\n", "      <td>[1869.79021803182, 333.812610489098, 82.592810...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4837.868623</td>\n", "      <td>[1793.68355922216, 397.477902180318, 73.989393...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4529.950424</td>\n", "      <td>[1739.1803182086, 376.829699469652, 77.4307601...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>[]</td>\n", "      <td>4820.104111</td>\n", "      <td>[1543.45315262227, 347.578078962876, 75.710076...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  image_id  category_id segmentation         area  \\\n", "0   1         1            3           []  4168.738691   \n", "1   2         1            3           []  4831.947119   \n", "2   3         1            3           []  4837.868623   \n", "3   4         1            3           []  4529.950424   \n", "4   5         1            3           []  4820.104111   \n", "\n", "                                                bbox  iscrowd  \n", "0  [1402.29522687095, 653.85975250442, 55.0618738...        0  \n", "1  [1869.79021803182, 333.812610489098, 82.592810...        0  \n", "2  [1793.68355922216, 397.477902180318, 73.989393...        0  \n", "3  [1739.1803182086, 376.829699469652, 77.4307601...        0  \n", "4  [1543.45315262227, 347.578078962876, 75.710076...        0  "]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26left1_annotdf2.head()"]}, {"cell_type": "code", "execution_count": 21, "id": "f728a3fe", "metadata": {}, "outputs": [], "source": ["ng26left1_annot.to_csv(\"NG26left1annotations.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "431bd5e3-19b8-4473-b5f2-07460645bd12", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "368d46a6-a4bf-443e-b5ee-4e21fd1d5a89", "metadata": {}, "source": ["### Annotations to COCO file"]}, {"cell_type": "code", "execution_count": null, "id": "cc08233f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 22, "id": "bc5b969d-9351-4fad-83cb-27aec08851b4", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "coco_dict['info']={\"description\": \"NG26 Stratum annotations\", \"version\": \"1.0\", \"year\": 2023, \"contributor\": \"MWS Lab Arusha\", \"date_created\": \"2023-09-24\"}\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = ng26left1_posimg.to_dict(orient='records')\n", "coco_dict['annotations'] = ng26left1_annot.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'ng26left1annotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": 32, "id": "25d27e8d-9b16-49d6-80ad-d0457b4526a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'id': 1,\n", "  'name': 'KAZA Elephant Survey',\n", "  'url': 'https://www.kavangozambezi.org/kaza-elephant-survey/'}]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["coco_data[\"licenses\"]"]}, {"cell_type": "code", "execution_count": 30, "id": "4b3bf7a4", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'description': 'NG26 Stratum annotations',\n", " 'version': '1.0',\n", " 'year': 2023,\n", " 'contributor': 'MWS Lab Arusha',\n", " 'date_created': '2023-09-24'}"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["coco_dict[\"info\"]"]}, {"cell_type": "code", "execution_count": null, "id": "b8c88241", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "8384a401-2855-4d3b-ae8c-3c2c9289b4b0", "metadata": {}, "source": ["### NG26 Left2"]}, {"cell_type": "code", "execution_count": 38, "id": "58c60c7e-3b7f-4961-9c65-0d0ad1eeabd4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220924A-064034_M0304288.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220924A-064036_M0304289.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220924A-064038_M0304290.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220924A-064040_M0304291.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220924A-064042_M0304292.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                c<PERSON><PERSON><PERSON><PERSON>\n", "0  KES22_IIM-L_20220924A-064034_M0304288.jpg\n", "1  KES22_IIM-L_20220924A-064036_M0304289.jpg\n", "2  KES22_IIM-L_20220924A-064038_M0304290.jpg\n", "3  KES22_IIM-L_20220924A-064040_M0304291.jpg\n", "4  KES22_IIM-L_20220924A-064042_M0304292.jpg"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26left2img = pd.read_csv(\"ng26left2cvat.csv\")\n", "ng26left2img.head()"]}, {"cell_type": "code", "execution_count": 39, "id": "450a1a0f-83ad-4493-a907-85053e854d43", "metadata": {}, "outputs": [{"data": {"text/plain": ["(147, 6)"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return ng26left1 positive images only \n", "ng26left2_posimg = ng26leftposimg[ng26leftposimg['file_name'].isin(ng26left2img['cvatfilename'])]\n", "ng26left2_posimg.shape"]}, {"cell_type": "code", "execution_count": 40, "id": "cb90b49e-245c-4f62-9ad4-c0d307da3fab", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>width</th>\n", "      <th>height</th>\n", "      <th>file_name</th>\n", "      <th>license</th>\n", "      <th>date_captured</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>135</th>\n", "      <td>513</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-064036_M0304289.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>136</th>\n", "      <td>512</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-064038_M0304290.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>510</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-064040_M0304291.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>138</th>\n", "      <td>509</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-064218_M0304340.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>139</th>\n", "      <td>511</td>\n", "      <td>7008</td>\n", "      <td>4672</td>\n", "      <td>KES22_IIM-L_20220924A-064220_M0304341.jpg</td>\n", "      <td>1</td>\n", "      <td>2022-09-23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  width  height                                  file_name  license  \\\n", "135  513   7008    4672  KES22_IIM-L_20220924A-064036_M0304289.jpg        1   \n", "136  512   7008    4672  KES22_IIM-L_20220924A-064038_M0304290.jpg        1   \n", "137  510   7008    4672  KES22_IIM-L_20220924A-064040_M0304291.jpg        1   \n", "138  509   7008    4672  KES22_IIM-L_20220924A-064218_M0304340.jpg        1   \n", "139  511   7008    4672  KES22_IIM-L_20220924A-064220_M0304341.jpg        1   \n", "\n", "    date_captured  \n", "135    2022-09-23  \n", "136    2022-09-23  \n", "137    2022-09-23  \n", "138    2022-09-23  \n", "139    2022-09-23  "]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26left2_posimg.head()"]}, {"cell_type": "code", "execution_count": 41, "id": "ab1b51de-2b2c-4dc3-8e98-6afaa164e9d8", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1346, 7)"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return annotations \n", "\n", "ng26left2_annot = annotationsdf[annotationsdf['image_id'].isin(ng26left2_posimg['id'])]\n", "ng26left2_annot.shape"]}, {"cell_type": "code", "execution_count": 42, "id": "023f5faa-e92f-4a26-8b41-9d1258b66a6e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>827</th>\n", "      <td>828</td>\n", "      <td>124</td>\n", "      <td>27</td>\n", "      <td>[]</td>\n", "      <td>7098.062255</td>\n", "      <td>[889.061736010779, 3007.64597977306, 92.433564...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>828</th>\n", "      <td>829</td>\n", "      <td>124</td>\n", "      <td>27</td>\n", "      <td>[]</td>\n", "      <td>6774.503862</td>\n", "      <td>[295.076378630217, 3210.99982143147, 95.277674...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>829</th>\n", "      <td>830</td>\n", "      <td>124</td>\n", "      <td>27</td>\n", "      <td>[]</td>\n", "      <td>3963.590319</td>\n", "      <td>[521.183097676986, 3269.30406973913, 69.680687...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>830</th>\n", "      <td>831</td>\n", "      <td>124</td>\n", "      <td>27</td>\n", "      <td>[]</td>\n", "      <td>4141.547436</td>\n", "      <td>[717.415155598123, 3348.93914059837, 91.011509...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>831</th>\n", "      <td>832</td>\n", "      <td>124</td>\n", "      <td>27</td>\n", "      <td>[]</td>\n", "      <td>7077.839856</td>\n", "      <td>[859.088310254704, 3421.46393727375, 99.543838...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      id  image_id  category_id segmentation         area  \\\n", "827  828       124           27           []  7098.062255   \n", "828  829       124           27           []  6774.503862   \n", "829  830       124           27           []  3963.590319   \n", "830  831       124           27           []  4141.547436   \n", "831  832       124           27           []  7077.839856   \n", "\n", "                                                  bbox  iscrowd  \n", "827  [889.061736010779, 3007.64597977306, 92.433564...        0  \n", "828  [295.076378630217, 3210.99982143147, 95.277674...        0  \n", "829  [521.183097676986, 3269.30406973913, 69.680687...        0  \n", "830  [717.415155598123, 3348.93914059837, 91.011509...        0  \n", "831  [859.088310254704, 3421.46393727375, 99.543838...        0  "]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26left2_annot.head()"]}, {"cell_type": "code", "execution_count": 43, "id": "2c79e39b", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "coco_dict['info']={\"description\": \"NG26 Stratum annotations\", \"version\": \"1.0\", \"year\": 2023, \"contributor\": \"MWS Lab Arusha\", \"date_created\": \"2023-09-24\"}\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = ng26left2_posimg.to_dict(orient='records')\n", "coco_dict['annotations'] = ng26left2_annot.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'ng26left2annotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "markdown", "id": "cb4888bd-d8f6-422f-ae74-f68d3cb7f971", "metadata": {}, "source": ["### NG26 Left3"]}, {"cell_type": "code", "execution_count": 32, "id": "09dd22c5-940a-4c9a-a843-64d5aaf190cb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cvat<PERSON><PERSON>me</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>KES22_IIM-L_20220924A-075638_M0306562.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>KES22_IIM-L_20220924A-075640_M0306563.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>KES22_IIM-L_20220924A-075642_M0306564.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>KES22_IIM-L_20220924A-075644_M0306565.jpg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>KES22_IIM-L_20220924A-075646_M0306566.jpg</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                c<PERSON><PERSON><PERSON><PERSON>\n", "0  KES22_IIM-L_20220924A-075638_M0306562.jpg\n", "1  KES22_IIM-L_20220924A-075640_M0306563.jpg\n", "2  KES22_IIM-L_20220924A-075642_M0306564.jpg\n", "3  KES22_IIM-L_20220924A-075644_M0306565.jpg\n", "4  KES22_IIM-L_20220924A-075646_M0306566.jpg"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26left3img = pd.read_csv(\"ng26left3cvat.csv\")\n", "ng26left3img.head()"]}, {"cell_type": "code", "execution_count": 33, "id": "af16e5a8-e918-4d55-8fe1-8ea01ebfeea7", "metadata": {}, "outputs": [{"data": {"text/plain": ["(149, 6)"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return ng26left1 positive images only \n", "ng26left3_posimg = ng26leftposimg[ng26leftposimg['file_name'].isin(ng26left3img['cvatfilename'])]\n", "ng26left3_posimg.shape"]}, {"cell_type": "code", "execution_count": 34, "id": "4852c36f-635e-4572-ac16-d180cb9c67fd", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1881, 7)"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["# Compare image 'ID' columns and return annotations \n", "\n", "ng26left3_annot = annotationsdf[annotationsdf['image_id'].isin(ng26left3_posimg['id'])]\n", "ng26left3_annot.shape"]}, {"cell_type": "code", "execution_count": 35, "id": "0a43e1fe-ecbe-4a73-8d7e-cb6e8f83a067", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>image_id</th>\n", "      <th>category_id</th>\n", "      <th>segmentation</th>\n", "      <th>area</th>\n", "      <th>bbox</th>\n", "      <th>iscrowd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1799</th>\n", "      <td>1800</td>\n", "      <td>233</td>\n", "      <td>28</td>\n", "      <td>[]</td>\n", "      <td>3387.100186</td>\n", "      <td>[1826.29051266942, 460.918341362975, 44.737772...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1800</th>\n", "      <td>1801</td>\n", "      <td>233</td>\n", "      <td>28</td>\n", "      <td>[]</td>\n", "      <td>5968.875853</td>\n", "      <td>[2373.46788450206, 1542.60257790632, 96.358279...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1801</th>\n", "      <td>1802</td>\n", "      <td>233</td>\n", "      <td>28</td>\n", "      <td>[]</td>\n", "      <td>1598.806032</td>\n", "      <td>[3001.10371243371, 992.296700058927, 34.413671...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1802</th>\n", "      <td>1803</td>\n", "      <td>233</td>\n", "      <td>28</td>\n", "      <td>[]</td>\n", "      <td>1039.223921</td>\n", "      <td>[4354.23128705989, 126.480034922036, 22.368886...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1803</th>\n", "      <td>1804</td>\n", "      <td>233</td>\n", "      <td>28</td>\n", "      <td>[]</td>\n", "      <td>1409.317910</td>\n", "      <td>[4263.15232418422, 129.92140204048, 29.2516205...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        id  image_id  category_id segmentation         area  \\\n", "1799  1800       233           28           []  3387.100186   \n", "1800  1801       233           28           []  5968.875853   \n", "1801  1802       233           28           []  1598.806032   \n", "1802  1803       233           28           []  1039.223921   \n", "1803  1804       233           28           []  1409.317910   \n", "\n", "                                                   bbox  iscrowd  \n", "1799  [1826.29051266942, 460.918341362975, 44.737772...        0  \n", "1800  [2373.46788450206, 1542.60257790632, 96.358279...        0  \n", "1801  [3001.10371243371, 992.296700058927, 34.413671...        0  \n", "1802  [4354.23128705989, 126.480034922036, 22.368886...        0  \n", "1803  [4263.15232418422, 129.92140204048, 29.2516205...        0  "]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["ng26left3_annot.head()"]}, {"cell_type": "code", "execution_count": null, "id": "3cf43b41-70ba-4fd8-9009-01217fc9e8f8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 37, "id": "612a93ba-599b-49ef-b944-32f7ee92da71", "metadata": {}, "outputs": [], "source": ["# Create the COCO dictionary\n", "coco_dict = {\n", "    'info': coco_data[\"info\"],\n", "    'licenses': coco_data[\"licenses\"],\n", "    'categories': coco_data[\"categories\"],\n", "    'images': [],\n", "    'annotations': []\n", "    \n", "}\n", "coco_dict['info']={\"description\": \"NG26 Stratum annotations\", \"version\": \"1.0\", \"year\": 2023, \"contributor\": \"MWS Lab Arusha\", \"date_created\": \"2023-09-24\"}\n", "# Assuming you have 'images' and 'annotations' DataFrames\n", "coco_dict['images'] = ng26left3_posimg.to_dict(orient='records')\n", "coco_dict['annotations'] = ng26left3_annot.to_dict(orient='records')\n", "\n", "# Save as JSO<PERSON>\n", "output_path = 'ng26left3annotations_coco_file.json'\n", "with open(output_path, 'w') as f:\n", "    json.dump(coco_dict, f, indent=2)"]}, {"cell_type": "code", "execution_count": null, "id": "d50fd600", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "254708b5-3db5-489a-bc35-918c449acee8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "16dd420b-4f4d-42de-9aa8-1917a1d7a760", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "043cfaf2-580a-4a72-9871-0e8bbad31a7f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6bebbff8-ee58-423d-b3bc-98f61a1ed892", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8d1c23e1-3e5b-4094-b896-17dc2f717478", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "135dfc6b-0c73-4c08-bae1-aa388ffc7fbc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c6d23d56-9cae-4f9b-845c-cc29ff54ecf2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0e0ddd1d-fb7e-4a44-bbad-da087e7bf8ec", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}